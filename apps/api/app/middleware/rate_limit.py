import time
from collections import defaultdict, deque
from typing import Dict, Deque
from fastapi import Request
from fastapi.responses import JSONResponse

from app.models.schemas.auth_schemas import RateLimitErrorResponse


class RateLimitStore:
    """Simple in-memory rate limiting store."""
    
    def __init__(self):
        self.requests: Dict[str, Deque[float]] = defaultdict(deque)
    
    def is_rate_limited(self, key: str, limit: int, window_hours: int) -> bool:
        """Check if key is rate limited."""
        now = time.time()
        window_seconds = window_hours * 3600
        cutoff = now - window_seconds
        
        # Clean old entries
        while self.requests[key] and self.requests[key][0] < cutoff:
            self.requests[key].popleft()
        
        # Check if limit exceeded
        if len(self.requests[key]) >= limit:
            return True
        
        # Record this request
        self.requests[key].append(now)
        return False


# Global rate limit store (in production, use Redis)
rate_limit_store = RateLimitStore()


async def rate_limit_forgot_password(request: Request, call_next):
    """Rate limiting middleware for forgot password endpoint."""
    
    if request.url.path == "/api/v1/auth/forgot-password" and request.method == "POST":
        # Get client IP
        client_ip = request.client.host
        if "x-forwarded-for" in request.headers:
            client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
        elif "x-real-ip" in request.headers:
            client_ip = request.headers["x-real-ip"]
        
        # Check rate limit (3 requests per hour)
        if rate_limit_store.is_rate_limited(client_ip, limit=3, window_hours=1):
            error_response = RateLimitErrorResponse(
                message="Too many password reset requests. Please try again later.",
                retry_after=3600  # 1 hour in seconds
            )
            return JSONResponse(
                status_code=429,
                content=error_response.model_dump()
            )
    
    response = await call_next(request)
    return response