import logging
from datetime import datetime, timedelta, timezone
from typing import Optional
from pathlib import Path
from fastapi import BackgroundTasks

from app.core.config import settings
from app.core.security import generate_reset_token
from app.repositories.user_repository import UserRepository
from app.repositories.password_reset_repository import PasswordResetTokenRepository
from app.services.email_service import EmailService
from app.models.schemas.auth_schemas import PasswordResetError


logger = logging.getLogger(__name__)


class PasswordResetService:
    """Service for handling password reset operations."""
    
    def __init__(
        self,
        email_service: EmailService,
        user_repo: UserRepository,
        reset_repo: PasswordResetTokenRepository,
    ):
        self.email_service = email_service
        self.user_repo = user_repo
        self.reset_repo = reset_repo
        # Set up frontend URL with fallback (following existing pattern)
        self.frontend_url = getattr(settings, "FRONTEND_URL", "http://localhost:3000")
        # Set up template directory path (following existing pattern)
        self.template_dir = Path(__file__).parent.parent / "templates" / "email"

    async def initiate_password_reset(
        self, 
        email: str, 
        ip_address: str, 
        user_agent: str,
        background_tasks: BackgroundTasks
    ) -> bool:
        """
        Initiate password reset process.
        
        Always returns immediately with True to prevent user enumeration.
        All processing done in background tasks for consistent timing.
        """
        # Queue ALL processing for background execution
        background_tasks.add_task(
            self._process_password_reset,
            email=email.lower().strip(),
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Return immediately - same timing for all requests
        return True

    async def _process_password_reset(self, email: str, ip_address: str, user_agent: str):
        """Background task that handles all password reset processing."""
        try:
            # Look up user (case-insensitive)
            user = self.user_repo.get_by_email(email)
            
            if user:
                # Generate secure token
                plain_token, token_hash = generate_reset_token()
                
                # Token expires in 1 hour  
                expires_at = datetime.now(timezone.utc) + timedelta(hours=1)
                
                # Invalidate any existing tokens for this user
                self.reset_repo.invalidate_user_tokens(user.id)
                
                # Create new token
                reset_token = self.reset_repo.create_token(
                    user_id=user.id,
                    token_hash=token_hash,
                    expires_at=expires_at,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                # Send reset email
                reset_url = f"{self.frontend_url}/reset-password?token={plain_token}"
                await self._send_reset_email(
                    user_email=user.email,
                    user_name=user.first_name or "User", 
                    reset_url=reset_url
                )
                
                # Simple audit logging (no email, just outcome)
                logger.info(f"Password reset token created for user {user.id} from IP {ip_address}")
            else:
                # Simple audit logging for non-existent user
                logger.info(f"Password reset requested for non-existent user from IP {ip_address}")
            
        except Exception as e:
            logger.error(f"Error during password reset processing from IP {ip_address}: {str(e)}")

    def _load_template(self, template_name: str, **kwargs) -> tuple[str, str]:
        """
        Load email template and replace variables.

        Args:
            template_name: Name of the template file (without extension)
            **kwargs: Variables to replace in the template

        Returns:
            tuple: (html_content, text_content)

        Raises:
            PasswordResetError: If template files not found
        """
        html_path = self.template_dir / f"{template_name}.html"
        text_path = self.template_dir / f"{template_name}.txt"

        # Load HTML template
        if not html_path.exists():
            raise PasswordResetError(f"HTML template not found: {html_path}")

        with open(html_path, "r") as f:
            html_content = f.read()
            # Replace template variables
            for key, value in kwargs.items():
                html_content = html_content.replace(f"{{{key}}}", str(value))

        # Load text template
        if not text_path.exists():
            raise PasswordResetError(f"Text template not found: {text_path}")

        with open(text_path, "r") as f:
            text_content = f.read()
            # Replace template variables
            for key, value in kwargs.items():
                text_content = text_content.replace(f"{{{key}}}", str(value))

        return html_content, text_content

    async def _send_reset_email(self, user_email: str, user_name: str, reset_url: str):
        """Send password reset email."""
        try:
            # Load template with variables
            html_body, text_body = self._load_template(
                "password_reset",
                first_name=user_name,
                reset_url=reset_url,
                expiry_hours=1
            )
            
            subject = "Password Reset Request - FinPro"
            
            # Send email
            await self.email_service.send_email(
                to_email=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body
            )
            
        except Exception as e:
            logger.error(f"Failed to send password reset email to {user_email}: {str(e)}")
            raise