import re
import secrets
import hashlib
from passlib.context import CryptContext

from app.models.database.auth import PasswordHashAlgorithm


class PasswordValidationError(Exception):
    """Base exception for password validation errors."""

    pass


class PasswordComplexityError(PasswordValidationError):
    """Raised when password doesn't meet complexity requirements."""

    pass


class PasswordValidationService:
    """Service for password validation, hashing, and complexity checks."""

    # Bcrypt cost factor (algorithm_version)
    BCRYPT_ROUNDS = 12

    # Password complexity requirements
    MIN_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGIT = True
    REQUIRE_SPECIAL = True
    SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    def __init__(self):
        # Initialize password context with bcrypt
        self.pwd_context = CryptContext(
            schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=self.BCRYPT_ROUNDS
        )

    def validate_complexity(self, password: str) -> bool:
        """
        Validate password meets complexity requirements.

        Args:
            password: Plain text password to validate

        Returns:
            bool: True if password meets all requirements

        Raises:
            PasswordComplexityError: If password doesn't meet requirements
        """
        errors = []

        # Check minimum length
        if len(password) < self.MIN_LENGTH:
            errors.append(
                f"Password must be at least {self.MIN_LENGTH} characters long"
            )

        # Check uppercase requirement
        if self.REQUIRE_UPPERCASE and not re.search(r"[A-Z]", password):
            errors.append("Password must contain at least one uppercase letter")

        # Check lowercase requirement
        if self.REQUIRE_LOWERCASE and not re.search(r"[a-z]", password):
            errors.append("Password must contain at least one lowercase letter")

        # Check digit requirement
        if self.REQUIRE_DIGIT and not re.search(r"\d", password):
            errors.append("Password must contain at least one number")

        # Check special character requirement
        if self.REQUIRE_SPECIAL and not any(
            char in self.SPECIAL_CHARS for char in password
        ):
            errors.append(
                f"Password must contain at least one special character "
                f"({self.SPECIAL_CHARS})"
            )

        if errors:
            raise PasswordComplexityError("; ".join(errors))

        return True

    def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt with configured cost factor.

        Args:
            password: Plain text password to hash

        Returns:
            str: Hashed password
        """
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a plain password against a hashed password.

        Args:
            plain_password: Plain text password to verify
            hashed_password: Hashed password to verify against

        Returns:
            bool: True if password matches, False otherwise
        """
        try:
            return self.pwd_context.verify(plain_password, hashed_password)
        except Exception:
            return False

    def needs_rehash(self, hashed_password: str) -> bool:
        """
        Check if a hashed password needs to be rehashed (e.g., if cost factor changed).

        Args:
            hashed_password: The hashed password to check

        Returns:
            bool: True if password should be rehashed
        """
        return self.pwd_context.needs_update(hashed_password)

    def hash_token(self, token: str) -> str:
        """
        Hash a verification token using SHA256.
        Use SHA256 for tokens since we need to look them up by hash.

        Args:
            token: Plain text token to hash

        Returns:
            str: Hashed token
        """
        return hashlib.sha256(token.encode()).hexdigest()

    def generate_secure_token(self, length: int = 32) -> str:
        """
        Generate a cryptographically secure random token.

        Args:
            length: Length of the token in bytes (default 32)

        Returns:
            str: URL-safe token
        """
        return secrets.token_urlsafe(length)

    def validate_password_strength(self, password: str) -> dict:
        """
        Get detailed password strength analysis.

        Args:
            password: Password to analyze

        Returns:
            dict: Strength analysis with score and details
        """
        score = 0
        max_score = 5
        details = {
            "length": len(password),
            "has_uppercase": bool(re.search(r"[A-Z]", password)),
            "has_lowercase": bool(re.search(r"[a-z]", password)),
            "has_digit": bool(re.search(r"\d", password)),
            "has_special": any(char in self.SPECIAL_CHARS for char in password),
        }

        # Score based on length
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1

        # Score based on character types
        if details["has_uppercase"]:
            score += 0.75
        if details["has_lowercase"]:
            score += 0.75
        if details["has_digit"]:
            score += 0.75
        if details["has_special"]:
            score += 0.75

        strength = "weak"
        if score >= 4:
            strength = "strong"
        elif score >= 3:
            strength = "medium"

        return {
            "score": min(score, max_score),
            "max_score": max_score,
            "strength": strength,
            "details": details,
        }

    def get_algorithm_info(self) -> dict:
        """Get information about the current hashing algorithm configuration."""
        return {
            "algorithm": PasswordHashAlgorithm.BCRYPT.value,
            "algorithm_version": self.BCRYPT_ROUNDS,
            "min_length": self.MIN_LENGTH,
            "require_uppercase": self.REQUIRE_UPPERCASE,
            "require_lowercase": self.REQUIRE_LOWERCASE,
            "require_digit": self.REQUIRE_DIGIT,
            "require_special": self.REQUIRE_SPECIAL,
        }
