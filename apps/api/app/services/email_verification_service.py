from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path
import logging

from app.models.database.user import User
from app.repositories.auth_repository import EmailVerificationTokenRepository
from app.repositories.user_repository import UserRepository
from app.services.email_service import EmailService
from app.services.password_validation_service import PasswordValidationService
from app.models.database.auth import VerificationType
from app.core.config import settings


logger = logging.getLogger(__name__)


class EmailVerificationError(Exception):
    """Base exception for email verification errors."""

    pass


class EmailVerificationService:
    """Service for handling email verification logic."""

    def __init__(
        self,
        email_service: EmailService,
        password_service: PasswordValidationService,
        token_repo: EmailVerificationTokenRepository,
        user_repo: UserRepository,
    ):
        self.email_service = email_service
        self.password_service = password_service
        self.token_repo = token_repo
        self.user_repo = user_repo
        self.frontend_url = getattr(settings, "FRONTEND_URL", "http://localhost:3000")
        self.token_expiry_hours = 24
        # Set up template directory path
        self.template_dir = Path(__file__).parent.parent / "templates" / "email"

    def _load_template(self, template_name: str, **kwargs) -> tuple[str, str]:
        """
        Load email template and replace variables.

        Args:
            template_name: Name of the template file (without extension)
            **kwargs: Variables to replace in the template

        Returns:
            tuple: (html_content, text_content)

        Raises:
            EmailVerificationError: If template files not found
        """
        html_path = self.template_dir / f"{template_name}.html"
        text_path = self.template_dir / f"{template_name}.txt"

        # Load HTML template
        if not html_path.exists():
            raise EmailVerificationError(f"HTML template not found: {html_path}")

        with open(html_path, "r") as f:
            html_content = f.read()
            # Replace template variables
            for key, value in kwargs.items():
                html_content = html_content.replace(f"{{{{{key}}}}}", str(value))

        # Load text template
        if not text_path.exists():
            raise EmailVerificationError(f"Text template not found: {text_path}")

        with open(text_path, "r") as f:
            text_content = f.read()
            # Replace template variables
            for key, value in kwargs.items():
                text_content = text_content.replace(f"{{{{{key}}}}}", str(value))

        return html_content, text_content

    async def send_verification_email(
        self, user: User, verification_token: str
    ) -> bool:
        """
        Send email verification to user using template.

        Args:
            user: User object to send email to
            verification_token: Plain text verification token

        Returns:
            bool: True if email sent successfully
        """
        verification_url = (
            f"{self.frontend_url}/auth/verify-email?token={verification_token}"
        )

        # Load template with variables
        html_body, text_body = self._load_template(
            "email_verification",
            first_name=user.first_name or "there",
            verification_url=verification_url,
            expiry_hours=self.token_expiry_hours,
            frontend_url=self.frontend_url,
        )

        subject = "Verify Your FinPro Account"

        return await self.email_service.send_email(
            to_email=user.email,
            subject=subject,
            html_body=html_body,
            text_body=text_body,
        )

    def create_verification_token(self, user_id: str) -> tuple[str, datetime]:
        """
        Create a new verification token for a user.

        Args:
            user_id: User ID to create token for

        Returns:
            tuple: (plain_token, expiry_datetime)
        """
        # Generate secure token
        plain_token = self.password_service.generate_secure_token()

        # Hash token for storage
        token_hash = self.password_service.hash_token(plain_token)

        # Set expiry
        expires_at = datetime.utcnow() + timedelta(hours=self.token_expiry_hours)

        # Create or update token in repository
        self.token_repo.create_or_update_token(
            user_id=user_id,
            token_hash=token_hash,
            expires_at=expires_at,
            verification_type=VerificationType.REGISTRATION,
        )

        return plain_token, expires_at

    def verify_email_token(self, token: str) -> bool:
        """
        Verify an email verification token and activate the user.

        Args:
            token: Plain text verification token

        Returns:
            bool: True if verification successful, False otherwise
        """
        try:
            # Hash the token to find it in database
            token_hash = self.password_service.hash_token(token)

            # Get valid token
            verification = self.token_repo.get_valid_token(token_hash)

            if not verification:
                return False

            # Mark token as verified
            self.token_repo.mark_token_as_verified(verification)

            # Get and activate user
            user = self.user_repo.get_by_id(verification.user_id)
            if user:
                self.user_repo.activate_user(user)

                # Send welcome email (non-critical, don't fail if it doesn't send)
                try:
                    import asyncio

                    asyncio.create_task(self._send_welcome_email(user))
                except Exception as e:
                    logger.warning(
                        f"Failed to send welcome email to {user.email}: {str(e)}"
                    )

            return True

        except Exception as e:
            logger.error(f"Error verifying email token: {str(e)}")
            return False

    async def resend_verification_email(self, email: str) -> bool:
        """
        Resend verification email to a user.

        Args:
            email: Email address of the user

        Returns:
            bool: True if email sent successfully
        """
        # Get user
        user = self.user_repo.get_by_email(email)
        if not user:
            raise EmailVerificationError("User not found")

        if user.email_verified:
            raise EmailVerificationError("Email already verified")

        # Generate new token
        plain_token, _ = self.create_verification_token(user.id)

        # Send email
        return await self.send_verification_email(user, plain_token)

    async def _send_welcome_email(self, user: User) -> bool:
        """
        Send welcome email after verification using template.

        Args:
            user: User to send welcome email to

        Returns:
            bool: True if email sent successfully
        """
        try:
            # Load template with variables
            html_body, text_body = self._load_template(
                "welcome",
                first_name=user.first_name or "there",
                login_url=f"{self.frontend_url}/login",
                frontend_url=self.frontend_url,
            )

            subject = "Welcome to FinPro - Account Activated!"

            return await self.email_service.send_email(
                to_email=user.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
            )

        except Exception as e:
            logger.warning(f"Failed to send welcome email to {user.email}: {str(e)}")
            return False
