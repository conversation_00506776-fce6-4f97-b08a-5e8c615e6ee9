import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import Optional, List
import logging

from app.core.config import settings


logger = logging.getLogger(__name__)


class EmailServiceError(Exception):
    """Base exception for email service errors."""

    pass


class EmailService:
    """Service for sending emails via SMTP."""

    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT or 587
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_tls = settings.SMTP_TLS
        self.from_email = settings.EMAILS_FROM_EMAIL or "<EMAIL>"
        self.from_name = settings.EMAILS_FROM_NAME or "FinPro"

    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_body: str,
        text_body: Optional[str] = None,
        cc: Optional[List[str]] = None,
        bcc: Optional[List[str]] = None,
    ) -> bool:
        """
        Send an email via SMTP.

        Args:
            to_email: Recipient email address
            subject: Email subject
            html_body: HTML email content
            text_body: Plain text email content (optional)
            cc: List of CC recipients (optional)
            bcc: List of BCC recipients (optional)

        Returns:
            bool: True if sent successfully

        Raises:
            EmailServiceError: If email sending fails
        """
        # If SMTP is not configured, log and return (for development)
        if not self.smtp_host or not self.smtp_user or not self.smtp_password:
            logger.warning(
                f"SMTP not configured. Would have sent email to {to_email} "
                f"with subject: {subject}"
            )
            logger.debug(f"Email body: {text_body or 'HTML only'}")
            return True

        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email

            if cc:
                message["Cc"] = ", ".join(cc)
            if bcc:
                message["Bcc"] = ", ".join(bcc)

            # Add plain text part if provided
            if text_body:
                text_part = MIMEText(text_body, "plain")
                message.attach(text_part)

            # Add HTML part
            html_part = MIMEText(html_body, "html")
            message.attach(html_part)

            # Prepare recipient list
            recipients = [to_email]
            if cc:
                recipients.extend(cc)
            if bcc:
                recipients.extend(bcc)

            # Send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.smtp_tls:
                    server.starttls()
                server.login(self.smtp_user, self.smtp_password)
                server.send_message(message, to_addrs=recipients)

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            raise EmailServiceError(f"Failed to send email: {str(e)}")

    async def send_bulk_emails(
        self,
        recipients: List[str],
        subject: str,
        html_body: str,
        text_body: Optional[str] = None,
    ) -> dict:
        """
        Send bulk emails to multiple recipients.

        Args:
            recipients: List of recipient email addresses
            subject: Email subject
            html_body: HTML email content
            text_body: Plain text email content (optional)

        Returns:
            dict: Results with successful and failed sends
        """
        results = {"successful": [], "failed": []}

        for recipient in recipients:
            try:
                await self.send_email(
                    to_email=recipient,
                    subject=subject,
                    html_body=html_body,
                    text_body=text_body,
                )
                results["successful"].append(recipient)
            except Exception as e:
                logger.error(f"Failed to send email to {recipient}: {str(e)}")
                results["failed"].append({"email": recipient, "error": str(e)})

        return results

    def is_configured(self) -> bool:
        """Check if email service is properly configured."""
        return bool(self.smtp_host and self.smtp_user and self.smtp_password)
