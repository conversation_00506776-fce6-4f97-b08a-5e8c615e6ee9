from typing import Optional
from sqlalchemy.orm import Session
import logging

from app.models.database.user import User, UserStatus, UserRole
from app.models.database.auth import PasswordHashAlgorithm
from app.repositories.user_repository import UserRepository
from app.repositories.auth_repository import UserPasswordRepository
from app.services.password_validation_service import PasswordValidationService
from app.services.email_verification_service import EmailVerificationService


logger = logging.getLogger(__name__)


class UserRegistrationError(Exception):
    """Base exception for user registration errors."""

    pass


class DuplicateEmailError(UserRegistrationError):
    """Raised when attempting to register with an email that already exists."""

    pass


class UserRegistrationService:
    """Service for handling user registration business logic."""

    def __init__(
        self,
        user_repo: UserRepository,
        password_repo: UserPasswordRepository,
        password_service: PasswordValidationService,
        email_verification_service: EmailVerificationService,
    ):
        self.user_repo = user_repo
        self.password_repo = password_repo
        self.password_service = password_service
        self.email_verification_service = email_verification_service

    async def register_user(
        self,
        db: Session,
        email: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        role: UserRole = UserRole.CLIENT,
    ) -> User:
        """
        Register a new user with the provided information.

        Args:
            db: Database session
            email: User's email address
            password: Plain text password
            first_name: User's first name (optional)
            last_name: User's last name (optional)
            role: User's role (defaults to CLIENT)

        Returns:
            User: The created user object

        Raises:
            DuplicateEmailError: If email already exists
            UserRegistrationError: For other registration errors
        """
        try:
            # Validate input
            email = email.lower().strip()
            self.password_service.validate_complexity(password)

            # Check for duplicate email
            if self.user_repo.email_exists(email):
                raise DuplicateEmailError(f"User with email {email} already exists")

            # Create user record
            user_data = {
                "email": email,
                "first_name": first_name.strip() if first_name else None,
                "last_name": last_name.strip() if last_name else None,
                "role": role,
                "status": UserStatus.PENDING_VERIFICATION,
                "email_verified": False,
            }

            user = self.user_repo.create(user_data)
            db.flush()  # Get the user ID

            # Hash password and create password record
            hashed_password = self.password_service.hash_password(password)
            self.password_repo.create_password_record(
                user_id=user.id,
                hashed_password=hashed_password,
                algorithm=PasswordHashAlgorithm.BCRYPT,
                algorithm_version=self.password_service.BCRYPT_ROUNDS,
            )

            # Generate verification token and send email
            (
                verification_token,
                _,
            ) = self.email_verification_service.create_verification_token(user.id)

            # Send verification email
            await self.email_verification_service.send_verification_email(
                user, verification_token
            )

            db.commit()
            logger.info(f"User registered successfully: {email}")

            return user

        except Exception as e:
            db.rollback()
            if isinstance(e, (DuplicateEmailError, UserRegistrationError)):
                raise
            logger.error(f"Failed to register user {email}: {str(e)}")
            raise UserRegistrationError(f"Failed to register user: {str(e)}")

    def verify_email(self, token: str) -> bool:
        """
        Verify an email verification token and activate the user.

        Args:
            token: Email verification token

        Returns:
            bool: True if verification successful, False otherwise
        """
        return self.email_verification_service.verify_email_token(token)

    async def resend_verification_email(self, email: str) -> bool:
        """
        Resend verification email to a user.

        Args:
            email: Email address of the user

        Returns:
            bool: True if email sent successfully
        """
        return await self.email_verification_service.resend_verification_email(email)

    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.

        Args:
            email: Email address to search for

        Returns:
            Optional[User]: User object if found, None otherwise
        """
        return self.user_repo.get_by_email(email)

    def check_email_available(self, email: str) -> bool:
        """
        Check if an email address is available for registration.

        Args:
            email: Email address to check

        Returns:
            bool: True if email is available, False if taken
        """
        email = email.lower().strip()
        return not self.user_repo.email_exists(email)

    def get_registration_status(self, email: str) -> dict:
        """
        Get registration status for a user.

        Args:
            email: Email address to check

        Returns:
            dict: Registration status information
        """
        user = self.user_repo.get_by_email(email)

        if not user:
            return {
                "exists": False,
                "email_verified": False,
                "status": None,
                "created_at": None,
            }

        return {
            "exists": True,
            "email_verified": user.email_verified,
            "status": user.status.value,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "last_login_at": user.last_login_at.isoformat()
            if user.last_login_at
            else None,
        }

    def validate_registration_data(
        self,
        email: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
    ) -> dict:
        """
        Validate registration data and return validation results.

        Args:
            email: Email address
            password: Password
            first_name: First name (optional)
            last_name: Last name (optional)

        Returns:
            dict: Validation results
        """
        errors = []
        warnings = []

        # Email validation
        email = email.lower().strip()
        if not email:
            errors.append("Email is required")
        elif self.user_repo.email_exists(email):
            errors.append("Email address is already registered")

        # Password validation
        try:
            self.password_service.validate_complexity(password)
        except Exception as e:
            errors.append(str(e))

        # Name validation (optional but if provided should be reasonable)
        if first_name and len(first_name.strip()) > 100:
            errors.append("First name must be 100 characters or less")

        if last_name and len(last_name.strip()) > 100:
            errors.append("Last name must be 100 characters or less")

        # Password strength analysis
        strength = self.password_service.validate_password_strength(password)
        if strength["strength"] == "weak":
            warnings.append("Password is weak. Consider using a stronger password.")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "password_strength": strength,
        }
