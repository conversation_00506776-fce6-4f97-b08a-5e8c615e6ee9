from functools import lru_cache
from typing import Generator
from fastapi import Depends, Request
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.repositories.user_repository import UserRepository
from app.repositories.auth_repository import (
    UserPasswordRepository,
    EmailVerificationTokenRepository,
)
from app.repositories.password_reset_repository import PasswordResetTokenRepository
from app.services.password_validation_service import PasswordValidationService
from app.services.email_service import EmailService
from app.services.email_verification_service import EmailVerificationService
from app.services.user_registration_service import UserRegistrationService
from app.services.password_reset_service import PasswordResetService


# Repository dependencies
def get_user_repository(db: Session = Depends(get_db)) -> UserRepository:
    """Get user repository instance."""
    return UserRepository(db)


def get_password_repository(db: Session = Depends(get_db)) -> UserPasswordRepository:
    """Get password repository instance."""
    return UserPasswordRepository(db)


def get_email_verification_token_repository(
    db: Session = Depends(get_db),
) -> EmailVerificationTokenRepository:
    """Get email verification token repository instance."""
    return EmailVerificationTokenRepository(db)


def get_password_reset_repository(db: Session = Depends(get_db)) -> PasswordResetTokenRepository:
    """Get password reset token repository instance."""
    return PasswordResetTokenRepository(db)


# Service dependencies
@lru_cache()
def get_password_validation_service() -> PasswordValidationService:
    """Get password validation service instance (cached)."""
    return PasswordValidationService()


@lru_cache()
def get_email_service() -> EmailService:
    """Get email service instance (cached)."""
    return EmailService()


def get_email_verification_service(
    email_service: EmailService = Depends(get_email_service),
    password_service: PasswordValidationService = Depends(
        get_password_validation_service
    ),
    token_repo: EmailVerificationTokenRepository = Depends(
        get_email_verification_token_repository
    ),
    user_repo: UserRepository = Depends(get_user_repository),
) -> EmailVerificationService:
    """Get email verification service instance."""
    return EmailVerificationService(
        email_service=email_service,
        password_service=password_service,
        token_repo=token_repo,
        user_repo=user_repo,
    )


def get_user_registration_service(
    user_repo: UserRepository = Depends(get_user_repository),
    password_repo: UserPasswordRepository = Depends(get_password_repository),
    password_service: PasswordValidationService = Depends(
        get_password_validation_service
    ),
    email_verification_service: EmailVerificationService = Depends(
        get_email_verification_service
    ),
) -> UserRegistrationService:
    """Get user registration service instance."""
    return UserRegistrationService(
        user_repo=user_repo,
        password_repo=password_repo,
        password_service=password_service,
        email_verification_service=email_verification_service,
    )


def get_password_reset_service(
    email_service: EmailService = Depends(get_email_service),
    user_repo: UserRepository = Depends(get_user_repository),
    reset_repo: PasswordResetTokenRepository = Depends(get_password_reset_repository),
) -> PasswordResetService:
    """Get password reset service instance."""
    return PasswordResetService(
        email_service=email_service,
        user_repo=user_repo,
        reset_repo=reset_repo,
    )


# Utility functions
def get_client_ip(request: Request) -> str:
    """Extract client IP address considering proxies."""
    # Try X-Forwarded-For first
    if "x-forwarded-for" in request.headers:
        return request.headers["x-forwarded-for"].split(",")[0].strip()
    
    # Try X-Real-IP
    if "x-real-ip" in request.headers:
        return request.headers["x-real-ip"]
    
    # Fall back to direct connection IP
    return request.client.host


# Database session dependencies
def get_db_session() -> Generator[Session, None, None]:
    """Get database session with automatic cleanup."""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


# Utility dependencies for testing and development
def get_repositories(db: Session = Depends(get_db)) -> dict:
    """Get all repository instances for testing purposes."""
    return {
        "user_repo": UserRepository(db),
        "password_repo": UserPasswordRepository(db),
        "token_repo": EmailVerificationTokenRepository(db),
        "reset_repo": PasswordResetTokenRepository(db),
    }


def get_services(
    password_service: PasswordValidationService = Depends(
        get_password_validation_service
    ),
    email_service: EmailService = Depends(get_email_service),
    email_verification_service: EmailVerificationService = Depends(
        get_email_verification_service
    ),
    user_registration_service: UserRegistrationService = Depends(
        get_user_registration_service
    ),
    password_reset_service: PasswordResetService = Depends(
        get_password_reset_service
    ),
) -> dict:
    """Get all service instances for testing purposes."""
    return {
        "password_service": password_service,
        "email_service": email_service,
        "email_verification_service": email_verification_service,
        "user_registration_service": user_registration_service,
        "password_reset_service": password_reset_service,
    }
