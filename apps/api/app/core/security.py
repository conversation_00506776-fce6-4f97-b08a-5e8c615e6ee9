import secrets
import hashlib
from typing import <PERSON><PERSON>


def generate_reset_token() -> <PERSON><PERSON>[str, str]:
    """
    Generate secure password reset token and its hash.
    
    Returns:
        Tuple[str, str]: (plain_token, token_hash)
    """
    # Generate 32-byte (256-bit) cryptographically secure token
    plain_token = secrets.token_urlsafe(32)
    
    # Hash the token for database storage
    token_hash = hashlib.sha256(plain_token.encode('utf-8')).hexdigest()
    
    return plain_token, token_hash