from typing import Optional, List
from sqlalchemy.orm import Session

from app.models.database.user import User, UserStatus, UserRole
from app.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for User entity database operations."""

    def __init__(self, db: Session):
        super().__init__(db, User)

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        return self.db.query(User).filter(User.email == email.lower().strip()).first()

    def get_active_users(self) -> List[User]:
        """Get all active users."""
        return self.db.query(User).filter(User.status == UserStatus.ACTIVE).all()

    def get_pending_verification_users(self) -> List[User]:
        """Get all users pending email verification."""
        return (
            self.db.query(User)
            .filter(User.status == UserStatus.PENDING_VERIFICATION)
            .all()
        )

    def get_users_by_role(self, role: UserRole) -> List[User]:
        """Get users by specific role."""
        return self.db.query(User).filter(User.role == role).all()

    def get_users_by_status(self, status: UserStatus) -> List[User]:
        """Get users by specific status."""
        return self.db.query(User).filter(User.status == status).all()

    def email_exists(self, email: str) -> bool:
        """Check if email already exists in the database."""
        return (
            self.db.query(User).filter(User.email == email.lower().strip()).first()
            is not None
        )

    def activate_user(self, user: User) -> User:
        """Activate a user account."""
        user.status = UserStatus.ACTIVE
        user.email_verified = True
        self.db.flush()
        return user

    def deactivate_user(self, user: User) -> User:
        """Deactivate a user account."""
        user.status = UserStatus.INACTIVE
        self.db.flush()
        return user

    def suspend_user(self, user: User) -> User:
        """Suspend a user account."""
        user.status = UserStatus.SUSPENDED
        self.db.flush()
        return user

    def search_users(
        self,
        email_pattern: Optional[str] = None,
        first_name_pattern: Optional[str] = None,
        last_name_pattern: Optional[str] = None,
        role: Optional[UserRole] = None,
        status: Optional[UserStatus] = None,
    ) -> List[User]:
        """Search users with optional filters."""
        query = self.db.query(User)

        if email_pattern:
            query = query.filter(User.email.ilike(f"%{email_pattern}%"))

        if first_name_pattern:
            query = query.filter(User.first_name.ilike(f"%{first_name_pattern}%"))

        if last_name_pattern:
            query = query.filter(User.last_name.ilike(f"%{last_name_pattern}%"))

        if role:
            query = query.filter(User.role == role)

        if status:
            query = query.filter(User.status == status)

        return query.all()
