from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import uuid

from app.models.database.auth import (
    UserPassword,
    EmailVerificationToken,
    VerificationType,
    PasswordHashAlgorithm,
)
from app.repositories.base_repository import BaseRepository


class UserPasswordRepository(BaseRepository[UserPassword]):
    """Repository for UserPassword entity database operations."""

    def __init__(self, db: Session):
        super().__init__(db, UserPassword)

    def get_by_user_id(self, user_id: uuid.UUID) -> Optional[UserPassword]:
        """Get the password record for a user (only one per user)."""
        return (
            self.db.query(UserPassword).filter(UserPassword.user_id == user_id).first()
        )

    def create_password_record(
        self,
        user_id: uuid.UUID,
        hashed_password: str,
        algorithm: PasswordHashAlgorithm = PasswordHashAlgorithm.BCRYPT,
        algorithm_version: int = 12,
    ) -> UserPassword:
        """Create a new password record for a user with explicit field values."""
        password_record = UserPassword(
            user_id=user_id,
            hashed_password=hashed_password,
            algorithm=algorithm,
            algorithm_version=algorithm_version,
            password_history=[],
        )
        self.db.add(password_record)
        self.db.flush()
        return password_record

    def update_password(
        self,
        user_id: uuid.UUID,
        new_hashed_password: str,
        algorithm: PasswordHashAlgorithm = PasswordHashAlgorithm.BCRYPT,
        algorithm_version: int = 12,
    ) -> UserPassword:
        """Update user's password and add current password record to history."""
        password_record = self.get_by_user_id(user_id)

        if not password_record:
            return self.create_password_record(
                user_id=user_id,
                hashed_password=new_hashed_password,
                algorithm=algorithm,
                algorithm_version=algorithm_version,
            )

        # Serialize current password record before updating
        history = password_record.password_history or []
        history.append(
            {
                "hashed_password": password_record.hashed_password,
                "algorithm": password_record.algorithm.value,
                "algorithm_version": password_record.algorithm_version,
                "created_at": password_record.created_at.isoformat()
                if password_record.created_at
                else None,
                "updated_at": password_record.updated_at.isoformat()
                if password_record.updated_at
                else None,
            }
        )

        # Keep only last 5 passwords in history
        password_record.password_history = history[-5:]
        password_record.hashed_password = new_hashed_password
        password_record.algorithm = algorithm
        password_record.algorithm_version = algorithm_version

        self.db.flush()
        return password_record

    def check_password_in_history(
        self, user_id: uuid.UUID, password_hash: str, history_depth: int = 5
    ) -> bool:
        """Check if a password hash exists in user's password history."""
        password_record = self.get_by_user_id(user_id)
        if not password_record:
            return False

        # Check current password
        if password_record.hashed_password == password_hash:
            return True

        # Check history
        history = password_record.password_history or []
        recent_history = (
            history[-history_depth:] if len(history) > history_depth else history
        )

        return any(
            entry.get("hashed_password") == password_hash for entry in recent_history
        )


class EmailVerificationTokenRepository(BaseRepository[EmailVerificationToken]):
    """Repository for EmailVerificationToken entity database operations."""

    def __init__(self, db: Session):
        super().__init__(db, EmailVerificationToken)

    def get_by_user_id(self, user_id: uuid.UUID) -> Optional[EmailVerificationToken]:
        """Get the verification token for a user (only one per user)."""
        return (
            self.db.query(EmailVerificationToken)
            .filter(EmailVerificationToken.user_id == user_id)
            .first()
        )

    def get_by_token_hash(self, token_hash: str) -> Optional[EmailVerificationToken]:
        """Get verification token by hash."""
        return (
            self.db.query(EmailVerificationToken)
            .filter(EmailVerificationToken.token_hash == token_hash)
            .first()
        )

    def get_valid_token(self, token_hash: str) -> Optional[EmailVerificationToken]:
        """Get a valid (unexpired, unverified) token by hash."""
        return (
            self.db.query(EmailVerificationToken)
            .filter(
                and_(
                    EmailVerificationToken.token_hash == token_hash,
                    EmailVerificationToken.expires_at > datetime.utcnow(),
                    EmailVerificationToken.is_verified.is_(False),
                )
            )
            .first()
        )

    def create_or_update_token(
        self,
        user_id: uuid.UUID,
        token_hash: str,
        expires_at: datetime,
        verification_type: VerificationType = VerificationType.REGISTRATION,
    ) -> EmailVerificationToken:
        """Create a new verification token or update existing one for a user."""
        existing_token = self.get_by_user_id(user_id)

        if existing_token:
            # Update existing token with explicit values
            existing_token.token_hash = token_hash
            existing_token.expires_at = expires_at
            existing_token.verification_type = verification_type
            existing_token.is_verified = False
            self.db.flush()
            return existing_token
        else:
            # Create new token with all fields explicitly set
            token = EmailVerificationToken(
                user_id=user_id,
                token_hash=token_hash,
                verification_type=verification_type,
                expires_at=expires_at,
                is_verified=False,
            )
            self.db.add(token)
            self.db.flush()
            return token

    def mark_token_as_verified(
        self, token: EmailVerificationToken
    ) -> EmailVerificationToken:
        """Mark a token as verified."""
        token.is_verified = True
        self.db.flush()
        return token

    def cleanup_expired_tokens(self) -> int:
        """Remove expired and verified tokens and return count of deleted tokens."""
        tokens_to_delete = self.db.query(EmailVerificationToken).filter(
            or_(
                EmailVerificationToken.expires_at <= datetime.utcnow(),
                EmailVerificationToken.is_verified.is_(True),
            )
        )
        count = tokens_to_delete.count()
        tokens_to_delete.delete()
        self.db.flush()
        return count
