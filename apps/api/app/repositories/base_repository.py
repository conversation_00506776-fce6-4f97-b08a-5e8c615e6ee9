from abc import ABC
from typing import Generic, TypeVar, List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

T = TypeVar("T")


class BaseRepository(Generic[T], ABC):
    """
    Base repository class providing common CRUD operations.
    This is the only layer that should access the database directly.
    """

    def __init__(self, db: Session, model: type[T]):
        self.db = db
        self.model = model

    def create(self, obj_data: Dict[str, Any]) -> T:
        """Create a new record."""
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        self.db.flush()
        return db_obj

    def get_by_id(self, id: Any) -> Optional[T]:
        """Get a record by its ID."""
        return self.db.query(self.model).filter(self.model.id == id).first()

    def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get all records with pagination."""
        return self.db.query(self.model).offset(skip).limit(limit).all()

    def update(self, db_obj: T, update_data: Dict[str, Any]) -> T:
        """Update an existing record."""
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        self.db.flush()
        return db_obj

    def delete(self, db_obj: T) -> None:
        """Delete a record."""
        self.db.delete(db_obj)
        self.db.flush()

    def filter_by(self, **kwargs) -> List[T]:
        """Filter records by arbitrary field values."""
        query = self.db.query(self.model)
        for field, value in kwargs.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        return query.all()

    def filter_by_multiple(self, filters: List[Any]) -> List[T]:
        """Filter records by multiple conditions using AND logic."""
        return self.db.query(self.model).filter(and_(*filters)).all()

    def count(self) -> int:
        """Count total records."""
        return self.db.query(self.model).count()

    def exists(self, **kwargs) -> bool:
        """Check if a record exists with given conditions."""
        query = self.db.query(self.model)
        for field, value in kwargs.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        return query.first() is not None
