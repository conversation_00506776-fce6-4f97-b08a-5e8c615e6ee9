import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.database.auth import PasswordResetToken
from app.repositories.base_repository import BaseRepository


class PasswordResetTokenRepository(BaseRepository[PasswordResetToken]):
    """Repository for PasswordResetToken entity database operations."""

    def __init__(self, db: Session):
        super().__init__(db, PasswordResetToken)

    def get_by_token_hash(self, token_hash: str) -> Optional[PasswordResetToken]:
        """Get password reset token by hash."""
        return self.db.query(PasswordResetToken).filter(
            PasswordResetToken.token_hash == token_hash
        ).first()

    def get_valid_token(self, token_hash: str) -> Optional[PasswordResetToken]:
        """Get a valid (unexpired, unused) token by hash."""
        return self.db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.token_hash == token_hash,
                PasswordResetToken.expires_at > datetime.now(timezone.utc),
                PasswordResetToken.is_used.is_(False)
            )
        ).first()

    def create_token(
        self, 
        user_id: uuid.UUID, 
        token_hash: str, 
        expires_at: datetime,
        ip_address: str,
        user_agent: str
    ) -> PasswordResetToken:
        """Create a new password reset token."""
        token = PasswordResetToken(
            user_id=user_id,
            token_hash=token_hash,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
            is_used=False
        )
        self.db.add(token)
        self.db.flush()
        return token

    def count_recent_requests_by_ip(self, ip_address: str, hours: int = 1) -> int:
        """Count recent password reset requests from an IP address."""
        since = datetime.now(timezone.utc) - timedelta(hours=hours)
        return self.db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.ip_address == ip_address,
                PasswordResetToken.issued_at > since
            )
        ).count()

    def invalidate_user_tokens(self, user_id: uuid.UUID) -> int:
        """Invalidate all active tokens for a user."""
        now = datetime.now(timezone.utc)
        tokens = self.db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.user_id == user_id,
                PasswordResetToken.is_used.is_(False),
                PasswordResetToken.expires_at > now
            )
        )
        count = tokens.count()
        tokens.update({
            "is_used": True,
            "used_at": now
        })
        self.db.flush()
        return count

    def cleanup_expired_tokens(self) -> int:
        """Remove expired and used tokens."""
        now = datetime.now(timezone.utc)
        tokens_to_delete = self.db.query(PasswordResetToken).filter(
            or_(
                PasswordResetToken.expires_at <= now,
                PasswordResetToken.is_used.is_(True)
            )
        )
        count = tokens_to_delete.count()
        tokens_to_delete.delete()
        self.db.flush()
        return count