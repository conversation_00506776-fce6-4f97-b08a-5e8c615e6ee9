from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional


class PasswordResetError(Exception):
    """Base exception for password reset errors."""
    pass


class ForgotPasswordRequest(BaseModel):
    """Schema for forgot password request."""
    
    email: EmailStr = Field(..., description="Email address for password reset")

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class ForgotPasswordResponse(BaseModel):
    """Schema for forgot password response."""
    
    success: bool = Field(True, description="Request processed successfully")
    message: str = Field(
        "If an account exists with this email, a password reset link has been sent.",
        description="Success message"
    )


class RateLimitErrorResponse(BaseModel):
    """Schema for rate limit error response."""
    
    success: bool = Field(False, description="Request failed")
    error: str = Field("RATE_LIMIT_EXCEEDED", description="Error code")
    message: str = Field(..., description="Error message")
    retry_after: Optional[int] = Field(None, description="Seconds until retry allowed")