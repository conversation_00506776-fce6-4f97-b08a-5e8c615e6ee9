from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, field_validator
import uuid

from app.models.database.user import User<PERSON><PERSON>, UserStatus


class UserRegistrationRequest(BaseModel):
    """Schema for user registration request."""

    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="User's password (8-128 characters)",
    )
    first_name: Optional[str] = Field(
        None, max_length=100, description="User's first name (max 100 characters)"
    )
    last_name: Optional[str] = Field(
        None, max_length=100, description="User's last name (max 100 characters)"
    )

    @field_validator("first_name", "last_name")
    def validate_names(cls, v: Optional[str]) -> Optional[str]:
        """Validate name fields."""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
        return v

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class UserRegistrationResponse(BaseModel):
    """Schema for user registration response."""

    id: uuid.UUID = Field(..., description="User's unique identifier")
    email: str = Field(..., description="User's email address")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    role: UserRole = Field(..., description="User's role")
    status: UserStatus = Field(..., description="User's account status")
    email_verified: bool = Field(..., description="Whether email is verified")
    created_at: datetime = Field(..., description="Account creation timestamp")
    message: str = Field(..., description="Registration success message")

    model_config = {"from_attributes": True}


class EmailVerificationRequest(BaseModel):
    """Schema for email verification request."""

    token: str = Field(..., description="Email verification token")


class EmailVerificationResponse(BaseModel):
    """Schema for email verification response."""

    success: bool = Field(..., description="Whether verification was successful")
    message: str = Field(..., description="Verification result message")


class ResendVerificationRequest(BaseModel):
    """Schema for resending verification email."""

    email: EmailStr = Field(..., description="Email address to resend verification to")

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class ResendVerificationResponse(BaseModel):
    """Schema for resend verification response."""

    success: bool = Field(..., description="Whether email was sent successfully")
    message: str = Field(..., description="Response message")


class UserRegistrationValidationRequest(BaseModel):
    """Schema for validating registration data."""

    email: EmailStr = Field(..., description="Email address to validate")
    password: str = Field(..., description="Password to validate")
    first_name: Optional[str] = Field(None, description="First name to validate")
    last_name: Optional[str] = Field(None, description="Last name to validate")

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class PasswordStrength(BaseModel):
    """Schema for password strength analysis."""

    score: float = Field(..., description="Password strength score")
    max_score: float = Field(..., description="Maximum possible score")
    strength: str = Field(
        ..., description="Password strength level (weak/medium/strong)"
    )
    details: dict = Field(..., description="Detailed strength analysis")


class UserRegistrationValidationResponse(BaseModel):
    """Schema for registration validation response."""

    valid: bool = Field(..., description="Whether the data is valid")
    errors: list[str] = Field(..., description="List of validation errors")
    warnings: list[str] = Field(..., description="List of validation warnings")
    password_strength: PasswordStrength = Field(
        ..., description="Password strength analysis"
    )


class EmailAvailabilityRequest(BaseModel):
    """Schema for checking email availability."""

    email: EmailStr = Field(..., description="Email address to check")

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class EmailAvailabilityResponse(BaseModel):
    """Schema for email availability response."""

    available: bool = Field(..., description="Whether email is available")
    email: str = Field(..., description="The checked email address")


class UserRegistrationStatusRequest(BaseModel):
    """Schema for getting registration status."""

    email: EmailStr = Field(..., description="Email address to check status for")

    @field_validator("email")
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email."""
        return v.lower().strip()


class UserRegistrationStatusResponse(BaseModel):
    """Schema for registration status response."""

    exists: bool = Field(..., description="Whether user exists")
    email_verified: bool = Field(..., description="Whether email is verified")
    status: Optional[str] = Field(None, description="User account status")
    created_at: Optional[str] = Field(None, description="Account creation timestamp")
    last_login_at: Optional[str] = Field(None, description="Last login timestamp")


class UserBase(BaseModel):
    """Base user schema with common fields."""

    email: str = Field(..., description="User's email address")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    role: UserRole = Field(..., description="User's role")


class UserPublic(UserBase):
    """Public user schema (safe for external consumption)."""

    id: uuid.UUID = Field(..., description="User's unique identifier")
    status: UserStatus = Field(..., description="User's account status")
    email_verified: bool = Field(..., description="Whether email is verified")
    created_at: datetime = Field(..., description="Account creation timestamp")

    model_config = {"from_attributes": True}


class UserInternal(UserPublic):
    """Internal user schema (includes sensitive fields for internal use)."""

    last_login_at: Optional[datetime] = Field(None, description="Last login timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    created_by_id: Optional[uuid.UUID] = Field(
        None, description="ID of user who created this account"
    )
    updated_by_id: Optional[uuid.UUID] = Field(
        None, description="ID of user who last updated this account"
    )

    model_config = {"from_attributes": True}
