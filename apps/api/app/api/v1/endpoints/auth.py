import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request, Header, BackgroundTasks

from app.models.schemas.auth_schemas import ForgotPasswordRequest, ForgotPasswordResponse
from app.services.password_reset_service import PasswordResetService
from app.dependencies import get_client_ip, get_password_reset_service


logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/forgot-password", response_model=ForgotPasswordResponse)
async def forgot_password(
    request: ForgotPasswordRequest,
    background_tasks: BackgroundTasks,
    http_request: Request,
    user_agent: str = Header(None),
    service: PasswordResetService = Depends(get_password_reset_service)
) -> ForgotPasswordResponse:
    """
    Initiate password reset process.
    
    Always returns 200 OK with the same message to prevent user enumeration.
    Rate limited to 3 requests per hour per IP address.
    Uses background tasks for consistent response timing.
    """
    # Get client IP
    client_ip = get_client_ip(http_request)
    
    # Initiate password reset (always returns True, uses background tasks)
    await service.initiate_password_reset(
        email=request.email,
        ip_address=client_ip,
        user_agent=user_agent or "Unknown",
        background_tasks=background_tasks
    )
    
    # Always return the same response immediately
    return ForgotPasswordResponse()