# Application
PROJECT_NAME="FinPro API"
VERSION="1.0.0"
API_V1_STR="/api/v1"

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# Database
POSTGRES_SERVER=localhost
POSTGRES_USER=finpro
POSTGRES_PASSWORD=password
POSTGRES_DB=finpro_db

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# Email
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=FinPro

# First superuser
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changeme