"""add password reset tokens table

Revision ID: 65fabd781000
Revises: aea905319d1e
Create Date: 2025-08-27 17:37:04.046126

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65fabd781000'
down_revision: Union[str, Sequence[str], None] = 'aea905319d1e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create password reset tokens table
    op.create_table('auth_password_reset_tokens',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('token_hash', sa.String(length=255), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('issued_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_used', sa.Boolean(), nullable=True),
        sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for password reset tokens
    op.create_index('idx_password_reset_tokens_hash', 'auth_password_reset_tokens', ['token_hash'], unique=True)
    op.create_index('idx_password_reset_tokens_expires', 'auth_password_reset_tokens', ['expires_at'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('idx_password_reset_tokens_expires', table_name='auth_password_reset_tokens')
    op.drop_index('idx_password_reset_tokens_hash', table_name='auth_password_reset_tokens')
    
    # Drop table
    op.drop_table('auth_password_reset_tokens')
