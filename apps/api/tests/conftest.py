import pytest
import asyncio
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import tempfile
import os
from fastapi.testclient import TestClient

from app.core.database import Base
from app.models.database.user import User, UserRole, UserStatus
from app.models.database.auth import UserPassword, EmailVerificationToken, PasswordResetToken, PasswordHashAlgorithm
from app.repositories.user_repository import UserRepository
from app.repositories.auth_repository import UserPasswordRepository, EmailVerificationTokenRepository
from app.repositories.password_reset_repository import PasswordResetTokenRepository
from app.services.password_validation_service import PasswordValidationService
from app.services.email_service import EmailService
from app.services.email_verification_service import EmailVerificationService
from app.services.user_registration_service import UserRegistrationService
from app.services.password_reset_service import PasswordResetService
from app.main import app


# Test database setup
@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine."""
    # Use in-memory SQLite for tests
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(bind=engine)
    return engine


@pytest.fixture
def test_db(test_engine) -> Generator[Session, None, None]:
    """Create test database session with proper cleanup."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.rollback()
        # Clear all tables for clean state
        for table in reversed(Base.metadata.sorted_tables):
            session.execute(table.delete())
        session.commit()
        session.close()


# Repository fixtures
@pytest.fixture
def user_repo(test_db: Session) -> UserRepository:
    """Get user repository for testing."""
    return UserRepository(test_db)


@pytest.fixture
def password_repo(test_db: Session) -> UserPasswordRepository:
    """Get password repository for testing."""
    return UserPasswordRepository(test_db)


@pytest.fixture
def token_repo(test_db: Session) -> EmailVerificationTokenRepository:
    """Get email verification token repository for testing."""
    return EmailVerificationTokenRepository(test_db)


@pytest.fixture
def reset_token_repo(test_db: Session) -> PasswordResetTokenRepository:
    """Get password reset token repository for testing."""
    return PasswordResetTokenRepository(test_db)


# Service fixtures
@pytest.fixture
def password_service() -> PasswordValidationService:
    """Get password validation service for testing."""
    return PasswordValidationService()


@pytest.fixture
def email_service() -> EmailService:
    """Get email service for testing."""
    return EmailService()


@pytest.fixture
def email_verification_service(
    email_service: EmailService,
    password_service: PasswordValidationService,
    token_repo: EmailVerificationTokenRepository,
    user_repo: UserRepository
) -> EmailVerificationService:
    """Get email verification service for testing."""
    return EmailVerificationService(
        email_service=email_service,
        password_service=password_service,
        token_repo=token_repo,
        user_repo=user_repo
    )


@pytest.fixture
def user_registration_service(
    user_repo: UserRepository,
    password_repo: UserPasswordRepository,
    password_service: PasswordValidationService,
    email_verification_service: EmailVerificationService
) -> UserRegistrationService:
    """Get user registration service for testing."""
    return UserRegistrationService(
        user_repo=user_repo,
        password_repo=password_repo,
        password_service=password_service,
        email_verification_service=email_verification_service
    )


@pytest.fixture
def password_reset_service(
    email_service: EmailService,
    user_repo: UserRepository,
    reset_token_repo: PasswordResetTokenRepository
) -> PasswordResetService:
    """Get password reset service for testing."""
    return PasswordResetService(
        email_service=email_service,
        user_repo=user_repo,
        reset_repo=reset_token_repo
    )


# API Testing fixtures
@pytest.fixture
def api_client():
    """FastAPI test client for endpoint testing."""
    return TestClient(app)


# Data fixtures
@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    import uuid
    unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
    return {
        "email": unique_email,
        "first_name": "John",
        "last_name": "Doe",
        "role": UserRole.CLIENT,
        "status": UserStatus.PENDING_VERIFICATION,
        "email_verified": False
    }


@pytest.fixture
def sample_user(test_db: Session, user_repo: UserRepository, sample_user_data):
    """Create a sample user for testing."""
    user = user_repo.create(sample_user_data)
    test_db.commit()
    return user


@pytest.fixture
def sample_password():
    """Sample password for testing."""
    return "SecurePass123!"


@pytest.fixture
def weak_password():
    """Weak password for testing."""
    return "weak"


@pytest.fixture
def sample_verification_token():
    """Sample verification token for testing."""
    return "test_token_123456789"


# Async fixtures
@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Mock fixtures for external dependencies
@pytest.fixture
def mock_email_templates(tmp_path):
    """Create temporary email templates for testing."""
    template_dir = tmp_path / "templates" / "email"
    template_dir.mkdir(parents=True)
    
    # Create email verification template
    verification_html = """
    <html>
        <body>
            <h1>Verify Email</h1>
            <p>Hi {{first_name}},</p>
            <p><a href="{{verification_url}}">Verify Email</a></p>
            <p>Expires in {{expiry_hours}} hours.</p>
        </body>
    </html>
    """
    
    verification_txt = """
    Verify Email
    
    Hi {{first_name}},
    
    Verify: {{verification_url}}
    
    Expires in {{expiry_hours}} hours.
    """
    
    (template_dir / "email_verification.html").write_text(verification_html)
    (template_dir / "email_verification.txt").write_text(verification_txt)
    
    # Create welcome template
    welcome_html = """
    <html>
        <body>
            <h1>Welcome!</h1>
            <p>Hi {{first_name}},</p>
            <p><a href="{{login_url}}">Login</a></p>
        </body>
    </html>
    """
    
    welcome_txt = """
    Welcome!
    
    Hi {{first_name}},
    
    Login: {{login_url}}
    """
    
    (template_dir / "welcome.html").write_text(welcome_html)
    (template_dir / "welcome.txt").write_text(welcome_txt)
    
    return template_dir


@pytest.fixture
def mock_template_dir(mock_email_templates, monkeypatch):
    """Mock the template directory in email verification service."""
    def mock_init(self, *args, **kwargs):
        # Call original init
        EmailVerificationService.__init__(self, *args, **kwargs)
        # Override template directory
        self.template_dir = mock_email_templates
    
    monkeypatch.setattr(EmailVerificationService, "__init__", mock_init)


# Utility functions for tests
def create_test_user(
    db: Session, 
    email: str = None,
    **kwargs
) -> User:
    """Helper function to create test users."""
    import uuid
    if email is None:
        email = f"test-{uuid.uuid4().hex[:8]}@example.com"
    
    user_data = {
        "email": email,
        "first_name": "Test",
        "last_name": "User",
        "role": UserRole.CLIENT,
        "status": UserStatus.PENDING_VERIFICATION,
        "email_verified": False,
        **kwargs
    }
    
    user_repo = UserRepository(db)
    user = user_repo.create(user_data)
    db.commit()
    return user


def create_test_password(
    db: Session,
    user_id: str,
    password: str = "TestPass123!"
) -> UserPassword:
    """Helper function to create test password records."""
    password_service = PasswordValidationService()
    hashed_password = password_service.hash_password(password)
    
    password_repo = UserPasswordRepository(db)
    password_record = password_repo.create_password_record(
        user_id=user_id,
        hashed_password=hashed_password
    )
    db.commit()
    return password_record


# Rate limiting and IP fixtures for auth tests
@pytest.fixture(autouse=True)
def clear_rate_limits():
    """Clear rate limit store before each test globally."""
    try:
        from app.middleware.rate_limit import rate_limit_store
        rate_limit_store.requests.clear()
        yield
        rate_limit_store.requests.clear()
    except ImportError:
        # Rate limiting might not be available in all test contexts
        yield


@pytest.fixture
def unique_ip():
    """Generate unique IP address for rate limiting tests that specifically need it."""
    import uuid
    random_id = str(uuid.uuid4().int)[:6]
    octet2 = int(random_id[:3]) % 256
    octet3 = int(random_id[3:]) % 256
    return f"192.{octet2}.{octet3}.100"