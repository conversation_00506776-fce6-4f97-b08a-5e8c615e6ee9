import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta, timezone
from pathlib import Path

from app.services.password_reset_service import PasswordResetService
from app.models.schemas.auth_schemas import PasswordResetError
from tests.conftest import create_test_user


class TestPasswordResetService:
    """Test cases for PasswordResetService."""
    
    # Test constants
    TEST_IP_ADDRESS = "*************"
    TEST_IP_ADDRESS_2 = "*************"
    TEST_USER_AGENT = "Mozilla/5.0 Test Browser"
    
    @pytest.fixture
    def mock_email_service(self):
        """Mock email service for testing."""
        mock_service = AsyncMock()
        mock_service.send_email = AsyncMock()
        return mock_service
    
    @pytest.fixture
    def mock_background_tasks(self):
        """Mock background tasks for testing."""
        mock_tasks = MagicMock()
        mock_tasks.add_task = MagicMock()
        return mock_tasks
    
    @pytest.fixture
    def password_reset_service(self, mock_email_service, user_repo, reset_token_repo):
        """Password reset service fixture."""
        return PasswordResetService(
            email_service=mock_email_service,
            user_repo=user_repo,
            reset_repo=reset_token_repo
        )
    
    @pytest.fixture
    def sample_user(self, test_db):
        """Create a sample user for testing."""
        return create_test_user(test_db, email="<EMAIL>")
    
    @pytest.fixture
    def mock_reset_templates(self, tmp_path, monkeypatch):
        """Create mock password reset templates."""
        template_dir = tmp_path / "templates" / "email"
        template_dir.mkdir(parents=True)
        
        # Create password_reset.html
        html_content = """
        <html>
            <body>
                <h1>Password Reset</h1>
                <p>Hi {{first_name}},</p>
                <p>Click <a href="{{reset_url}}">here</a> to reset your password.</p>
                <p>This link expires in {{expiry_hours}} hours.</p>
            </body>
        </html>
        """
        (template_dir / "password_reset.html").write_text(html_content.strip())
        
        # Create password_reset.txt
        txt_content = """
        Password Reset
        
        Hi {{first_name}},
        
        Reset your password: {{reset_url}}
        
        This link expires in {{expiry_hours}} hours.
        """
        (template_dir / "password_reset.txt").write_text(txt_content.strip())
        
        # Mock the template directory in service
        def mock_init(self, *args, **kwargs):
            # Call original __init__
            PasswordResetService.__init__(self, *args, **kwargs)
            # Override template directory
            self.template_dir = template_dir
        
        monkeypatch.setattr(PasswordResetService, "__init__", mock_init)
        return template_dir
    
    @pytest.mark.asyncio
    async def test_initiate_reset_existing_user(
        self, 
        password_reset_service, 
        mock_background_tasks,
        sample_user
    ):
        """Test initiating password reset for existing user."""
        email = sample_user.email
        ip_address = self.TEST_IP_ADDRESS
        user_agent = self.TEST_USER_AGENT
        
        result = await password_reset_service.initiate_password_reset(
            email=email,
            ip_address=ip_address,
            user_agent=user_agent,
            background_tasks=mock_background_tasks
        )
        
        # Should always return True immediately
        assert result is True
        
        # Should queue background task
        mock_background_tasks.add_task.assert_called_once()
        call_args = mock_background_tasks.add_task.call_args
        assert call_args[1]["email"] == email.lower()
        assert call_args[1]["ip_address"] == ip_address
        assert call_args[1]["user_agent"] == user_agent
    
    @pytest.mark.asyncio
    async def test_initiate_reset_nonexistent_user(
        self,
        password_reset_service,
        mock_background_tasks
    ):
        """Test initiating password reset for non-existent user."""
        email = "<EMAIL>"
        ip_address = self.TEST_IP_ADDRESS
        user_agent = self.TEST_USER_AGENT
        
        result = await password_reset_service.initiate_password_reset(
            email=email,
            ip_address=ip_address,
            user_agent=user_agent,
            background_tasks=mock_background_tasks
        )
        
        # Should still return True (no user enumeration)
        assert result is True
        
        # Should still queue background task
        mock_background_tasks.add_task.assert_called_once()
    
    def test_email_normalization(self, password_reset_service, mock_background_tasks):
        """Test that email is normalized (lowercase, trimmed) before processing."""
        email = "  <EMAIL>  "
        ip_address = self.TEST_IP_ADDRESS
        user_agent = self.TEST_USER_AGENT
        
        # Use asyncio.run to handle the async call in a sync test
        asyncio.run(password_reset_service.initiate_password_reset(
            email=email,
            ip_address=ip_address,
            user_agent=user_agent,
            background_tasks=mock_background_tasks
        ))
        
        # Check that email was normalized
        call_args = mock_background_tasks.add_task.call_args
        assert call_args[1]["email"] == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_process_password_reset_existing_user(
        self,
        password_reset_service,
        test_db,
        sample_user,
        reset_token_repo,
        mock_reset_templates
    ):
        """Test background processing for existing user."""
        email = sample_user.email
        ip_address = self.TEST_IP_ADDRESS
        user_agent = self.TEST_USER_AGENT
        
        # Mock token generation
        with patch('app.services.password_reset_service.generate_reset_token') as mock_generate:
            mock_generate.return_value = ("plain_token_123", "hashed_token_123")
            
            # Process reset in background
            await password_reset_service._process_password_reset(
                email=email,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Check token was created
            tokens = test_db.query(password_reset_service.reset_repo.model).filter_by(
                user_id=sample_user.id
            ).all()
            
            assert len(tokens) == 1
            token = tokens[0]
            assert token.token_hash == "hashed_token_123"
            assert token.ip_address == ip_address
            assert token.user_agent == user_agent
            assert token.is_used is False
            
            # Check email was sent
            password_reset_service.email_service.send_email.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_password_reset_nonexistent_user(
        self,
        password_reset_service,
        test_db,
        reset_token_repo
    ):
        """Test background processing for non-existent user."""
        email = "<EMAIL>"
        ip_address = self.TEST_IP_ADDRESS
        user_agent = self.TEST_USER_AGENT
        
        # Process reset in background
        await password_reset_service._process_password_reset(
            email=email,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # No tokens should be created
        token_count = test_db.query(password_reset_service.reset_repo.model).count()
        assert token_count == 0
        
        # No email should be sent
        password_reset_service.email_service.send_email.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_token_invalidation(
        self,
        password_reset_service,
        test_db,
        sample_user,
        reset_token_repo,
        mock_reset_templates
    ):
        """Test that existing tokens are invalidated when creating new ones."""
        # Create an existing token
        existing_token = reset_token_repo.create_token(
            user_id=sample_user.id,
            token_hash="old_token_hash",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS_2,
            user_agent="Old Browser"
        )
        test_db.commit()
        
        # Process new reset
        with patch('app.services.password_reset_service.generate_reset_token') as mock_generate:
            mock_generate.return_value = ("new_plain_token", "new_hashed_token")
            
            await password_reset_service._process_password_reset(
                email=sample_user.email,
                ip_address=self.TEST_IP_ADDRESS,
                user_agent="New Browser"
            )
        
        # Check old token was invalidated
        test_db.refresh(existing_token)
        assert existing_token.is_used is True
        assert existing_token.used_at is not None
        
        # Check new token was created
        new_tokens = test_db.query(password_reset_service.reset_repo.model).filter_by(
            user_id=sample_user.id,
            is_used=False
        ).all()
        assert len(new_tokens) == 1
        assert new_tokens[0].token_hash == "new_hashed_token"
    
    def test_token_expiration(self, password_reset_service):
        """Test that tokens are created with 1-hour expiration."""
        # This is tested indirectly through the process function
        # The actual expiration time is set in _process_password_reset
        # We can verify this through the repository tests
        pass
    
    def test_load_template_success(self, password_reset_service):
        """Test successful template loading with variable replacement."""
        html_content, text_content = password_reset_service._load_template(
            "password_reset",
            first_name="John",
            reset_url="https://example.com/reset?token=abc123",
            expiry_hours=1
        )
        
        # Check HTML content
        assert "Hi John," in html_content
        assert "https://example.com/reset?token=abc123" in html_content
        assert "1 hour(s)" in html_content
        
        # Check text content
        assert "Hi John," in text_content
        assert "https://example.com/reset?token=abc123" in text_content
        assert "1 hour(s)" in text_content
    
    def test_load_template_missing_html(self, password_reset_service, tmp_path):
        """Test template loading when HTML template is missing."""
        # Set template dir to empty directory
        password_reset_service.template_dir = tmp_path
        
        with pytest.raises(PasswordResetError, match="HTML template not found"):
            password_reset_service._load_template("password_reset")
    
    def test_load_template_missing_text(self, password_reset_service, tmp_path):
        """Test template loading when text template is missing."""
        # Create only HTML template
        password_reset_service.template_dir = tmp_path
        (tmp_path / "password_reset.html").write_text("<html>Test</html>")
        
        with pytest.raises(PasswordResetError, match="Text template not found"):
            password_reset_service._load_template("password_reset")
    
    @pytest.mark.asyncio
    async def test_send_reset_email(
        self,
        password_reset_service
    ):
        """Test sending password reset email."""
        user_email = "<EMAIL>"
        user_name = "John"
        reset_url = "https://example.com/reset?token=abc123"
        
        await password_reset_service._send_reset_email(
            user_email=user_email,
            user_name=user_name,
            reset_url=reset_url
        )
        
        # Verify email service was called correctly
        password_reset_service.email_service.send_email.assert_called_once()
        call_args = password_reset_service.email_service.send_email.call_args
        
        assert call_args[1]["to_email"] == user_email
        assert call_args[1]["subject"] == "Password Reset Request - FinPro"
        assert user_name in call_args[1]["html_body"]
        assert reset_url in call_args[1]["html_body"]
        assert user_name in call_args[1]["text_body"]
        assert reset_url in call_args[1]["text_body"]
    
    @pytest.mark.asyncio
    async def test_send_email_failure_handling(
        self,
        password_reset_service,
        mock_reset_templates
    ):
        """Test handling of email sending failures."""
        # Make email service raise an exception
        password_reset_service.email_service.send_email.side_effect = Exception("SMTP Error")
        
        with pytest.raises(Exception, match="SMTP Error"):
            await password_reset_service._send_reset_email(
                user_email="<EMAIL>",
                user_name="John",
                reset_url="https://example.com/reset?token=abc123"
            )
    
    @pytest.mark.asyncio
    async def test_background_processing_error_handling(
        self,
        password_reset_service,
        sample_user
    ):
        """Test error handling in background processing."""
        # Make user repo raise an exception
        password_reset_service.user_repo.get_by_email = MagicMock(side_effect=Exception("DB Error"))
        
        # Should not raise exception (background task)
        await password_reset_service._process_password_reset(
            email=sample_user.email,
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        
        # Verify the exception was handled (no tokens created)
        # This is mainly for logging purposes
    
    def test_frontend_url_configuration(self, mock_email_service, user_repo, reset_token_repo):
        """Test that frontend URL is properly configured."""
        service = PasswordResetService(
            email_service=mock_email_service,
            user_repo=user_repo,
            reset_repo=reset_token_repo
        )
        
        # Should have a default frontend URL
        assert service.frontend_url is not None
        assert isinstance(service.frontend_url, str)
    
    def test_template_directory_setup(self, mock_email_service, user_repo, reset_token_repo):
        """Test that template directory is properly set up."""
        service = PasswordResetService(
            email_service=mock_email_service,
            user_repo=user_repo,
            reset_repo=reset_token_repo
        )
        
        # Should have template directory set
        assert service.template_dir is not None
        assert isinstance(service.template_dir, Path)
        assert service.template_dir.name == "email"
    
    @pytest.mark.asyncio
    async def test_consistent_response_timing(
        self,
        password_reset_service,
        mock_background_tasks
    ):
        """Test that response timing is consistent regardless of user existence."""
        # This test verifies that the service returns immediately in both cases
        import time
        
        # Test with existing user
        start_time = time.time()
        result1 = await password_reset_service.initiate_password_reset(
            email="<EMAIL>",
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT,
            background_tasks=mock_background_tasks
        )
        time1 = time.time() - start_time
        
        # Reset mock
        mock_background_tasks.reset_mock()
        
        # Test with non-existing user
        start_time = time.time()
        result2 = await password_reset_service.initiate_password_reset(
            email="<EMAIL>",
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT,
            background_tasks=mock_background_tasks
        )
        time2 = time.time() - start_time
        
        # Both should return True
        assert result1 is True
        assert result2 is True
        
        # Response times should be similar (both very fast, <10ms)
        assert time1 < 0.01
        assert time2 < 0.01
        assert abs(time1 - time2) < 0.005  # Within 5ms of each other
    
    def test_multiple_concurrent_requests(self, password_reset_service, mock_background_tasks):
        """Test handling multiple concurrent reset requests."""
        async def make_request(email_suffix):
            return await password_reset_service.initiate_password_reset(
                email=f"user{email_suffix}@example.com",
                ip_address=self.TEST_IP_ADDRESS,
                user_agent=self.TEST_USER_AGENT,
                background_tasks=mock_background_tasks
            )
        
        # Run multiple concurrent requests
        async def run_concurrent_requests():
            tasks = [make_request(i) for i in range(5)]
            results = await asyncio.gather(*tasks)
            return results
        
        results = asyncio.run(run_concurrent_requests())
        
        # All should succeed
        assert all(result is True for result in results)
        
        # All should queue background tasks
        assert mock_background_tasks.add_task.call_count == 5