import pytest
from unittest.mock import AsyncMock, patch
from app.services.user_registration_service import (
    UserRegistrationService,
    UserRegistrationError,
    DuplicateEmailError
)
from app.models.database.user import UserRole, UserStatus


class TestUserRegistrationService:
    """Test cases for UserRegistrationService."""
    
    @pytest.mark.asyncio
    async def test_register_user_success(
        self, 
        user_registration_service, 
        test_db, 
        sample_password
    ):
        """Test successful user registration."""
        email = "<EMAIL>"
        first_name = "<PERSON>"
        last_name = "<PERSON>e"
        
        # Mock email verification service
        with patch.object(
            user_registration_service.email_verification_service,
            'create_verification_token',
            return_value=("test_token", None)
        ) as mock_create_token, \
        patch.object(
            user_registration_service.email_verification_service,
            'send_verification_email',
            new_callable=AsyncMock
        ) as mock_send_email:
            
            user = await user_registration_service.register_user(
                db=test_db,
                email=email,
                password=sample_password,
                first_name=first_name,
                last_name=last_name
            )
            
            assert user.email == email.lower()
            assert user.first_name == first_name
            assert user.last_name == last_name
            assert user.role == UserRole.CLIENT
            assert user.status == UserStatus.PENDING_VERIFICATION
            assert user.email_verified is False
            
            mock_create_token.assert_called_once()
            mock_send_email.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_register_user_duplicate_email(
        self, 
        user_registration_service, 
        test_db, 
        sample_user, 
        sample_password
    ):
        """Test user registration with duplicate email."""
        with pytest.raises(DuplicateEmailError) as exc_info:
            await user_registration_service.register_user(
                db=test_db,
                email=sample_user.email,
                password=sample_password
            )
        
        assert "already exists" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_register_user_weak_password(
        self, 
        user_registration_service, 
        test_db, 
        weak_password
    ):
        """Test user registration with weak password."""
        email = "<EMAIL>"
        
        with pytest.raises(UserRegistrationError):
            await user_registration_service.register_user(
                db=test_db,
                email=email,
                password=weak_password
            )
    
    @pytest.mark.asyncio
    async def test_register_user_email_normalization(
        self, 
        user_registration_service, 
        test_db, 
        sample_password
    ):
        """Test email normalization during registration."""
        import uuid
        unique_id = uuid.uuid4().hex[:8]
        email = f"  NewUser{unique_id}@EXAMPLE.COM  "
        expected_email = f"newuser{unique_id}@example.com"
        
        with patch.object(
            user_registration_service.email_verification_service,
            'create_verification_token',
            return_value=("test_token", None)
        ), \
        patch.object(
            user_registration_service.email_verification_service,
            'send_verification_email',
            new_callable=AsyncMock
        ):
            
            user = await user_registration_service.register_user(
                db=test_db,
                email=email,
                password=sample_password
            )
            
            assert user.email == expected_email
    
    @pytest.mark.asyncio
    async def test_register_user_name_normalization(
        self, 
        user_registration_service, 
        test_db, 
        sample_password
    ):
        """Test name normalization during registration."""
        import uuid
        email = f"newuser{uuid.uuid4().hex[:8]}@example.com"
        first_name = "  Jane  "
        last_name = "  Doe  "
        
        with patch.object(
            user_registration_service.email_verification_service,
            'create_verification_token',
            return_value=("test_token", None)
        ), \
        patch.object(
            user_registration_service.email_verification_service,
            'send_verification_email',
            new_callable=AsyncMock
        ):
            
            user = await user_registration_service.register_user(
                db=test_db,
                email=email,
                password=sample_password,
                first_name=first_name,
                last_name=last_name
            )
            
            assert user.first_name == "Jane"
            assert user.last_name == "Doe"
    
    @pytest.mark.asyncio
    async def test_register_user_custom_role(
        self, 
        user_registration_service, 
        test_db, 
        sample_password
    ):
        """Test user registration with custom role."""
        import uuid
        email = f"advisor{uuid.uuid4().hex[:8]}@example.com"
        role = UserRole.ADVISOR
        
        with patch.object(
            user_registration_service.email_verification_service,
            'create_verification_token',
            return_value=("test_token", None)
        ), \
        patch.object(
            user_registration_service.email_verification_service,
            'send_verification_email',
            new_callable=AsyncMock
        ):
            
            user = await user_registration_service.register_user(
                db=test_db,
                email=email,
                password=sample_password,
                role=role
            )
            
            assert user.role == UserRole.ADVISOR
    
    def test_verify_email_success(self, user_registration_service):
        """Test successful email verification."""
        token = "valid_token"
        
        with patch.object(
            user_registration_service.email_verification_service,
            'verify_email_token',
            return_value=True
        ) as mock_verify:
            
            result = user_registration_service.verify_email(token)
            
            assert result is True
            mock_verify.assert_called_once_with(token)
    
    def test_verify_email_failure(self, user_registration_service):
        """Test failed email verification."""
        token = "invalid_token"
        
        with patch.object(
            user_registration_service.email_verification_service,
            'verify_email_token',
            return_value=False
        ) as mock_verify:
            
            result = user_registration_service.verify_email(token)
            
            assert result is False
            mock_verify.assert_called_once_with(token)
    
    @pytest.mark.asyncio
    async def test_resend_verification_email_success(self, user_registration_service):
        """Test successful resend verification email."""
        email = "<EMAIL>"
        
        with patch.object(
            user_registration_service.email_verification_service,
            'resend_verification_email',
            new_callable=AsyncMock,
            return_value=True
        ) as mock_resend:
            
            result = await user_registration_service.resend_verification_email(email)
            
            assert result is True
            mock_resend.assert_called_once_with(email)
    
    def test_get_user_by_email_found(self, user_registration_service, sample_user):
        """Test getting user by email when user exists."""
        user = user_registration_service.get_user_by_email(sample_user.email)
        
        assert user is not None
        assert user.email == sample_user.email
    
    def test_get_user_by_email_not_found(self, user_registration_service):
        """Test getting user by email when user doesn't exist."""
        user = user_registration_service.get_user_by_email("<EMAIL>")
        
        assert user is None
    
    def test_check_email_available_true(self, user_registration_service):
        """Test email availability check when email is available."""
        email = "<EMAIL>"
        
        result = user_registration_service.check_email_available(email)
        
        assert result is True
    
    def test_check_email_available_false(self, user_registration_service, sample_user):
        """Test email availability check when email is taken."""
        result = user_registration_service.check_email_available(sample_user.email)
        
        assert result is False
    
    def test_check_email_available_normalization(self, user_registration_service, sample_user):
        """Test email availability check with normalization."""
        # Test with different case
        email_different_case = sample_user.email.upper()
        
        result = user_registration_service.check_email_available(email_different_case)
        
        assert result is False
    
    def test_get_registration_status_existing_user(self, user_registration_service, sample_user):
        """Test getting registration status for existing user."""
        status = user_registration_service.get_registration_status(sample_user.email)
        
        assert status["exists"] is True
        assert status["email_verified"] == sample_user.email_verified
        assert status["status"] == sample_user.status.value
        assert status["created_at"] is not None
    
    def test_get_registration_status_nonexistent_user(self, user_registration_service):
        """Test getting registration status for nonexistent user."""
        status = user_registration_service.get_registration_status("<EMAIL>")
        
        assert status["exists"] is False
        assert status["email_verified"] is False
        assert status["status"] is None
        assert status["created_at"] is None
    
    def test_validate_registration_data_valid(self, user_registration_service, sample_password):
        """Test validation of valid registration data."""
        email = "<EMAIL>"
        first_name = "John"
        last_name = "Doe"
        
        result = user_registration_service.validate_registration_data(
            email=email,
            password=sample_password,
            first_name=first_name,
            last_name=last_name
        )
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        assert "password_strength" in result
    
    def test_validate_registration_data_duplicate_email(
        self, 
        user_registration_service, 
        sample_user, 
        sample_password
    ):
        """Test validation with duplicate email."""
        result = user_registration_service.validate_registration_data(
            email=sample_user.email,
            password=sample_password
        )
        
        assert result["valid"] is False
        assert any("already registered" in error for error in result["errors"])
    
    def test_validate_registration_data_weak_password(self, user_registration_service, weak_password):
        """Test validation with weak password."""
        email = "<EMAIL>"
        
        result = user_registration_service.validate_registration_data(
            email=email,
            password=weak_password
        )
        
        assert result["valid"] is False
        assert len(result["errors"]) > 0
        assert result["password_strength"]["strength"] == "weak"
    
    def test_validate_registration_data_empty_email(self, user_registration_service, sample_password):
        """Test validation with empty email."""
        result = user_registration_service.validate_registration_data(
            email="",
            password=sample_password
        )
        
        assert result["valid"] is False
        assert any("required" in error for error in result["errors"])
    
    def test_validate_registration_data_long_names(self, user_registration_service, sample_password):
        """Test validation with overly long names."""
        email = "<EMAIL>"
        long_name = "A" * 101  # Over 100 character limit
        
        result = user_registration_service.validate_registration_data(
            email=email,
            password=sample_password,
            first_name=long_name,
            last_name=long_name
        )
        
        assert result["valid"] is False
        assert any("100 characters" in error for error in result["errors"])
    
    def test_validate_registration_data_password_warnings(
        self, 
        user_registration_service
    ):
        """Test validation with password that triggers warnings."""
        email = "<EMAIL>"
        weak_but_valid_password = "Password1!"  # Valid but weak
        
        result = user_registration_service.validate_registration_data(
            email=email,
            password=weak_but_valid_password
        )
        
        assert result["valid"] is True
        # May have warnings about password strength
        assert "warnings" in result