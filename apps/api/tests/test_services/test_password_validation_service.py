import pytest
from app.services.password_validation_service import (
    PasswordValidationService,
    PasswordComplexityError
)


class TestPasswordValidationService:
    """Test cases for PasswordValidationService."""
    
    def test_validate_complexity_valid_password(self, password_service):
        """Test password complexity validation with valid password."""
        valid_password = "SecurePass123!"
        
        # Should not raise exception
        result = password_service.validate_complexity(valid_password)
        assert result is True
    
    def test_validate_complexity_too_short(self, password_service):
        """Test password complexity validation with too short password."""
        short_password = "Short1!"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(short_password)
        
        assert "at least 8 characters" in str(exc_info.value)
    
    def test_validate_complexity_no_uppercase(self, password_service):
        """Test password complexity validation without uppercase."""
        no_upper = "securepass123!"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(no_upper)
        
        assert "uppercase letter" in str(exc_info.value)
    
    def test_validate_complexity_no_lowercase(self, password_service):
        """Test password complexity validation without lowercase."""
        no_lower = "SECUREPASS123!"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(no_lower)
        
        assert "lowercase letter" in str(exc_info.value)
    
    def test_validate_complexity_no_digit(self, password_service):
        """Test password complexity validation without digit."""
        no_digit = "SecurePass!"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(no_digit)
        
        assert "number" in str(exc_info.value)
    
    def test_validate_complexity_no_special(self, password_service):
        """Test password complexity validation without special character."""
        no_special = "SecurePass123"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(no_special)
        
        assert "special character" in str(exc_info.value)
    
    def test_validate_complexity_multiple_errors(self, password_service):
        """Test password complexity validation with multiple errors."""
        bad_password = "weak"
        
        with pytest.raises(PasswordComplexityError) as exc_info:
            password_service.validate_complexity(bad_password)
        
        error_message = str(exc_info.value)
        assert "at least 8 characters" in error_message
        assert "uppercase letter" in error_message
        assert "number" in error_message
        assert "special character" in error_message
    
    def test_hash_password(self, password_service):
        """Test password hashing."""
        password = "TestPassword123!"
        
        hashed = password_service.hash_password(password)
        
        assert hashed is not None
        assert hashed != password
        assert hashed.startswith("$2b$")  # bcrypt prefix
    
    def test_verify_password_correct(self, password_service):
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = password_service.hash_password(password)
        
        result = password_service.verify_password(password, hashed)
        
        assert result is True
    
    def test_verify_password_incorrect(self, password_service):
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = password_service.hash_password(password)
        
        result = password_service.verify_password(wrong_password, hashed)
        
        assert result is False
    
    def test_verify_password_invalid_hash(self, password_service):
        """Test password verification with invalid hash."""
        password = "TestPassword123!"
        invalid_hash = "invalid_hash"
        
        result = password_service.verify_password(password, invalid_hash)
        
        assert result is False
    
    def test_needs_rehash_fresh_hash(self, password_service):
        """Test needs_rehash with fresh hash."""
        password = "TestPassword123!"
        hashed = password_service.hash_password(password)
        
        result = password_service.needs_rehash(hashed)
        
        assert result is False
    
    def test_hash_token(self, password_service):
        """Test token hashing."""
        token = "test_token_12345"
        
        hashed = password_service.hash_token(token)
        
        assert hashed is not None
        assert hashed != token
        assert len(hashed) == 64  # SHA256 hex length
    
    def test_hash_token_consistent(self, password_service):
        """Test token hashing is consistent."""
        token = "test_token_12345"
        
        hash1 = password_service.hash_token(token)
        hash2 = password_service.hash_token(token)
        
        assert hash1 == hash2
    
    def test_generate_secure_token(self, password_service):
        """Test secure token generation."""
        token = password_service.generate_secure_token()
        
        assert token is not None
        assert len(token) > 0
        assert isinstance(token, str)
    
    def test_generate_secure_token_unique(self, password_service):
        """Test secure token generation produces unique tokens."""
        token1 = password_service.generate_secure_token()
        token2 = password_service.generate_secure_token()
        
        assert token1 != token2
    
    def test_generate_secure_token_custom_length(self, password_service):
        """Test secure token generation with custom length."""
        length = 16
        token = password_service.generate_secure_token(length)
        
        # URL-safe base64 encoding, so actual length may vary
        assert len(token) > 0
    
    def test_validate_password_strength_strong(self, password_service):
        """Test password strength validation for strong password."""
        strong_password = "VerySecurePassword123!@#"
        
        result = password_service.validate_password_strength(strong_password)
        
        assert result["strength"] == "strong"
        assert result["score"] >= 4
        assert result["details"]["has_uppercase"] is True
        assert result["details"]["has_lowercase"] is True
        assert result["details"]["has_digit"] is True
        assert result["details"]["has_special"] is True
    
    def test_validate_password_strength_medium(self, password_service):
        """Test password strength validation for medium password."""
        medium_password = "Password1"  # 9 chars, has upper, lower, digit, no special
        
        result = password_service.validate_password_strength(medium_password)
        
        assert result["strength"] == "medium"
        assert 3 <= result["score"] < 4
    
    def test_validate_password_strength_weak(self, password_service):
        """Test password strength validation for weak password."""
        weak_password = "password"
        
        result = password_service.validate_password_strength(weak_password)
        
        assert result["strength"] == "weak"
        assert result["score"] < 3
    
    def test_get_algorithm_info(self, password_service):
        """Test getting algorithm information."""
        info = password_service.get_algorithm_info()
        
        assert info["algorithm"] == "bcrypt"
        assert info["algorithm_version"] == 12
        assert info["min_length"] == 8
        assert info["require_uppercase"] is True
        assert info["require_lowercase"] is True
        assert info["require_digit"] is True
        assert info["require_special"] is True