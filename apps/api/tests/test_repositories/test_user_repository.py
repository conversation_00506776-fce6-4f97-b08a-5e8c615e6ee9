import pytest
from app.models.database.user import User<PERSON><PERSON>, UserStatus
from tests.conftest import create_test_user


class TestUserRepository:
    """Test cases for UserRepository."""
    
    def test_create_user(self, user_repo, test_db, sample_user_data):
        """Test creating a user."""
        user = user_repo.create(sample_user_data)
        test_db.commit()
        
        assert user.id is not None
        assert user.email == sample_user_data["email"]
        assert user.first_name == sample_user_data["first_name"]
        assert user.last_name == sample_user_data["last_name"]
        assert user.role == sample_user_data["role"]
        assert user.status == sample_user_data["status"]
        assert user.email_verified == sample_user_data["email_verified"]
    
    def test_get_by_id(self, user_repo, test_db, sample_user):
        """Test getting user by ID."""
        user = user_repo.get_by_id(sample_user.id)
        
        assert user is not None
        assert user.id == sample_user.id
        assert user.email == sample_user.email
    
    def test_get_by_id_not_found(self, user_repo, test_db):
        """Test getting user by ID when user doesn't exist."""
        import uuid
        non_existent_id = uuid.uuid4()
        
        user = user_repo.get_by_id(non_existent_id)
        
        assert user is None
    
    def test_get_by_email(self, user_repo, test_db, sample_user):
        """Test getting user by email."""
        user = user_repo.get_by_email(sample_user.email)
        
        assert user is not None
        assert user.email == sample_user.email
    
    def test_get_by_email_case_insensitive(self, user_repo, test_db, sample_user):
        """Test getting user by email is case insensitive."""
        user = user_repo.get_by_email(sample_user.email.upper())
        
        assert user is not None
        assert user.email == sample_user.email
    
    def test_get_by_email_not_found(self, user_repo, test_db):
        """Test getting user by email when user doesn't exist."""
        user = user_repo.get_by_email("<EMAIL>")
        
        assert user is None
    
    def test_email_exists_true(self, user_repo, test_db, sample_user):
        """Test email exists check when email exists."""
        exists = user_repo.email_exists(sample_user.email)
        
        assert exists is True
    
    def test_email_exists_false(self, user_repo, test_db):
        """Test email exists check when email doesn't exist."""
        exists = user_repo.email_exists("<EMAIL>")
        
        assert exists is False
    
    def test_email_exists_case_insensitive(self, user_repo, test_db, sample_user):
        """Test email exists check is case insensitive."""
        exists = user_repo.email_exists(sample_user.email.upper())
        
        assert exists is True
    
    def test_get_active_users(self, user_repo, test_db):
        """Test getting active users."""
        # Create active user
        active_user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.ACTIVE
        )
        
        # Create inactive user
        create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.INACTIVE
        )
        
        active_users = user_repo.get_active_users()
        
        assert len(active_users) == 1
        assert active_users[0].id == active_user.id
        assert active_users[0].status == UserStatus.ACTIVE
    
    def test_get_pending_verification_users(self, user_repo, test_db):
        """Test getting users pending verification."""
        # Create pending user
        pending_user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.PENDING_VERIFICATION
        )
        
        # Create active user
        create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.ACTIVE
        )
        
        pending_users = user_repo.get_pending_verification_users()
        
        assert len(pending_users) == 1
        assert pending_users[0].id == pending_user.id
        assert pending_users[0].status == UserStatus.PENDING_VERIFICATION
    
    def test_get_users_by_role(self, user_repo, test_db):
        """Test getting users by role."""
        # Create client user
        client_user = create_test_user(
            test_db, 
            "<EMAIL>",
            role=UserRole.CLIENT
        )
        
        # Create advisor user
        advisor_user = create_test_user(
            test_db, 
            "<EMAIL>",
            role=UserRole.ADVISOR
        )
        
        client_users = user_repo.get_users_by_role(UserRole.CLIENT)
        advisor_users = user_repo.get_users_by_role(UserRole.ADVISOR)
        
        assert len(client_users) == 1
        assert client_users[0].id == client_user.id
        assert len(advisor_users) == 1
        assert advisor_users[0].id == advisor_user.id
    
    def test_get_users_by_status(self, user_repo, test_db):
        """Test getting users by status."""
        # Create active user
        active_user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.ACTIVE
        )
        
        # Create pending user
        pending_user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.PENDING_VERIFICATION
        )
        
        active_users = user_repo.get_users_by_status(UserStatus.ACTIVE)
        pending_users = user_repo.get_users_by_status(UserStatus.PENDING_VERIFICATION)
        
        assert len(active_users) == 1
        assert active_users[0].id == active_user.id
        assert len(pending_users) == 1
        assert pending_users[0].id == pending_user.id
    
    def test_activate_user(self, user_repo, test_db, sample_user):
        """Test activating a user."""
        user = user_repo.activate_user(sample_user)
        test_db.commit()
        
        assert user.status == UserStatus.ACTIVE
        assert user.email_verified is True
    
    def test_deactivate_user(self, user_repo, test_db):
        """Test deactivating a user."""
        # Create active user
        user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.ACTIVE
        )
        
        updated_user = user_repo.deactivate_user(user)
        test_db.commit()
        
        assert updated_user.status == UserStatus.INACTIVE
    
    def test_suspend_user(self, user_repo, test_db):
        """Test suspending a user."""
        # Create active user
        user = create_test_user(
            test_db, 
            "<EMAIL>",
            status=UserStatus.ACTIVE
        )
        
        updated_user = user_repo.suspend_user(user)
        test_db.commit()
        
        assert updated_user.status == UserStatus.SUSPENDED
    
    def test_search_users_by_email_pattern(self, user_repo, test_db):
        """Test searching users by email pattern."""
        # Create test users
        create_test_user(test_db, "<EMAIL>")
        create_test_user(test_db, "<EMAIL>")
        create_test_user(test_db, "<EMAIL>")
        
        # Search for users with "doe" in email
        results = user_repo.search_users(email_pattern="doe")
        
        assert len(results) == 2
        assert all("doe" in user.email for user in results)
    
    def test_search_users_by_name_pattern(self, user_repo, test_db):
        """Test searching users by name pattern."""
        # Create test users
        create_test_user(test_db, "<EMAIL>", first_name="John", last_name="Smith")
        create_test_user(test_db, "<EMAIL>", first_name="Jane", last_name="Johnson")
        create_test_user(test_db, "<EMAIL>", first_name="Admin", last_name="User")
        
        # Search for users with "John" in first name
        results = user_repo.search_users(first_name_pattern="John")
        
        assert len(results) == 1
        assert results[0].first_name == "John"
        
        # Search for users with "John" in last name
        results = user_repo.search_users(last_name_pattern="John")
        
        assert len(results) == 1
        assert results[0].last_name == "Johnson"
    
    def test_search_users_by_role_and_status(self, user_repo, test_db):
        """Test searching users by role and status."""
        # Create test users
        create_test_user(
            test_db, 
            "<EMAIL>",
            role=UserRole.CLIENT,
            status=UserStatus.ACTIVE
        )
        create_test_user(
            test_db, 
            "<EMAIL>",
            role=UserRole.ADVISOR,
            status=UserStatus.ACTIVE
        )
        create_test_user(
            test_db, 
            "<EMAIL>",
            role=UserRole.CLIENT,
            status=UserStatus.PENDING_VERIFICATION
        )
        
        # Search for active clients
        results = user_repo.search_users(
            role=UserRole.CLIENT,
            status=UserStatus.ACTIVE
        )
        
        assert len(results) == 1
        assert results[0].role == UserRole.CLIENT
        assert results[0].status == UserStatus.ACTIVE
    
    def test_search_users_multiple_criteria(self, user_repo, test_db):
        """Test searching users with multiple criteria."""
        # Create test users
        create_test_user(
            test_db, 
            "<EMAIL>",
            first_name="John",
            role=UserRole.CLIENT,
            status=UserStatus.ACTIVE
        )
        create_test_user(
            test_db, 
            "<EMAIL>",
            first_name="John",
            role=UserRole.ADVISOR,
            status=UserStatus.ACTIVE
        )
        
        # Search for active clients named John
        results = user_repo.search_users(
            first_name_pattern="John",
            role=UserRole.CLIENT,
            status=UserStatus.ACTIVE
        )
        
        assert len(results) == 1
        assert results[0].first_name == "John"
        assert results[0].role == UserRole.CLIENT
        assert results[0].status == UserStatus.ACTIVE
    
    def test_update_user(self, user_repo, test_db, sample_user):
        """Test updating user data."""
        update_data = {
            "first_name": "Updated",
            "last_name": "Name"
        }
        
        updated_user = user_repo.update(sample_user, update_data)
        test_db.commit()
        
        assert updated_user.first_name == "Updated"
        assert updated_user.last_name == "Name"
        # Email should remain unchanged
        assert updated_user.email == sample_user.email
    
    def test_delete_user(self, user_repo, test_db, sample_user):
        """Test deleting a user."""
        user_id = sample_user.id
        
        user_repo.delete(sample_user)
        test_db.commit()
        
        # Verify user is deleted
        deleted_user = user_repo.get_by_id(user_id)
        assert deleted_user is None
    
    def test_count_users(self, user_repo, test_db):
        """Test counting users."""
        # Create multiple users
        create_test_user(test_db, "<EMAIL>")
        create_test_user(test_db, "<EMAIL>")
        create_test_user(test_db, "<EMAIL>")
        
        count = user_repo.count()
        
        assert count == 3
    
    def test_get_all_with_pagination(self, user_repo, test_db):
        """Test getting all users with pagination."""
        # Create multiple users
        for i in range(5):
            create_test_user(test_db, f"user{i}@example.com")
        
        # Get first 3 users
        first_page = user_repo.get_all(skip=0, limit=3)
        assert len(first_page) == 3
        
        # Get next 2 users
        second_page = user_repo.get_all(skip=3, limit=3)
        assert len(second_page) == 2
        
        # Verify no overlap
        first_page_ids = {user.id for user in first_page}
        second_page_ids = {user.id for user in second_page}
        assert first_page_ids.isdisjoint(second_page_ids)