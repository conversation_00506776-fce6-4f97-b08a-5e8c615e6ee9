import pytest
from datetime import datetime, timedelta, timezone
from app.repositories.password_reset_repository import PasswordResetTokenRepository
from app.models.database.auth import PasswordResetToken
from tests.conftest import create_test_user


class TestPasswordResetTokenRepository:
    """Test cases for PasswordResetTokenRepository."""
    
    # Test constants
    TEST_IP_ADDRESS = "*************"
    TEST_IP_ADDRESS_2 = "*************" 
    TEST_USER_AGENT = "Mozilla/5.0 Test Browser"
    
    @pytest.fixture
    def reset_repo(self, test_db):
        """Password reset repository fixture."""
        return PasswordResetTokenRepository(test_db)
    
    @pytest.fixture
    def sample_user(self, test_db):
        """Create a sample user for testing."""
        return create_test_user(test_db)
    
    @pytest.fixture
    def sample_token_data(self):
        """Sample token data for testing."""
        return {
            "token_hash": "test_token_hash_123",
            "ip_address": self.TEST_IP_ADDRESS,
            "user_agent": self.TEST_USER_AGENT,
            "expires_at": datetime.now(timezone.utc) + timedelta(hours=1)
        }
    
    def test_create_token(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test creating a password reset token."""
        token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        test_db.commit()
        
        assert token.id is not None
        assert token.user_id == sample_user.id
        assert token.token_hash == sample_token_data["token_hash"]
        # Compare timestamps by converting to UTC if needed
        if token.expires_at.tzinfo is None:
            token_expires_utc = token.expires_at.replace(tzinfo=timezone.utc)
        else:
            token_expires_utc = token.expires_at
        assert token_expires_utc == sample_token_data["expires_at"]
        assert token.ip_address == sample_token_data["ip_address"]
        assert token.user_agent == sample_token_data["user_agent"]
        assert token.is_used is False
        assert token.used_at is None
        assert token.issued_at is not None
        assert token.created_at is not None
    
    def test_get_by_token_hash(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test getting token by hash."""
        created_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        test_db.commit()
        
        found_token = reset_repo.get_by_token_hash(sample_token_data["token_hash"])
        
        assert found_token is not None
        assert found_token.id == created_token.id
        assert found_token.token_hash == sample_token_data["token_hash"]
    
    def test_get_by_token_hash_not_found(self, reset_repo, test_db):
        """Test getting token by hash when token doesn't exist."""
        token = reset_repo.get_by_token_hash("nonexistent_token_hash")
        
        assert token is None
    
    def test_get_valid_token(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test getting a valid (unexpired, unused) token."""
        created_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        test_db.commit()
        
        found_token = reset_repo.get_valid_token(sample_token_data["token_hash"])
        
        assert found_token is not None
        assert found_token.id == created_token.id
        assert found_token.is_used is False
        # Handle timezone-naive comparison
        current_time = datetime.now(timezone.utc)
        if found_token.expires_at.tzinfo is None:
            current_time = datetime.now(timezone.utc).replace(tzinfo=None)
        assert found_token.expires_at > current_time
    
    def test_get_expired_token(self, reset_repo, test_db, sample_user):
        """Test getting an expired token returns None."""
        expired_token_data = {
            "token_hash": "expired_token_hash",
            "ip_address": self.TEST_IP_ADDRESS,
            "user_agent": self.TEST_USER_AGENT,
            "expires_at": datetime.now(timezone.utc) - timedelta(hours=1)  # Expired
        }
        
        reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=expired_token_data["token_hash"],
            expires_at=expired_token_data["expires_at"],
            ip_address=expired_token_data["ip_address"],
            user_agent=expired_token_data["user_agent"]
        )
        test_db.commit()
        
        found_token = reset_repo.get_valid_token(expired_token_data["token_hash"])
        
        assert found_token is None
    
    def test_get_used_token(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test getting a used token returns None."""
        created_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        
        # Mark token as used
        created_token.is_used = True
        created_token.used_at = datetime.now(timezone.utc)
        test_db.commit()
        
        found_token = reset_repo.get_valid_token(sample_token_data["token_hash"])
        
        assert found_token is None
    
    def test_count_recent_requests_by_ip(self, reset_repo, test_db, sample_user):
        """Test counting recent password reset requests by IP address."""
        ip_address = self.TEST_IP_ADDRESS
        
        # Create 3 tokens with the same IP address
        for i in range(3):
            reset_repo.create_token(
                user_id=sample_user.id,
                token_hash=f"token_hash_{i}",
                expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
                ip_address=ip_address,
                user_agent=self.TEST_USER_AGENT
            )
        
        # Create 1 token with different IP address
        reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="different_ip_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS_2,
            user_agent=self.TEST_USER_AGENT
        )
        
        test_db.commit()
        
        count = reset_repo.count_recent_requests_by_ip(ip_address, hours=1)
        
        assert count == 3
    
    def test_count_recent_requests_by_ip_time_filter(self, reset_repo, test_db, sample_user):
        """Test that count_recent_requests_by_ip respects time filter."""
        ip_address = self.TEST_IP_ADDRESS
        
        # Create a token (this will be "recent" within the last hour)
        recent_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="recent_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=ip_address,
            user_agent=self.TEST_USER_AGENT
        )
        
        # Manually set issued_at to 2 hours ago (old token)
        old_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="old_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=ip_address,
            user_agent=self.TEST_USER_AGENT
        )
        old_token.issued_at = datetime.now(timezone.utc) - timedelta(hours=2)
        
        test_db.commit()
        
        # Count requests in last 1 hour
        count = reset_repo.count_recent_requests_by_ip(ip_address, hours=1)
        
        assert count == 1  # Only recent token should be counted
    
    def test_invalidate_user_tokens(self, reset_repo, test_db, sample_user):
        """Test invalidating all active tokens for a user."""
        # Create multiple active tokens for the user
        active_tokens = []
        for i in range(3):
            token = reset_repo.create_token(
                user_id=sample_user.id,
                token_hash=f"active_token_{i}",
                expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
                ip_address=self.TEST_IP_ADDRESS,
                user_agent=self.TEST_USER_AGENT
            )
            active_tokens.append(token)
        
        # Create an already used token (should not be affected)
        used_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="already_used_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        used_token.is_used = True
        used_token.used_at = datetime.now(timezone.utc) - timedelta(minutes=30)
        
        # Create an expired token (should not be affected)
        expired_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="expired_token",
            expires_at=datetime.now(timezone.utc) - timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        
        test_db.commit()
        
        # Invalidate active tokens
        invalidated_count = reset_repo.invalidate_user_tokens(sample_user.id)
        test_db.commit()
        
        assert invalidated_count == 3
        
        # Verify all active tokens are now marked as used
        for token in active_tokens:
            test_db.refresh(token)
            assert token.is_used is True
            assert token.used_at is not None
        
        # Verify already used and expired tokens are unchanged
        test_db.refresh(used_token)
        assert used_token.is_used is True  # Still used
        test_db.refresh(expired_token)
        assert expired_token.is_used is False  # Still not marked as used
    
    def test_invalidate_user_tokens_no_active_tokens(self, reset_repo, test_db, sample_user):
        """Test invalidating tokens when user has no active tokens."""
        invalidated_count = reset_repo.invalidate_user_tokens(sample_user.id)
        
        assert invalidated_count == 0
    
    def test_cleanup_expired_tokens(self, reset_repo, test_db, sample_user):
        """Test cleaning up expired and used tokens."""
        # Create valid active token (should not be deleted)
        active_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="active_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        
        # Create expired token (should be deleted)
        expired_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="expired_token",
            expires_at=datetime.now(timezone.utc) - timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        
        # Create used token (should be deleted)
        used_token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash="used_token",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
            ip_address=self.TEST_IP_ADDRESS,
            user_agent=self.TEST_USER_AGENT
        )
        used_token.is_used = True
        used_token.used_at = datetime.now(timezone.utc)
        
        test_db.commit()
        
        # Cleanup expired/used tokens
        deleted_count = reset_repo.cleanup_expired_tokens()
        test_db.commit()
        
        assert deleted_count == 2
        
        # Verify active token still exists
        found_active = reset_repo.get_by_token_hash("active_token")
        assert found_active is not None
        
        # Verify expired and used tokens are deleted
        found_expired = reset_repo.get_by_token_hash("expired_token")
        assert found_expired is None
        
        found_used = reset_repo.get_by_token_hash("used_token")
        assert found_used is None
    
    def test_cleanup_expired_tokens_no_tokens_to_cleanup(self, reset_repo, test_db):
        """Test cleanup when there are no expired or used tokens."""
        deleted_count = reset_repo.cleanup_expired_tokens()
        
        assert deleted_count == 0
    
    def test_token_relationships(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test that token relationships work correctly."""
        token = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        test_db.commit()
        
        # Test relationship to user
        assert token.user is not None
        assert token.user.id == sample_user.id
        assert token.user.email == sample_user.email
    
    def test_multiple_tokens_same_user(self, reset_repo, test_db, sample_user):
        """Test creating multiple tokens for the same user."""
        tokens = []
        for i in range(3):
            token = reset_repo.create_token(
                user_id=sample_user.id,
                token_hash=f"token_hash_{i}",
                expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
                ip_address=self.TEST_IP_ADDRESS,
                user_agent=self.TEST_USER_AGENT
            )
            tokens.append(token)
        
        test_db.commit()
        
        # All tokens should be created successfully
        for i, token in enumerate(tokens):
            assert token.id is not None
            assert token.token_hash == f"token_hash_{i}"
            assert token.user_id == sample_user.id
        
        # All tokens should be retrievable
        for i in range(3):
            found_token = reset_repo.get_by_token_hash(f"token_hash_{i}")
            assert found_token is not None
            assert found_token.user_id == sample_user.id
    
    def test_token_hash_uniqueness(self, reset_repo, test_db, sample_user, sample_token_data):
        """Test that duplicate token hashes are handled correctly."""
        # Create first token
        token1 = reset_repo.create_token(
            user_id=sample_user.id,
            token_hash=sample_token_data["token_hash"],
            expires_at=sample_token_data["expires_at"],
            ip_address=sample_token_data["ip_address"],
            user_agent=sample_token_data["user_agent"]
        )
        test_db.commit()
        
        # Try to create second token with same hash (should raise integrity error)
        with pytest.raises(Exception):  # SQLAlchemy will raise IntegrityError
            token2 = reset_repo.create_token(
                user_id=sample_user.id,
                token_hash=sample_token_data["token_hash"],  # Same hash
                expires_at=sample_token_data["expires_at"],
                ip_address=sample_token_data["ip_address"],
                user_agent=sample_token_data["user_agent"]
            )
            test_db.commit()