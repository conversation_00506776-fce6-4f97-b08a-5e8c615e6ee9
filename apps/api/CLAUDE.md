# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Docker Compose (Recommended)
```bash
# Note: Run these commands from the finpro/ root directory
docker-compose up -d db api          # Start database and API
docker-compose logs api              # View API logs
docker-compose exec api bash         # Shell into API container
docker-compose exec api alembic upgrade head    # Run migrations (or after pulling DB changes)

# Testing in Docker
docker-compose exec api python -m pytest                                    # Run tests (requires running service)
docker-compose run --rm api python -m pytest                               # Run tests (fresh container, same image)

# After dependency changes (rebuild image first)
docker-compose build api && docker-compose run --rm api python -m pytest   # Rebuild and test

# Specific test commands
docker-compose run --rm api python -m pytest --cov=app --cov-report=html   # With coverage
docker-compose run --rm api python -m pytest tests/test_services/ -v       # Service tests only
docker-compose run --rm api python -m pytest -k "test_user_registration"   # Pattern matching
```

### Local Development
```bash
# Setup (dependency management via Poetry)
poetry install                       # Install dependencies
poetry install --with dev            # Install with dev dependencies
poetry shell                        # Activate virtual environment

# Development server
uvicorn app.main:app --reload --port 8000

# Database migrations
alembic revision --autogenerate -m "Description"  # Create migration
alembic upgrade head                               # Apply migrations
alembic downgrade -1                              # Rollback one migration

# After pulling code with database changes
alembic upgrade head                               # Apply new migrations to existing DB

# Testing
pytest                                             # Run all tests
pytest --cov=app --cov-report=html               # With coverage report
pytest tests/test_services/                       # Run service tests
pytest tests/test_repositories/                   # Run repository tests
pytest tests/test_endpoints/                      # Run endpoint tests
pytest -v tests/test_services/test_user_registration_service.py  # Specific test
pytest -k "test_user_registration"               # Run tests matching pattern

# Code Quality
black app/                                         # Format code
flake8 app/                                       # Lint code
mypy app/                                         # Type checking
```

## Architecture

### API Structure
```
app/
├── api/v1/           # API endpoints and routing
│   ├── endpoints/    # Individual endpoint modules
│   └── router.py     # Main API router
├── core/             # Core utilities (config, security, database)
├── models/           # Data models
│   ├── database/     # SQLAlchemy ORM models
│   └── schemas/      # Pydantic request/response models
├── repositories/     # Data access layer
├── services/         # Business logic layer
├── middleware/       # Custom middleware (rate limiting)
├── templates/email/  # Email templates (HTML and text)
├── utils/           # Utility functions
└── main.py          # Application entry point
```

### Layered Architecture Pattern
- **Endpoints** (`api/v1/endpoints/`) → **Services** (`services/`) → **Repositories** (`repositories/`) → **Database**
- Repository pattern for data access abstraction
- Service layer for business logic separation
- Pydantic v2 models for validation with `@field_validator`

### Key Implementation Details
- **Authentication**: JWT tokens with rate limiting on forgot password
- **Database**: SQLAlchemy 2.0 async patterns with PostgreSQL
- **Email System**: Template-based with HTML/text versions in `app/templates/email/`
- **Testing**: Comprehensive fixtures in `tests/conftest.py` with SQLite in-memory database
- **Configuration**: Pydantic Settings with environment variable support

### Repository Layer
- Extends `BaseRepository` with generic CRUD operations
- Examples: `UserRepository`, `UserPasswordRepository`, `PasswordResetTokenRepository`
- Only layer that directly accesses the database

### Service Layer
- Examples: `UserRegistrationService`, `PasswordResetService`, `EmailVerificationService`
- Contains business logic and orchestrates repository calls
- Handles validation and external service integration (email)

### Models Structure
- **Database models** (`models/database/`): SQLAlchemy ORM classes
- **Schema models** (`models/schemas/`): Pydantic request/response validation
- User system with roles (`UserRole`), status (`UserStatus`), and email verification

### Testing Architecture
- Uses SQLite in-memory database for tests
- Comprehensive fixtures for repositories, services, and test data
- Mock email templates and rate limiting utilities
- Separate test modules for repositories, services, and endpoints

## Technology Stack

- **FastAPI**: High-performance async web framework
- **SQLAlchemy 2.0**: Modern async ORM patterns
- **Alembic**: Database schema migrations
- **Pydantic v2**: Request/response validation with `@field_validator`
- **Poetry**: Dependency management
- **pytest**: Testing with coverage reporting
- **Black/flake8/mypy**: Code quality tools

## Development Standards

- **Type hints required** for all functions
- **Repository → Service → Endpoint** pattern must be followed
- **All schema changes require Alembic migrations**
- **Service and repository layers must have test coverage**
- **Email templates** must include both HTML and text versions
- **Pydantic v2 validation patterns** with `@field_validator` decorators
- **Environment-based configuration** using Pydantic Settings