# Password Reset Testing Implementation Plan

## Overview
Implement comprehensive test coverage for password reset functionality following existing codebase patterns and modern FastAPI best practices.

## Testing Strategy Analysis

### Current Codebase Strengths ✅
- **Excellent foundation**: Uses pytest with comprehensive fixtures
- **Consistent patterns**: Class-based organization, clear naming conventions  
- **Database isolation**: In-memory SQLite with per-test cleanup
- **Async support**: Already handles `@pytest.mark.asyncio` properly
- **Comprehensive mocking**: Template mocking, dependency injection

### Modern FastAPI Additions Needed 🚀
- **API endpoint testing**: First endpoint tests in the codebase
- **Background task testing**: Mock BackgroundTasks for timing consistency
- **Rate limiting testing**: Verify 3/hour IP-based limits
- **Security property testing**: Response consistency, timing attacks

## Implementation Plan

### 1. Repository Tests ⏳
**File**: `tests/test_repositories/test_password_reset_repository.py`
- Follow existing repository test patterns
- Test token creation, validation, cleanup
- Test rate limiting queries (`count_recent_requests_by_ip`)
- Test token invalidation and expiration

**Key Test Cases:**
```python
class TestPasswordResetTokenRepository:
    def test_create_token(self): # Basic token creation
    def test_get_valid_token(self): # Valid token retrieval
    def test_get_expired_token(self): # Expired token handling
    def test_get_used_token(self): # Used token handling
    def test_count_recent_requests_by_ip(self): # Rate limiting
    def test_invalidate_user_tokens(self): # Token invalidation
    def test_cleanup_expired_tokens(self): # Maintenance
```

### 2. Service Tests ⏳
**File**: `tests/test_services/test_password_reset_service.py`
- Follow existing service test patterns (`TestPasswordValidationService`)
- Mock email service and background tasks
- Test timing consistency (background task usage)
- Test template loading with existing mock patterns
- Test user enumeration prevention

**Key Test Cases:**
```python
class TestPasswordResetService:
    @pytest.mark.asyncio
    async def test_initiate_reset_existing_user(self): # Valid user
    
    @pytest.mark.asyncio  
    async def test_initiate_reset_nonexistent_user(self): # Invalid user
    
    def test_token_generation_security(self): # Token security
    def test_token_expiration(self): # 1-hour expiration
    
    @pytest.mark.asyncio
    async def test_email_sending(self): # Email integration
    
    def test_template_loading(self): # Template system
    def test_background_task_usage(self): # Timing consistency
```

### 3. API Endpoint Tests ⏳
**File**: `tests/test_endpoints/test_auth.py`
- **New pattern**: First endpoint tests in codebase
- Use FastAPI TestClient for sync testing
- Test rate limiting (3 requests → 429)
- Test response consistency (existing vs non-existing users)
- Test schema validation and error handling

**Key Test Cases:**
```python
class TestForgotPasswordEndpoint:
    def test_forgot_password_success(self): # Valid request
    def test_forgot_password_nonexistent_email(self): # Invalid email
    def test_forgot_password_invalid_email_format(self): # Bad format
    def test_rate_limiting(self): # 3 requests → 429
    def test_response_consistency(self): # Same response always
    def test_response_timing_consistency(self): # Timing attacks
    def test_request_validation(self): # Pydantic validation
```

### 4. Security Tests ⏳
**File**: `tests/test_security/test_password_reset_security.py`
- Test timing attack prevention
- Test user enumeration prevention
- Test rate limiting bypass attempts  
- Test token security properties

**Key Test Cases:**
```python
class TestPasswordResetSecurity:
    def test_no_user_enumeration(self): # Same responses
    def test_timing_attack_prevention(self): # Consistent timing
    def test_rate_limiting_by_ip(self): # IP-based limits
    def test_token_uniqueness(self): # Token collision resistance
    def test_token_unpredictability(self): # Crypto security
    def test_expired_token_rejection(self): # Time-based security
```

### 5. Integration Tests ⏳
**File**: `tests/test_integration/test_password_reset_flow.py`
- End-to-end password reset flow
- Database + service + endpoint integration
- Email template integration
- Background task integration

**Key Test Cases:**
```python
class TestPasswordResetIntegration:
    @pytest.mark.asyncio
    async def test_complete_reset_flow(self): # Full E2E
    
    @pytest.mark.asyncio
    async def test_email_template_integration(self): # Templates work
    
    def test_database_integration(self): # DB operations
    def test_middleware_integration(self): # Rate limiting middleware
```

## Fixtures Strategy

### New Fixtures to Add to `conftest.py`
```python
# Password reset specific fixtures
@pytest.fixture
def reset_repo(test_db: Session) -> PasswordResetTokenRepository:
    return PasswordResetTokenRepository(test_db)

@pytest.fixture
def password_reset_service(...) -> PasswordResetService:
    return PasswordResetService(...)

@pytest.fixture
def mock_background_tasks():
    # Mock BackgroundTasks for testing
    
@pytest.fixture  
def mock_reset_email_templates(tmp_path):
    # Create password reset templates
    
@pytest.fixture
def rate_limit_test_client():
    # TestClient with rate limiting enabled
```

### Template Mocking Pattern
Follow existing `mock_email_templates` pattern:
```python
@pytest.fixture
def mock_reset_templates(tmp_path):
    template_dir = tmp_path / "templates" / "email" 
    template_dir.mkdir(parents=True)
    
    # Create password_reset.html
    html_content = "<html>Reset: {reset_url}</html>"
    (template_dir / "password_reset.html").write_text(html_content)
    
    # Create password_reset.txt  
    txt_content = "Reset: {reset_url}"
    (template_dir / "password_reset.txt").write_text(txt_content)
    
    return template_dir
```

## Testing Approaches by Layer

### Repository Layer Testing
- **Database operations**: SQLite in-memory testing
- **Query validation**: Ensure correct SQL generation  
- **Data integrity**: Foreign key constraints, cascades
- **Performance**: Query efficiency, index usage

### Service Layer Testing  
- **Business logic**: Core password reset workflow
- **Dependency mocking**: Email service, repositories
- **Template integration**: Email template loading
- **Background tasks**: Async processing simulation
- **Error handling**: Exception scenarios

### API Layer Testing
- **HTTP interface**: Request/response validation
- **Status codes**: 200 OK, 429 rate limited
- **Schema validation**: Pydantic model validation
- **Middleware integration**: Rate limiting behavior
- **Security headers**: Proper HTTP headers

### Security Layer Testing
- **Timing attacks**: Response time consistency
- **User enumeration**: Response content consistency  
- **Rate limiting**: IP-based request limiting
- **Token security**: Cryptographic properties
- **Session management**: Background task isolation

### Integration Testing
- **End-to-end flows**: Complete user journeys
- **Component interaction**: Service + DB + HTTP
- **External dependencies**: Email service integration
- **Configuration**: Settings and environment variables

## Key Testing Principles

### 1. Security-First Testing
- Every test considers security implications
- Timing attack prevention verification
- User enumeration prevention validation
- Rate limiting effectiveness testing

### 2. Isolation and Independence  
- Each test is completely independent
- Database cleanup between tests
- Mock external dependencies
- No shared state between tests

### 3. Realistic Scenarios
- Test real-world usage patterns
- Include edge cases and error conditions
- Validate against actual security threats
- Test production-like configurations

### 4. Maintainable Test Code
- Follow existing codebase patterns
- Clear, descriptive test names
- Comprehensive documentation
- DRY principles with fixtures

## Success Criteria

### Test Coverage Goals
- **Repository Layer**: 100% line coverage
- **Service Layer**: 100% line coverage  
- **API Layer**: 100% endpoint coverage
- **Security Properties**: All security requirements tested
- **Integration**: Critical user flows covered

### Quality Gates
- All tests pass consistently
- No flaky tests (timing-dependent)
- Fast execution (< 30 seconds total)
- Clear failure messages
- Easy to run locally and in CI

### Security Validation
- ✅ No user enumeration possible
- ✅ Timing attacks prevented  
- ✅ Rate limiting effective
- ✅ Tokens cryptographically secure
- ✅ Background tasks isolate processing

This comprehensive testing plan ensures our password reset functionality is bulletproof, secure, and maintainable while following the excellent patterns already established in the codebase.