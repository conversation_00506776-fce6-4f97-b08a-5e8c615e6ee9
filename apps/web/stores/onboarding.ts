'use client'

import { create } from 'zustand'
import { persist, PersistStorage } from 'zustand/middleware'
import { OnboardingStepId, OnboardingFormData } from '../app/(onboarding)/types'
import { ONBOARDING_STEPS } from '../app/(onboarding)/constants'

interface OnboardingStore {
  // Core state
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>

  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  markStepComplete: (step: OnboardingStepId) => void
  resetOnboarding: () => void
}

const storage: PersistStorage<OnboardingStore> = {
  getItem: (name) => {
    const str = typeof window !== 'undefined' ? localStorage.getItem(name) : null
    if (!str) return null
    const { state, version } = JSON.parse(str)
    return {
      state: {
        ...state,
        completedSteps: new Set(state.completedSteps),
      },
      version,
    }
  },
  setItem: (name, value) => {
    if (typeof window !== 'undefined') {
      const { state, version } = value
      const serializedState = {
        state: {
          ...state,
          completedSteps: Array.from(state.completedSteps),
        },
        version,
      }
      localStorage.setItem(name, JSON.stringify(serializedState))
    }
  },
  removeItem: (name) => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(name)
    }
  },
}

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => ({
      // Initial state
      data: {},
      completedSteps: new Set<OnboardingStepId>(),

      // Actions
      updateData: (section, data) =>
        set(state => ({
          data: { ...state.data, [section]: data }
        })),

      markStepComplete: (step) =>
        set(state => ({
          completedSteps: new Set([...Array.from(state.completedSteps), step])
        })),

      resetOnboarding: () => set({
        data: {},
        completedSteps: new Set<OnboardingStepId>()
      })
    }),
    {
      name: 'onboarding-storage',
      storage,
    }
  )
)

// Computed selectors
export const useOnboardingProgress = () => {
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  return Math.round((completedSteps.size / ONBOARDING_STEPS.length) * 100)
}


