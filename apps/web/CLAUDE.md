# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Available UI Components

Import components from `@finpro/ui`:

```tsx
import { Button, Input, Dialog } from '@finpro/ui';
```

### Core Components

**Button**
- `Button` - Primary button component with variants and loading states
- Props: `ButtonProps`

**Form Controls**
- `Input` - Text input field
- `Textarea` - Multi-line text input
- `Checkbox` - Checkbox with label support
- `Switch` - Toggle switch component
- `RadioGroup`, `RadioGroupItem`, `RadioGroupOption` - Radio button groups
- `Slider` - Range slider input
- `Label` - Form field labels
- Props: `CheckboxProps`, `SwitchProps`, `LabelProps`

**Select & Dropdown**
- `Select`, `SelectGroup`, `SelectValue`, `SelectTrigger`, `SelectContent`, `SelectLabel`, `SelectItem`, `SelectSeparator`, `SelectScrollUpButton`, `SelectScrollDownButton` - Select dropdown
- `DropdownMenu`, `DropdownMenuTrigger`, `DropdownMenuContent`, `DropdownMenuItem`, `DropdownMenuCheckboxItem`, `DropdownMenuRadioItem`, `DropdownMenuLabel`, `DropdownMenuSeparator`, `DropdownMenuShortcut`, `DropdownMenuGroup`, `DropdownMenuPortal`, `DropdownMenuSub`, `DropdownMenuSubContent`, `DropdownMenuSubTrigger`, `DropdownMenuRadioGroup` - Context dropdown menus

### Layout Components

**Dialog & Modals**
- `Dialog`, `DialogPortal`, `DialogOverlay`, `DialogClose`, `DialogTrigger`, `DialogContent`, `DialogHeader`, `DialogFooter`, `DialogTitle`, `DialogDescription` - Modal dialogs
- `AlertDialog`, `AlertDialogPortal`, `AlertDialogOverlay`, `AlertDialogTrigger`, `AlertDialogContent`, `AlertDialogHeader`, `AlertDialogFooter`, `AlertDialogTitle`, `AlertDialogDescription`, `AlertDialogAction`, `AlertDialogCancel` - Alert confirmation dialogs

**Overlays**
- `Popover`, `PopoverTrigger`, `PopoverContent`, `PopoverAnchor` - Floating content
- `Tooltip`, `TooltipTrigger`, `TooltipContent`, `TooltipProvider` - Hover tooltips
- `HoverCard`, `HoverCardTrigger`, `HoverCardContent`, `HoverCardArrow`, `HoverCardHeader`, `HoverCardAvatar`, `HoverCardTitle`, `HoverCardDescription`, `HoverCardFooter` - Rich hover cards
- Props: `HoverCardContentProps`

**Containers**
- `Tabs`, `TabsList`, `TabsTrigger`, `TabsContent` - Tab navigation
- `Accordion`, `AccordionItem`, `AccordionTrigger`, `AccordionContent` - Collapsible sections
- `Collapsible`, `CollapsibleTrigger`, `CollapsibleContent` - Simple show/hide content
- `ScrollArea`, `ScrollAreaViewport`, `ScrollAreaScrollbar`, `ScrollAreaThumb`, `ScrollAreaCorner` - Custom scrollbars
- Props: `CollapsibleProps`, `CollapsibleTriggerProps`, `CollapsibleContentProps`, `ScrollAreaProps`, `ScrollAreaScrollbarProps`, `ScrollAreaThumbProps`

### Navigation Components

**Menus**
- `NavigationMenu`, `NavigationMenuList`, `NavigationMenuItem`, `NavigationMenuTrigger`, `NavigationMenuContent`, `NavigationMenuLink`, `NavigationMenuIndicator`, `NavigationMenuViewport`, `NavigationMenuSub`, `NavigationMenuLinkItem`, `NavigationMenuGrid`, `NavigationMenuGridItem` - Complex navigation menus
- `ContextMenu`, `ContextMenuTrigger`, `ContextMenuContent`, `ContextMenuItem`, `ContextMenuCheckboxItem`, `ContextMenuRadioItem`, `ContextMenuLabel`, `ContextMenuSeparator`, `ContextMenuShortcut`, `ContextMenuGroup`, `ContextMenuPortal`, `ContextMenuSub`, `ContextMenuSubContent`, `ContextMenuSubTrigger`, `ContextMenuRadioGroup` - Right-click context menus

**Command Interface**
- `Command`, `CommandInput`, `CommandList`, `CommandEmpty`, `CommandGroup`, `CommandItem`, `CommandShortcut`, `CommandSeparator`, `CommandDialog` - Command palette/search interface
- Props: `CommandProps`

### Data Display

**Media**
- `Avatar`, `AvatarImage`, `AvatarFallback` - User profile pictures
- `AspectRatio` - Maintain aspect ratios for media
- Props: `AspectRatioProps`

**Feedback**
- `Progress`, `CircularProgress` - Progress indicators
- `Toast`, `ToastProvider`, `ToastViewport`, `ToastTitle`, `ToastDescription`, `ToastClose`, `ToastAction` - Toast notifications

**Loading States**
- `Spinner` - Loading spinner
- `LoadingDots` - Animated loading dots
- `Skeleton` - Skeleton loading placeholders
- Props: `SpinnerProps`, `LoadingDotsProps`, `SkeletonProps`

### Utility Components

**Form Helpers**
- `Form`, `FormField`, `FormLabel`, `FormControl`, `FormMessage`, `FormSubmit`, `FormDescription`, `FormValidityState` - Form validation and structure
- Props: `FormFieldProps`, `FormLabelProps`, `FormControlProps`, `FormMessageProps`, `FormSubmitProps`, `FormDescriptionProps`

**UI Elements**
- `Separator` - Visual dividers
- `Toggle` - Toggle button
- `ToggleGroup`, `ToggleMultiGroup`, `ToggleGroupItem` - Toggle button groups
- `VisuallyHidden` - Screen reader only content
- Props: `SeparatorProps`, `ToggleProps`, `ToggleGroupSingleProps`, `ToggleGroupMultipleProps`, `ToggleGroupItemProps`, `VisuallyHiddenProps`

## Usage Examples

### Basic Form
```tsx
import { Button, Input, Label, Form } from '@finpro/ui';

function LoginForm() {
  return (
    <Form>
      <div className="space-y-4">
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" />
        </div>
        <div>
          <Label htmlFor="password">Password</Label>
          <Input id="password" type="password" />
        </div>
        <Button type="submit">Sign In</Button>
      </div>
    </Form>
  );
}
```

### Modal Dialog
```tsx
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription, Button } from '@finpro/ui';

function ConfirmDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="destructive">Delete</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
```

### Data Loading
```tsx
import { Skeleton, Spinner, LoadingDots } from '@finpro/ui';

function LoadingStates() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-4 w-full" />
      <Spinner size="lg" />
      <LoadingDots />
    </div>
  );
}
```

All components are built with Radix UI primitives for accessibility and include comprehensive TypeScript support.