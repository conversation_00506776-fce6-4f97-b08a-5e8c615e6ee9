'use client'

import React from 'react'
import { DollarSign } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'
import { 
  Card, 
  CardHeader, 
  CardContent, 
  CardFooter, 
  CardTitle, 
  CardDescription 
} from '@finpro/ui'
import { Badge } from '@finpro/ui'
import { 
  Asset, 
  formatCurrency, 
  getAssetTypeLabel, 
  getAssetTypeColor,
} from '@/domain/assets'


const assetCardVariants = cva(
  'transition-all duration-200 hover:shadow-lg',
  {
    variants: {
      variant: {
        default: '',
        compact: 'p-4',
        detailed: 'border-2',
        wide: 'p-4',
      },
      size: {
        default: '',
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-lg',
      },
      performance: {
        positive: 'border-green-200 hover:border-green-300',
        negative: 'border-red-200 hover:border-red-300',
        neutral: 'border-gray-200 hover:border-gray-300',
      },
    },
    defaultVariants: {
      variant: 'default',
      performance: 'neutral',
    },
  }
)

interface AssetCardProps 
  extends Omit<React.ComponentProps<typeof Card>, 'onClick'>,
    VariantProps<typeof assetCardVariants> {
  asset: Asset
  onClick?: (asset: Asset) => void
}


export function AssetCard({ 
  asset, 
  onClick, 
  className,
  variant,
  size,
  performance,
  ...props 
}: AssetCardProps) {
  // Check if this is an investment asset
  const isInvestmentAsset = asset.assetType === 'investment_account'
  
  // Performance calculations removed per domain model requirements
  const performanceVariant = performance || 'neutral'

  const handleClick = () => {
    if (onClick) {
      onClick(asset)
    }
  }

  if (variant === 'wide') {
    return (
      <Card 
        className={clsx(
          assetCardVariants({ 
            variant, 
            size, 
            performance: performanceVariant 
          }),
          'flex-row items-center min-h-[80px]',
          onClick && 'cursor-pointer',
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {/* Left Section: Asset Identity */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-semibold text-base truncate">
                {asset.name}
              </h4>
              <Badge size="sm" className={getAssetTypeColor(asset.assetType)}>
                {getAssetTypeLabel(asset.assetType)}
              </Badge>
            </div>
            {asset.description && (
              <p className="text-sm text-muted-foreground">
                {asset.description}
              </p>
            )}
          </div>
        </div>

        {/* Center Section: Additional Info */}
        {asset.institutionName && (
          <div className="flex items-center gap-6 px-4">
            <div className="text-right">
              <div className="text-xs text-muted-foreground mb-1">Institution</div>
              <div className="font-medium">{asset.institutionName}</div>
            </div>
          </div>
        )}

        {/* Right Section: Values */}
        <div className="flex items-center gap-8 px-4">
          {asset.purchaseValue && (
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Purchase Value</div>
              <div className="font-medium">{formatCurrency(asset.purchaseValue)}</div>
            </div>
          )}
          <div className="text-center">
            <div className="text-xs text-muted-foreground mb-1">Current Value</div>
            <div className="font-bold text-lg">{formatCurrency(asset.currentValue)}</div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card 
      className={clsx(
        assetCardVariants({ 
          variant, 
          size, 
          performance: performanceVariant 
        }),
        onClick && 'cursor-pointer',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">
              {asset.name}
            </CardTitle>
            {asset.description && (
              <CardDescription className="text-sm text-muted-foreground">
                {asset.description}
              </CardDescription>
            )}
          </div>
          <Badge size="sm" className={getAssetTypeColor(asset.assetType)}>
            {getAssetTypeLabel(asset.assetType)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className={clsx(
        'space-y-4',
        variant === 'compact' && 'space-y-2'
      )}>
        {/* Current Value */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Current Value</span>
          </div>
          <span className={clsx(
            'font-bold',
            variant === 'compact' ? 'text-lg' : 'text-xl'
          )}>
            {formatCurrency(asset.currentValue)}
          </span>
        </div>

        {/* Purchase Value */}
        {asset.purchaseValue && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Purchase Value</span>
            <span className="font-medium">
              {formatCurrency(asset.purchaseValue)}
            </span>
          </div>
        )}

        {/* Institution */}
        {asset.institutionName && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Institution</span>
            <span className="font-medium">{asset.institutionName}</span>
          </div>
        )}

        {/* Account Number */}
        {asset.accountNumber && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Account</span>
            <span className="font-medium font-mono">{asset.accountNumber}</span>
          </div>
        )}

        {/* Ownership Information */}
        {(asset.ownershipType || asset.ownershipPercentage) && (
          <div className={clsx(
            'grid gap-4 pt-2 border-t',
            asset.ownershipType && asset.ownershipPercentage ? 'grid-cols-2' : 'grid-cols-1',
            variant === 'compact' && 'pt-1'
          )}>
            {asset.ownershipType && (
              <div>
                <div className="text-xs text-muted-foreground">Ownership</div>
                <div className="font-medium capitalize">{asset.ownershipType.replace('_', ' ')}</div>
              </div>
            )}
            {asset.ownershipPercentage && (
              <div>
                <div className="text-xs text-muted-foreground">Percentage</div>
                <div className="font-medium">{asset.ownershipPercentage}%</div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {variant !== 'compact' && asset.purchaseDate && (
        <CardFooter className="pt-2">
          <div className="w-full">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Purchase Date</span>
              <span>{new Date(asset.purchaseDate).toLocaleDateString()}</span>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  )
}