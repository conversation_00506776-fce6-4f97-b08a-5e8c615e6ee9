'use client'

import React, { useState } from 'react'
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormField,
  FormLabel,
  FormControl,
  FormMessage,
  Input,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@finpro/ui'
import { 
  Landmark, 
  TrendingUp, 
  PiggyBank, 
  Home, 
  Car, 
  Gem, 
  Building2, 
  FileText 
} from 'lucide-react'
import { AssetType } from '@/domain/assets'

interface AddAssetDialogProps {
  open: boolean
  onClose: () => void
}

type FormData = {
  // Common fields
  name: string
  currentValue: string
  description?: string
  institutionName?: string
  accountNumber?: string
  purchaseValue?: string
  purchaseDate?: string
  
  // Investment account fields
  accountType?: string
  brokerageName?: string
  
  // Bank account fields
  // accountType used for both investment and bank
  
  // Retirement account fields
  planName?: string
  employerName?: string
  
  // Real estate fields
  addressLine1?: string
  city?: string
  stateProvince?: string
  postalCode?: string
  propertyType?: string
  
  // Vehicle fields
  vehicleType?: string
  year?: string
  make?: string
  model?: string
  vin?: string
  licensePlate?: string
  
  // Personal property fields
  itemDescription?: string
  
  // Business interest fields
  businessName?: string
  businessType?: string
  taxId?: string
  ownershipPercentage?: string
  
  // Other asset fields
  assetTypeDescription?: string
}

const assetTypes = [
  {
    assetType: 'bank_account' as AssetType,
    title: 'Bank Account',
    description: 'Checking, savings, CDs, and money market accounts',
    IconComponent: Landmark
  },
  {
    assetType: 'investment_account' as AssetType,
    title: 'Investment Account',
    description: 'Brokerage accounts, mutual funds, and investment portfolios',
    IconComponent: TrendingUp
  },
  {
    assetType: 'retirement_account' as AssetType,
    title: 'Retirement Account',
    description: '401(k), IRA, pension, and other retirement accounts',
    IconComponent: PiggyBank
  },
  {
    assetType: 'real_estate' as AssetType,
    title: 'Real Estate',
    description: 'Residential, commercial, and investment properties',
    IconComponent: Home
  },
  {
    assetType: 'vehicle' as AssetType,
    title: 'Vehicle',
    description: 'Cars, trucks, boats, motorcycles, and other vehicles',
    IconComponent: Car
  },
  {
    assetType: 'personal_property' as AssetType,
    title: 'Personal Property',
    description: 'Jewelry, art, collectibles, and valuable items',
    IconComponent: Gem
  },
  {
    assetType: 'business_interest' as AssetType,
    title: 'Business Interest',
    description: 'Ownership stakes in businesses and partnerships',
    IconComponent: Building2
  },
  {
    assetType: 'other' as AssetType,
    title: 'Other Asset',
    description: 'Cash, intellectual property, and miscellaneous assets',
    IconComponent: FileText
  }
]

export function AddAssetDialog({ open, onClose }: AddAssetDialogProps) {
  const [stage, setStage] = useState<1 | 2>(1)
  const [selectedAssetType, setSelectedAssetType] = useState<typeof assetTypes[0] | null>(null)
  const [formData, setFormData] = useState<FormData>({
    name: '',
    currentValue: ''
  })

  const handleAssetTypeSelect = (assetType: typeof assetTypes[0]) => {
    setSelectedAssetType(assetType)
    setStage(2)
  }

  const handleBack = () => {
    setStage(1)
    setSelectedAssetType(null)
  }

  const handleClose = () => {
    setStage(1)
    setSelectedAssetType(null)
    setFormData({ name: '', currentValue: '' })
    onClose()
  }

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    
    // Create asset object (for now just log it)
    const assetData = {
      assetType: selectedAssetType?.assetType,
      ...formData,
      currentValue: parseFloat(formData.currentValue) || 0,
      purchaseValue: formData.purchaseValue ? parseFloat(formData.purchaseValue) : undefined,
      ownershipPercentage: formData.ownershipPercentage ? parseFloat(formData.ownershipPercentage) : undefined
    }
    
    console.log('Asset to be created:', assetData)
    
    // Close dialog and reset
    handleClose()
  }

  const renderStage1 = () => (
    <>
      <DialogHeader>
        <DialogTitle>Add New Asset</DialogTitle>
      </DialogHeader>
      <div className="grid grid-cols-2 gap-4 py-4">
        {assetTypes.map((assetType) => (
          <Card 
            key={assetType.assetType}
            className="cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => handleAssetTypeSelect(assetType)}
          >
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <assetType.IconComponent className="h-5 w-5" />
                {assetType.title}
              </CardTitle>
              <CardContent className="text-sm">
                {assetType.description}
              </CardContent>
            </CardHeader>
          </Card>
        ))}
      </div>
      <DialogFooter>
        <Button variant="ghost" onClick={handleClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  )

  const renderStage2 = () => {
    if (!selectedAssetType) return null

    return (
      <>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <selectedAssetType.IconComponent className="h-5 w-5" />
            Add {selectedAssetType.title}
          </DialogTitle>
        </DialogHeader>
        <Form onSubmit={handleFormSubmit} className="space-y-4 py-4">
          {/* Common fields for all assets */}
          <FormField name="name">
            <FormLabel required>Asset Name</FormLabel>
            <FormControl asChild>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter asset name"
                required
              />
            </FormControl>
            <FormMessage match="valueMissing">Asset name is required</FormMessage>
          </FormField>

          <FormField name="currentValue">
            <FormLabel required>Current Value</FormLabel>
            <FormControl asChild>
              <Input
                type="number"
                step="0.01"
                value={formData.currentValue}
                onChange={(e) => setFormData(prev => ({ ...prev, currentValue: e.target.value }))}
                placeholder="0.00"
                required
              />
            </FormControl>
            <FormMessage match="valueMissing">Current value is required</FormMessage>
          </FormField>

          {/* Common fields */}
          <FormField name="description">
            <FormLabel>Description</FormLabel>
            <FormControl asChild>
              <Input
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of the asset"
              />
            </FormControl>
          </FormField>

          <FormField name="purchaseValue">
            <FormLabel>Purchase Value</FormLabel>
            <FormControl asChild>
              <Input
                type="number"
                step="0.01"
                value={formData.purchaseValue || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, purchaseValue: e.target.value }))}
                placeholder="0.00"
              />
            </FormControl>
          </FormField>

          <FormField name="purchaseDate">
            <FormLabel>Purchase Date</FormLabel>
            <FormControl asChild>
              <Input
                type="date"
                value={formData.purchaseDate || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, purchaseDate: e.target.value }))}
              />
            </FormControl>
          </FormField>

          {/* Institution and account fields for financial assets */}
          {(['investment_account', 'bank_account', 'retirement_account'].includes(selectedAssetType.assetType)) && (
            <>
              <FormField name="institutionName">
                <FormLabel>Institution Name</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.institutionName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, institutionName: e.target.value }))}
                    placeholder="Financial institution name"
                  />
                </FormControl>
              </FormField>

              <FormField name="accountNumber">
                <FormLabel>Account Number</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.accountNumber || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, accountNumber: e.target.value }))}
                    placeholder="****1234 (last 4 digits recommended)"
                  />
                </FormControl>
              </FormField>
            </>
          )}

          {/* Investment-specific fields */}
          {selectedAssetType.assetType === 'investment_account' && (
            <>
              <FormField name="accountType">
                <FormLabel>Account Type</FormLabel>
                <Select value={formData.accountType} onValueChange={(value) => setFormData(prev => ({ ...prev, accountType: value }))}>
                  <SelectTrigger>
                    <FormControl asChild>
                      <SelectValue placeholder="Select account type" />
                    </FormControl>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brokerage">Brokerage</SelectItem>
                    <SelectItem value="mutual_fund">Mutual Fund</SelectItem>
                    <SelectItem value="529">529 Plan</SelectItem>
                    <SelectItem value="custodial">Custodial</SelectItem>
                    <SelectItem value="trust">Trust</SelectItem>
                  </SelectContent>
                </Select>
              </FormField>
              
              <FormField name="brokerageName">
                <FormLabel>Brokerage Name</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.brokerageName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, brokerageName: e.target.value }))}
                    placeholder="e.g., Fidelity, Charles Schwab"
                  />
                </FormControl>
              </FormField>
            </>
          )}

          {/* Bank account specific fields */}
          {selectedAssetType.assetType === 'bank_account' && (
            <FormField name="accountType">
              <FormLabel>Account Type</FormLabel>
              <Select value={formData.accountType} onValueChange={(value) => setFormData(prev => ({ ...prev, accountType: value }))}>
                <SelectTrigger>
                  <FormControl asChild>
                    <SelectValue placeholder="Select account type" />
                  </FormControl>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="checking">Checking</SelectItem>
                  <SelectItem value="savings">Savings</SelectItem>
                  <SelectItem value="money_market">Money Market</SelectItem>
                  <SelectItem value="certificate_of_deposit">Certificate of Deposit</SelectItem>
                </SelectContent>
              </Select>
            </FormField>
          )}

          {/* Retirement account specific fields */}
          {selectedAssetType.assetType === 'retirement_account' && (
            <>
              <FormField name="accountType">
                <FormLabel>Account Type</FormLabel>
                <Select value={formData.accountType} onValueChange={(value) => setFormData(prev => ({ ...prev, accountType: value }))}>
                  <SelectTrigger>
                    <FormControl asChild>
                      <SelectValue placeholder="Select retirement account type" />
                    </FormControl>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="401k">401(k)</SelectItem>
                    <SelectItem value="ira">Traditional IRA</SelectItem>
                    <SelectItem value="roth_ira">Roth IRA</SelectItem>
                    <SelectItem value="sep_ira">SEP IRA</SelectItem>
                    <SelectItem value="simple_ira">SIMPLE IRA</SelectItem>
                    <SelectItem value="pension">Pension</SelectItem>
                  </SelectContent>
                </Select>
              </FormField>
              
              <FormField name="planName">
                <FormLabel>Plan Name</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.planName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, planName: e.target.value }))}
                    placeholder="e.g., Company 401(k) Plan"
                  />
                </FormControl>
              </FormField>
              
              <FormField name="employerName">
                <FormLabel>Employer Name</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.employerName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, employerName: e.target.value }))}
                    placeholder="Employer or plan sponsor name"
                  />
                </FormControl>
              </FormField>
            </>
          )}

          {/* Real estate specific fields */}
          {selectedAssetType.assetType === 'real_estate' && (
            <>
              <FormField name="addressLine1">
                <FormLabel>Street Address</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.addressLine1 || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, addressLine1: e.target.value }))}
                    placeholder="123 Main Street"
                  />
                </FormControl>
              </FormField>
              
              <div className="grid grid-cols-3 gap-4">
                <FormField name="city">
                  <FormLabel>City</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.city || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      placeholder="New York"
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="stateProvince">
                  <FormLabel>State</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.stateProvince || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, stateProvince: e.target.value }))}
                      placeholder="NY"
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="postalCode">
                  <FormLabel>Zip Code</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.postalCode || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                      placeholder="10001"
                    />
                  </FormControl>
                </FormField>
              </div>
              
              <FormField name="propertyType">
                <FormLabel>Property Type</FormLabel>
                <Select value={formData.propertyType} onValueChange={(value) => setFormData(prev => ({ ...prev, propertyType: value }))}>
                  <SelectTrigger>
                    <FormControl asChild>
                      <SelectValue placeholder="Select property type" />
                    </FormControl>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single_family">Single Family Home</SelectItem>
                    <SelectItem value="multi_family">Multi-Family</SelectItem>
                    <SelectItem value="condo">Condominium</SelectItem>
                    <SelectItem value="townhouse">Townhouse</SelectItem>
                    <SelectItem value="commercial">Commercial</SelectItem>
                    <SelectItem value="land">Land</SelectItem>
                  </SelectContent>
                </Select>
              </FormField>
            </>
          )}

          {/* Vehicle specific fields */}
          {selectedAssetType.assetType === 'vehicle' && (
            <>
              <FormField name="vehicleType">
                <FormLabel>Vehicle Type</FormLabel>
                <Select value={formData.vehicleType} onValueChange={(value) => setFormData(prev => ({ ...prev, vehicleType: value }))}>
                  <SelectTrigger>
                    <FormControl asChild>
                      <SelectValue placeholder="Select vehicle type" />
                    </FormControl>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="car">Car</SelectItem>
                    <SelectItem value="truck">Truck</SelectItem>
                    <SelectItem value="motorcycle">Motorcycle</SelectItem>
                    <SelectItem value="boat">Boat</SelectItem>
                    <SelectItem value="rv">RV</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </FormField>
              
              <div className="grid grid-cols-3 gap-4">
                <FormField name="year">
                  <FormLabel>Year</FormLabel>
                  <FormControl asChild>
                    <Input
                      type="number"
                      value={formData.year || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
                      placeholder="2024"
                      min="1900"
                      max="2030"
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="make">
                  <FormLabel>Make</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.make || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, make: e.target.value }))}
                      placeholder="Toyota"
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="model">
                  <FormLabel>Model</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.model || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}
                      placeholder="Camry"
                    />
                  </FormControl>
                </FormField>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField name="vin">
                  <FormLabel>VIN</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.vin || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, vin: e.target.value }))}
                      placeholder="1HGBH41JXMN109186"
                      maxLength={17}
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="licensePlate">
                  <FormLabel>License Plate</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.licensePlate || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, licensePlate: e.target.value }))}
                      placeholder="ABC-1234"
                    />
                  </FormControl>
                </FormField>
              </div>
            </>
          )}

          {/* Personal property specific fields */}
          {selectedAssetType.assetType === 'personal_property' && (
            <FormField name="itemDescription">
              <FormLabel>Item Description</FormLabel>
              <FormControl asChild>
                <Input
                  value={formData.itemDescription || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, itemDescription: e.target.value }))}
                  placeholder="Describe the item (jewelry, art, etc.)"
                />
              </FormControl>
            </FormField>
          )}

          {/* Business interest specific fields */}
          {selectedAssetType.assetType === 'business_interest' && (
            <>
              <FormField name="businessName">
                <FormLabel>Business Name</FormLabel>
                <FormControl asChild>
                  <Input
                    value={formData.businessName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessName: e.target.value }))}
                    placeholder="Name of the business"
                  />
                </FormControl>
              </FormField>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField name="businessType">
                  <FormLabel>Business Type</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.businessType || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, businessType: e.target.value }))}
                      placeholder="LLC, Corporation, etc."
                    />
                  </FormControl>
                </FormField>
                
                <FormField name="taxId">
                  <FormLabel>Tax ID</FormLabel>
                  <FormControl asChild>
                    <Input
                      value={formData.taxId || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, taxId: e.target.value }))}
                      placeholder="12-3456789"
                    />
                  </FormControl>
                </FormField>
              </div>
              
              <FormField name="ownershipPercentage">
                <FormLabel>Ownership Percentage</FormLabel>
                <FormControl asChild>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={formData.ownershipPercentage || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, ownershipPercentage: e.target.value }))}
                    placeholder="50"
                  />
                </FormControl>
              </FormField>
            </>
          )}

          {/* Other asset specific fields */}
          {selectedAssetType.assetType === 'other' && (
            <FormField name="assetTypeDescription">
              <FormLabel>Asset Type</FormLabel>
              <FormControl asChild>
                <Input
                  value={formData.assetTypeDescription || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, assetTypeDescription: e.target.value }))}
                  placeholder="Describe the type of asset"
                />
              </FormControl>
            </FormField>
          )}

          <DialogFooter className="flex gap-2">
            <Button type="button" variant="ghost" onClick={handleBack}>
              Back
            </Button>
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit">
              Add Asset
            </Button>
          </DialogFooter>
        </Form>
      </>
    )
  }

  return (
    <Dialog open={open} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent size="lg" className="max-h-[90vh] overflow-y-auto">
        {stage === 1 ? renderStage1() : renderStage2()}
      </DialogContent>
    </Dialog>
  )
}