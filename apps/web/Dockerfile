FROM node:18-alpine

WORKDIR /workspace

# Change ownership of workspace early so all subsequent operations inherit permissions
RUN chown -R node:node /workspace

# Copy workspace package.json and install workspace dependencies
COPY --chown=node:node package*.json ./
COPY --chown=node:node packages/ ./packages/
COPY --chown=node:node apps/web/package*.json ./apps/web/

# Install dependencies from workspace root
RUN npm install

# Build the UI package first so it's available to web app
RUN cd /workspace/packages/ui && npm run build

WORKDIR /workspace/apps/web

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["npm", "run", "dev"]