'use client'

import { useState } from 'react'
import { AssetCard } from '@/components/assets/AssetCard'
import { AddAssetDialog } from '@/components/assets/AddAssetDialog'
import { Button, ToggleMultiGroup, ToggleGroupItem } from '@finpro/ui'
import { Asset } from '@/domain/assets'
import clsx from 'clsx';

// Mock asset data using new domain model
const mockAssets: Asset[] = [
  // Investment Assets
  {
    id: '1',
    portfolioId: 'portfolio-1',
    name: 'Fidelity Brokerage Account',
    assetType: 'investment_account',
    description: 'Primary investment brokerage account',
    currentValue: 52840,
    purchaseValue: 45000,
    institutionName: 'Fidelity Investments',
    accountNumber: '****1234',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '2',
    portfolioId: 'portfolio-1',
    name: 'Vanguard Investment Account',
    assetType: 'investment_account',
    description: 'Diversified ETF portfolio',
    currentValue: 38400,
    purchaseValue: 35200,
    institutionName: 'Vanguard',
    accountNumber: '****5678',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '3',
    portfolioId: 'portfolio-1',
    name: 'Coinbase Account',
    assetType: 'investment_account',
    description: 'Cryptocurrency holdings',
    currentValue: 35200,
    purchaseValue: 28900,
    institutionName: 'Coinbase',
    accountNumber: '****9012',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  
  // Retirement Assets
  {
    id: '4',
    portfolioId: 'portfolio-1',
    name: 'Company 401(k)',
    assetType: 'retirement_account',
    description: 'Employer-sponsored retirement plan',
    currentValue: 185000,
    purchaseValue: 120000,
    institutionName: 'Fidelity',
    accountNumber: '****2468',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '5',
    portfolioId: 'portfolio-1',
    name: 'Traditional IRA',
    assetType: 'retirement_account',
    description: 'Individual retirement account',
    currentValue: 67500,
    purchaseValue: 55000,
    institutionName: 'Charles Schwab',
    accountNumber: '****1357',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  
  // Banking Assets
  {
    id: '6',
    portfolioId: 'portfolio-1',
    name: 'High-Yield Savings',
    assetType: 'bank_account',
    description: '4.5% APY savings account',
    currentValue: 25000,
    purchaseValue: 25000,
    institutionName: 'Marcus by Goldman Sachs',
    accountNumber: '****4680',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '7',
    portfolioId: 'portfolio-1',
    name: '12-Month CD',
    assetType: 'bank_account',
    description: '5.1% APY certificate of deposit',
    currentValue: 15000,
    purchaseValue: 15000,
    institutionName: 'Capital One',
    accountNumber: '****9753',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  
  // Property Assets
  {
    id: '8',
    portfolioId: 'portfolio-1',
    name: 'Primary Residence',
    assetType: 'real_estate',
    description: '3 bedroom house at 123 Main St',
    currentValue: 485000,
    purchaseValue: 425000,
    purchaseDate: '2019-06-15',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '9',
    portfolioId: 'portfolio-1',
    name: '2022 Tesla Model Y',
    assetType: 'vehicle',
    description: 'Long Range AWD',
    currentValue: 38000,
    purchaseValue: 58000,
    purchaseDate: '2022-03-15',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '10',
    portfolioId: 'portfolio-1',
    name: 'Home Office Equipment',
    assetType: 'personal_property',
    description: 'Computer, monitors, desk, chair',
    currentValue: 8500,
    purchaseValue: 12000,
    purchaseDate: '2023-01-10',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  
  // Other Assets
  {
    id: '11',
    portfolioId: 'portfolio-1',
    name: 'UTMA Account',
    assetType: 'other',
    description: 'Custodial account for minor',
    currentValue: 12000,
    purchaseValue: 10000,
    institutionName: 'E*Trade',
    accountNumber: '****8642',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  },
  {
    id: '12',
    portfolioId: 'portfolio-1',
    name: 'Personal Property',
    assetType: 'personal_property',
    description: 'Jewelry, art, collectibles',
    currentValue: 15000,
    purchaseValue: 12000,
    purchaseDate: '2020-08-22',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'user-1',
    updatedAt: '2024-01-15T00:00:00Z',
    updatedBy: 'user-1',
    isDeleted: false,
    version: 1,
  }
]

export default function AssetsPage() {
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null)
  const [assetFilters, setAssetFilters] = useState<string[]>(['all'])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const handleFilterChange = (newFilters: string[]) => {
    if (newFilters.length === 0) {
      // Don't allow empty selection, keep current filters
      return
    }

    const hasAll = newFilters.includes('all')
    const hadAll = assetFilters.includes('all')
    const nonAllOptions = filterOptions.filter(opt => opt.value !== 'all').map(opt => opt.value)
    const nonAllFilters = newFilters.filter(f => f !== 'all')

    if (hasAll && hadAll && nonAllFilters.length > 0) {
      // User clicked a specific filter while "all" was selected - switch to just that filter
      setAssetFilters(nonAllFilters)
    } else if (hasAll && !hadAll) {
      // User selected "all" - clear all other filters
      setAssetFilters(['all'])
    } else if (!hasAll && hadAll) {
      // User deselected "all" - keep the other selected filters
      setAssetFilters(newFilters)
    } else if (!hasAll) {
      // Check if all non-"all" options are selected
      const allNonAllSelected = nonAllOptions.every(option => nonAllFilters.includes(option))
      
      if (allNonAllSelected && nonAllFilters.length === nonAllOptions.length) {
        // All specific filters selected - switch to "all"
        setAssetFilters(['all'])
      } else {
        // Normal multi-selection without "all"
        setAssetFilters(newFilters)
      }
    } else if (hasAll && hadAll && nonAllFilters.length === 0) {
      // User tried to deselect "all" but no other filters selected - keep "all"
      setAssetFilters(['all'])
    }
  }

  // Filter options with counts based on asset types
  const filterOptions = [
    { 
      value: 'all', 
      label: 'All', 
      count: mockAssets.length 
    },
    { 
      value: 'investment_account', 
      label: 'Investment', 
      count: mockAssets.filter(asset => asset.assetType === 'investment_account').length 
    },
    { 
      value: 'retirement_account', 
      label: 'Retirement', 
      count: mockAssets.filter(asset => asset.assetType === 'retirement_account').length 
    },
    { 
      value: 'bank_account', 
      label: 'Banking', 
      count: mockAssets.filter(asset => asset.assetType === 'bank_account').length 
    },
    { 
      value: 'real_estate', 
      label: 'Real Estate', 
      count: mockAssets.filter(asset => asset.assetType === 'real_estate').length 
    },
    { 
      value: 'vehicle', 
      label: 'Vehicle', 
      count: mockAssets.filter(asset => asset.assetType === 'vehicle').length 
    },
    { 
      value: 'personal_property', 
      label: 'Personal Property', 
      count: mockAssets.filter(asset => asset.assetType === 'personal_property').length 
    },
    { 
      value: 'business_interest', 
      label: 'Business Interest', 
      count: mockAssets.filter(asset => asset.assetType === 'business_interest').length 
    },
    { 
      value: 'other', 
      label: 'Other', 
      count: mockAssets.filter(asset => asset.assetType === 'other').length 
    }
  ].filter(option => option.count > 0) // Only show options with assets

  // Filter assets based on selected filters
  const filteredAssets = assetFilters.includes('all') 
    ? mockAssets 
    : mockAssets.filter(asset => {
        return assetFilters.some(filter => asset.assetType === filter)
      })

  const totalAssetValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0)

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(selectedAsset === asset.id ? null : asset.id)
  }

  return (
    <div className="container mx-auto px-6 py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Assets</h1>
            <p className="text-muted-foreground mt-2">
              Manage your investment portfolio and track asset performance.
            </p>
          </div>
          <Button 
            onClick={() => setIsAddDialogOpen(true)}
            startIcon={<span className="text-lg">+</span>}
          >
            Add Asset
          </Button>
        </div>

        {/* Asset Class Filters */}
        <div className="flex flex-wrap gap-2">
          <ToggleMultiGroup value={assetFilters} onValueChange={handleFilterChange}>
            {filterOptions.map((option) => {
              const isSelected = assetFilters.includes(option.value);
              const spanClasses = clsx('font-medium pl-2', !isSelected && 'text-primary');
              return (
                <ToggleGroupItem key={option.value} value={option.value} variant="outline">
                  {option.label} <span className={spanClasses}>{option.count}</span>
                </ToggleGroupItem>
              )
            })}
          </ToggleMultiGroup>
        </div>

        {/* Portfolio Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-card border rounded-lg p-6">
            <h3 className="text-sm font-medium text-muted-foreground">Total Asset Value</h3>
            <p className="text-2xl font-bold">
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
              }).format(totalAssetValue)}
            </p>
          </div>
          <div className="bg-card border rounded-lg p-6">
            <h3 className="text-sm font-medium text-muted-foreground">Total Assets</h3>
            <p className="text-2xl font-bold">
              {filteredAssets.length}
            </p>
          </div>
        </div>

        {/* Asset Cards */}
        <div className="space-y-2">
          {filteredAssets.map((asset) => (
            <AssetCard
              key={asset.id}
              asset={asset}
              variant="wide"
              onClick={handleAssetClick}
              className={selectedAsset === asset.id ? 'ring-2 ring-blue-500' : ''}
            />
          ))}
        </div>
        
        <AddAssetDialog 
          open={isAddDialogOpen}
          onClose={() => setIsAddDialogOpen(false)}
        />
      </div>
    </div>
  )
}