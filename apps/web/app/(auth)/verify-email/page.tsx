'use client'

import { Suspense, useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button, LoadingDots } from '@finpro/ui'

type VerificationState = 'loading' | 'success' | 'error' | 'expired'

function VerifyEmailContent() {
  const searchParams = useSearchParams()
  const [verificationState, setVerificationState] = useState<VerificationState>('loading')
  const [errorMessage, setErrorMessage] = useState('')

  useEffect(() => {
    const token = searchParams.get('token')
    
    if (!token) {
      setVerificationState('error')
      setErrorMessage('No verification token provided')
      return
    }

    verifyEmail(token)
  }, [searchParams])

  const verifyEmail = async (token: string) => {
    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      })

      const data = await response.json()

      if (response.ok) {
        setVerificationState('success')
      } else {
        setVerificationState(data.error === 'Token expired' ? 'expired' : 'error')
        setErrorMessage(data.error || 'Verification failed')
      }
    } catch {
      setVerificationState('error')
      setErrorMessage('Network error. Please try again.')
    }
  }

  const renderContent = () => {
    switch (verificationState) {
      case 'loading':
        return (
          <div className="text-center">
            <div className="mb-4">
              <LoadingDots />
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">Verifying your email...</h2>
            <p className="text-muted-foreground">Please wait while we confirm your email address.</p>
          </div>
        )

      case 'success':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-[#02bebf]/20 mb-4">
              <svg className="h-6 w-6 text-[#02bebf]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">Email verified successfully!</h2>
            <p className="text-muted-foreground mb-6">
              Your account has been activated. You can now sign in to access your account.
            </p>
            <Link href="/login">
              <Button className="w-full sm:w-auto">
                Continue to Sign In
              </Button>
            </Link>
          </div>
        )

      case 'expired':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-[#ffd850]/20 mb-4">
              <svg className="h-6 w-6 text-[#ffd850]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">Verification link expired</h2>
            <p className="text-muted-foreground mb-6">
              This verification link has expired. Please request a new verification email.
            </p>
            <div className="space-y-3">
              <Button className="w-full sm:w-auto" variant="outlined">
                Resend Verification Email
              </Button>
              <div>
                <Link href="/register" className="text-sm text-primary hover:text-primary/80 transition-colors">
                  Create a new account
                </Link>
              </div>
            </div>
          </div>
        )

      case 'error':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-destructive/20 mb-4">
              <svg className="h-6 w-6 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">Verification failed</h2>
            <p className="text-muted-foreground mb-6">
              {errorMessage || 'We could not verify your email address. Please try again.'}
            </p>
            <div className="space-y-3">
              <Button 
                className="w-full sm:w-auto" 
                variant="outlined"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
              <div>
                <Link href="/register" className="text-sm text-primary hover:text-primary/80 transition-colors">
                  Create a new account
                </Link>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="max-w-md mx-auto">
      {renderContent()}
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="max-w-md mx-auto text-center">
        <div className="mb-4">
          <LoadingDots />
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-2">Loading...</h2>
        <p className="text-muted-foreground">Please wait while we prepare your verification.</p>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  )
}