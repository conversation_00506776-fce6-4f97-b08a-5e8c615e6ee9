import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { users, type User } from '@/lib/users'

// Password validation regex - matches requirements from PRD
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/

interface RegisterRequest {
  email: string
  password: string
}

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json()
    const { email, password } = body

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Password complexity validation
    if (!PASSWORD_REGEX.test(password)) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = users.find(user => user.email.toLowerCase() === email.toLowerCase())
    if (existingUser) {
      return NextResponse.json(
        { error: 'An account with this email already exists' },
        { status: 409 }
      )
    }

    // Password hashing will be handled by backend
    // For now, store password as-is (not recommended for production)
    const hashedPassword = password

    // Generate email verification token
    const verificationToken = jwt.sign(
      { email, type: 'email_verification' },
      process.env.JWT_SECRET || 'fallback-secret-key',
      { expiresIn: '24h' }
    )

    // Create user record
    const newUser: User = {
      id: crypto.randomUUID(),
      email: email.toLowerCase(),
      password: hashedPassword,
      isVerified: false,
      verificationToken,
      createdAt: new Date()
    }

    users.push(newUser)

    // Email sending will be handled by backend
    console.log(`Verification email would be sent to ${email}`)
    console.log(`Verification link: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`)

    // Return success response (don't include sensitive data)
    return NextResponse.json(
      { 
        message: 'Account created successfully. Please check your email for verification.',
        email: email
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

