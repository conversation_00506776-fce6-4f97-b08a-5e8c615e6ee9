import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { users } from '@/lib/users'

interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json()
    const { email, password, rememberMe = false } = body

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Find user by email
    const user = users.find(user => user.email.toLowerCase() === email.toLowerCase())
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Check if user is verified
    if (!user.isVerified) {
      return NextResponse.json(
        { error: 'Please verify your email before signing in. Check your email for the verification link.' },
        { status: 403 }
      )
    }

    // Password verification will be handled by backend
    // For now, just check if password field exists
    if (!user.password) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Generate JWT token
    const tokenExpiry = rememberMe ? '30d' : '24h'
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        type: 'session'
      },
      process.env.JWT_SECRET || 'fallback-secret-key',
      { expiresIn: tokenExpiry }
    )

    // Create response
    const response = NextResponse.json(
      {
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          isVerified: user.isVerified
        },
        token
      },
      { status: 200 }
    )

    // Set httpOnly cookie for added security
    response.cookies.set({
      name: 'authToken',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60 // 30 days or 24 hours
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}