import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { users } from '@/lib/users'

interface VerifyEmailRequest {
  token: string
}

interface JWTPayload {
  email: string
  type: string
  iat: number
  exp: number
}

export async function POST(request: NextRequest) {
  try {
    const body: VerifyEmailRequest = await request.json()
    const { token } = body

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      )
    }

    // Verify and decode the JWT token
    let decoded: JWTPayload
    try {
      decoded = jwt.verify(
        token, 
        process.env.JWT_SECRET || 'fallback-secret-key'
      ) as JWTPayload
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return NextResponse.json(
          { error: 'Token expired' },
          { status: 400 }
        )
      }
      return NextResponse.json(
        { error: 'Invalid verification token' },
        { status: 400 }
      )
    }

    // Check if token is for email verification
    if (decoded.type !== 'email_verification') {
      return NextResponse.json(
        { error: 'Invalid token type' },
        { status: 400 }
      )
    }

    // Find user by email
    const user = users.find(u => u.email === decoded.email.toLowerCase())
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already verified
    if (user.isVerified) {
      return NextResponse.json(
        { message: 'Email already verified' },
        { status: 200 }
      )
    }

    // Check if the token matches the stored verification token
    if (user.verificationToken !== token) {
      return NextResponse.json(
        { error: 'Invalid verification token' },
        { status: 400 }
      )
    }

    // Mark user as verified and remove verification token
    user.isVerified = true
    user.verificationToken = undefined

    console.log(`User ${user.email} email verified successfully`)

    return NextResponse.json(
      { message: 'Email verified successfully' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Email verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}