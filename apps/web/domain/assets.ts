// Asset domain types precisely aligned with database schema

// Asset types from database enum
export type AssetType = 
  | 'bank_account'
  | 'investment_account'
  | 'retirement_account'
  | 'real_estate'
  | 'vehicle'
  | 'personal_property'
  | 'business_interest'
  | 'other'

// Account status from database enum
export type AccountStatus = 
  | 'active'
  | 'inactive' 
  | 'closed'
  | 'frozen'

// Bank account types from database enum
export type BankAccountType = 
  | 'checking'
  | 'savings'
  | 'money_market'
  | 'certificate_of_deposit'

// Investment account types from database enum
export type InvestmentAccountType = 
  | 'brokerage'
  | 'mutual_fund'
  | '529'
  | 'custodial'
  | 'trust'

// Retirement account types from database enum
export type RetirementAccountType = 
  | '401k'
  | 'ira'
  | 'roth_ira'
  | 'sep_ira'
  | 'simple_ira'
  | 'pension'

// Property types from database enum
export type PropertyType = 
  | 'single_family'
  | 'multi_family'
  | 'condo'
  | 'townhouse'
  | 'commercial'
  | 'land'

// Property use from database enum
export type PropertyUse = 
  | 'primary_residence'
  | 'secondary_residence'
  | 'rental_property'
  | 'commercial'
  | 'vacant_land'

// Vehicle types from database enum
export type VehicleType = 
  | 'car'
  | 'truck'
  | 'motorcycle'
  | 'boat'
  | 'rv'
  | 'other'

// Vehicle use from database enum
export type VehicleUse = 
  | 'personal'
  | 'business'
  | 'rental'

// Payment frequency from database enum
export type PaymentFrequency = 
  | 'weekly'
  | 'bi_weekly'
  | 'monthly'
  | 'quarterly'
  | 'semi_annual'
  | 'annual'

// Asset ownership types from database enum
export type AssetOwnershipType = 
  | 'individual'
  | 'joint_tenancy'
  | 'joint_tenancy_survivorship'
  | 'tenants_in_common'
  | 'community_property'
  | 'trust'
  | 'corporation'
  | 'partnership'

// Base Asset interface precisely aligned with database schema
export interface Asset {
  // Primary fields from assets table
  id: string
  portfolioId: string
  assetType: AssetType
  name: string
  description?: string
  ownershipType?: AssetOwnershipType
  ownershipPercentage?: number
  currentValue: number
  purchaseValue?: number
  purchaseDate?: Date | string
  institutionName?: string
  accountNumber?: string
  contactInfo?: Record<string, any>
  metadata?: Record<string, any>
  tags?: string[]
  
  // Status and audit fields
  isActive: boolean
  createdAt: Date | string
  createdBy: string
  updatedAt: Date | string
  updatedBy: string
  isDeleted: boolean
  deletedAt?: Date | string | null
  deletedBy?: string | null
  version: number
}

// Bank Account specific properties from asset_bank_accounts table
export interface BankAccountAsset extends Asset {
  assetType: 'bank_account'
  accountType: BankAccountType
  accountStatus: AccountStatus
  routingNumber?: string
  isChecking?: boolean
  isSavings?: boolean
  interestRate?: number
  interestFrequency?: PaymentFrequency
  minimumBalance?: number
  monthlyFee?: number
  overdraftProtection?: boolean
  hasDebitCard?: boolean
  hasChecks?: boolean
  onlineBankingEnabled?: boolean
}

// Investment Account specific properties from asset_investment_accounts table
export interface InvestmentAccountAsset extends Asset {
  assetType: 'investment_account'
  accountType: InvestmentAccountType
  accountStatus: AccountStatus
  brokerageName?: string
  totalHoldings?: number
  cashBalance?: number
  marginBalance?: number
  costBasis?: number
  unrealizedGainLoss?: number
  ytdReturn?: number
  marginEnabled?: boolean
  optionsEnabled?: boolean
  riskTolerance?: string
  investmentObjective?: string
}

// Retirement Account specific properties from asset_retirement_accounts table
export interface RetirementAccountAsset extends Asset {
  assetType: 'retirement_account'
  accountType: RetirementAccountType
  accountStatus: AccountStatus
  planName?: string
  employerName?: string
  participantId?: string
  employeeContributionPct?: number
  employeeContributionAmt?: number
  employerMatchPct?: number
  employerMatchAmt?: number
  vestingSchedule?: string
  vestedPercentage?: number
  vestedBalance?: number
  hasLoan?: boolean
  loanBalance?: number
  loanInterestRate?: number
  eligibleForDistribution?: boolean
  requiredMinimumDistribution?: number
  lastDistributionDate?: Date | string
}

// Real Estate specific properties from asset_real_estate table
export interface RealEstateAsset extends Asset {
  assetType: 'real_estate'
  propertyType?: PropertyType
  propertyUse?: PropertyUse
  addressLine1?: string
  addressLine2?: string
  city?: string
  stateProvince?: string
  postalCode?: string
  country?: string
  yearBuilt?: number
  squareFootage?: number
  lotSizeAcres?: number
  bedrooms?: number
  bathrooms?: number
  assessedValue?: number
  assessmentDate?: Date | string
  marketValue?: number
  appraisalDate?: Date | string
  isRental?: boolean
  monthlyRentalIncome?: number
  occupancyRate?: number
  propertyTaxAnnual?: number
  insuranceAnnual?: number
  hoaMonthly?: number
  maintenanceAnnual?: number
}

// Vehicle specific properties from asset_vehicles table
export interface VehicleAsset extends Asset {
  assetType: 'vehicle'
  vehicleType?: VehicleType
  vehicleUse?: VehicleUse
  year?: number
  make?: string
  model?: string
  trim?: string
  vin?: string
  licensePlate?: string
  mileage?: number
  condition?: string
  hasLoan?: boolean
  loanBalance?: number
  monthlyPayment?: number
  insuranceCarrier?: string
  insurancePolicyNumber?: string
  insuranceAnnualPremium?: number
}

// Personal Property specific properties from asset_personal_property table
export interface PersonalPropertyAsset extends Asset {
  assetType: 'personal_property'
  propertyType?: string
  itemDescription?: string
  manufacturer?: string
  modelNumber?: string
  serialNumber?: string
  purchasePrice?: number
  appraisalDate?: Date | string
  appraisedValue?: number
  appraiserName?: string
  condition?: string
  storageLocation?: string
  isScheduledOnInsurance?: boolean
  insuranceDeclaredValue?: number
}

// Business Interest specific properties from asset_business_interests table
export interface BusinessInterestAsset extends Asset {
  assetType: 'business_interest'
  businessName?: string
  businessType?: string
  taxId?: string
  numberOfShares?: number
  shareClass?: string
  bookValue?: number
  marketValue?: number
  valuationDate?: Date | string
  valuationMethod?: string
  annualRevenue?: number
  annualProfit?: number
  annualDistributions?: number
  industry?: string
  numberOfEmployees?: number
  yearEstablished?: number
}

// Other Asset specific properties from asset_other table
export interface OtherAsset extends Asset {
  assetType: 'other'
  assetTypeDescription?: string
  details?: Record<string, any>
  maturityDate?: Date | string
  interestRate?: number
  annualIncome?: number
  incomeType?: string
}

// Union type for all specific asset types
export type SpecificAsset = 
  | BankAccountAsset
  | InvestmentAccountAsset
  | RetirementAccountAsset
  | RealEstateAsset
  | VehicleAsset
  | PersonalPropertyAsset
  | BusinessInterestAsset
  | OtherAsset

// Utility functions
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value)
}

export const formatPercent = (value: number): string => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
}

// Asset type to UI label mapping
export const getAssetTypeLabel = (assetType: AssetType): string => {
  const labels: Record<AssetType, string> = {
    bank_account: 'Bank Account',
    investment_account: 'Investment Account',
    retirement_account: 'Retirement Account',
    real_estate: 'Real Estate',
    vehicle: 'Vehicle', 
    personal_property: 'Personal Property',
    business_interest: 'Business Interest',
    other: 'Other Asset'
  }
  
  return labels[assetType]
}

// Asset type to unique color mapping with custom Tailwind classes
export const getAssetTypeColor = (assetType: AssetType): string => {
  const colors: Record<AssetType, string> = {
    bank_account: 'bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200',           // Blue
    investment_account: 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200', // Green
    retirement_account: 'bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-200', // Purple
    real_estate: 'bg-orange-100 text-orange-800 border-orange-300 hover:bg-orange-200',    // Orange
    vehicle: 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200',                   // Red
    personal_property: 'bg-teal-100 text-teal-800 border-teal-300 hover:bg-teal-200',     // Teal
    business_interest: 'bg-indigo-100 text-indigo-800 border-indigo-300 hover:bg-indigo-200', // Indigo
    other: 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200'                  // Gray
  }
  
  return colors[assetType]
}

// Get account type label for bank accounts
export const getBankAccountTypeLabel = (accountType: BankAccountType): string => {
  const labels: Record<BankAccountType, string> = {
    checking: 'Checking',
    savings: 'Savings',
    money_market: 'Money Market',
    certificate_of_deposit: 'Certificate of Deposit'
  }
  return labels[accountType]
}

// Get investment account type label
export const getInvestmentAccountTypeLabel = (accountType: InvestmentAccountType): string => {
  const labels: Record<InvestmentAccountType, string> = {
    brokerage: 'Brokerage',
    mutual_fund: 'Mutual Fund',
    '529': '529 Plan',
    custodial: 'Custodial',
    trust: 'Trust'
  }
  return labels[accountType]
}

// Get retirement account type label
export const getRetirementAccountTypeLabel = (accountType: RetirementAccountType): string => {
  const labels: Record<RetirementAccountType, string> = {
    '401k': '401(k)',
    'ira': 'Traditional IRA',
    'roth_ira': 'Roth IRA',
    'sep_ira': 'SEP IRA',
    'simple_ira': 'SIMPLE IRA',
    'pension': 'Pension'
  }
  return labels[accountType]
}
