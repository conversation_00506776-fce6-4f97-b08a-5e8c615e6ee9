{"name": "finpro-monorepo", "version": "1.0.0", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "npm run dev --workspace=apps/web", "build": "npm run build --workspaces --if-present", "lint": "npm run lint --workspaces --if-present", "ui:dev": "npm run dev --workspace=packages/ui", "ui:build": "npm run build --workspace=packages/ui", "ui:storybook": "npm run storybook --workspace=packages/ui", "web:dev": "npm run dev --workspace=apps/web", "web:build": "npm run build --workspace=apps/web"}, "devDependencies": {}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}