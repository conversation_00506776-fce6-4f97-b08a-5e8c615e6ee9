# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Docker Compose (Recommended for Local Development)
```bash
# Start the entire stack (database, API, web app, Redis)
docker-compose up

# Start in background
docker-compose up -d

# Start specific services
docker-compose up db api          # Database and API only
docker-compose up db web          # Database and web app only

# View logs
docker-compose logs               # All services
docker-compose logs api           # API service only
docker-compose logs web           # Web service only

# Stop services
docker-compose down               # Stop all services
docker-compose down -v            # Stop and remove volumes (clean slate)

# Rebuild containers after code changes
docker-compose build             # Rebuild all
docker-compose build api         # Rebuild API only
docker-compose build web         # Rebuild web only

# Run commands in containers
docker-compose exec api bash                    # Shell into API container
docker-compose exec api alembic upgrade head    # Run migrations
docker-compose exec api pytest                  # Run API tests
docker-compose exec web npm run lint            # Run web linting
```

**Services Available:**
- **Database (PostgreSQL)**: `localhost:5432`
- **API (FastAPI)**: `localhost:8000` with docs at `/docs`
- **Web App (Next.js)**: `localhost:3000`
- **Redis**: `localhost:6379`

### Frontend Monorepo Commands
```bash
# Root commands (run from finpro/ directory)
npm run dev              # Start web app development server
npm run build            # Build all workspaces
npm run lint             # Lint all workspaces

# Workspace-specific commands
npm run web:dev          # Start web app (Next.js)
npm run web:build        # Build web app
npm run ui:dev           # Start UI library development (Vite)
npm run ui:build         # Build UI library
npm run ui:storybook     # Start Storybook for UI components
```

### Backend API Commands
```bash
# In apps/api/ (FastAPI backend)
cd apps/api

# Setup
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
poetry install

# Development
uvicorn app.main:app --reload --port 8000

# Database
alembic revision --autogenerate -m "Description"  # Create migration
alembic upgrade head                               # Apply migrations
alembic downgrade -1                              # Rollback one migration

# Testing
pytest                                             # Run all tests
pytest --cov=app --cov-report=html               # With coverage
pytest tests/test_users.py                        # Specific test file

# Code Quality
black app/                                         # Format code
flake8 app/                                       # Lint code
mypy app/                                         # Type checking
```

### Individual Workspace Commands
```bash
# In apps/web/ (Next.js app)
npm run dev              # Start Next.js with Turbopack (--turbopack flag)
npm run build            # Build Next.js application
npm run start            # Start production build
npm run lint             # Run Next.js ESLint

# In packages/ui/ (Component library)
npm run dev              # Start Vite dev server
npm run build            # Build library (ESM + UMD)
npm run storybook        # Start Storybook on port 6006
npm run lint             # Run ESLint
```

## Architecture Overview

### Full-Stack Monorepo Structure
```
finpro/
├── apps/
│   ├── api/            # FastAPI backend application
│   │   ├── app/
│   │   │   ├── api/v1/        # API endpoints
│   │   │   ├── core/          # Core utilities (config, security, database)
│   │   │   ├── models/        # Data models (domain, schemas, database)
│   │   │   ├── repositories/  # Data access layer
│   │   │   ├── services/      # Business logic
│   │   │   ├── middleware/    # Custom middleware
│   │   │   ├── templates/                # Templates directory (email)
│   │   │   └── main.py        # Application entry point
│   │   ├── alembic/           # Database migrations
│   │   ├── tests/             # Test suite
│   │   └── Dockerfile         # Container configuration
│   └── web/            # Next.js 15 financial dashboard application
│       ├── app/               # App Router pages
│       └── components/        # React components
├── packages/
│   └── ui/             # React component library ("@finpro/ui")
├── docs/               # Project documentation
│   ├── requirements/   # Product specs and features
│   ├── technical/             # Technical documentation
│   └── project-management/    # Roadmap and planning
└── package.json        # Workspace configuration
```

### Backend API (apps/api/)
- **Framework**: FastAPI (Python)
- **Database**: PostgreSQL 14+ with SQLAlchemy ORM
- **Migrations**: Alembic for database schema management
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Pydantic models for request/response validation
- **Server**: Uvicorn ASGI server
- **Testing**: pytest with coverage reporting
- **Code Quality**: Black formatter, flake8 linter, mypy type checker
- **Documentation**: Auto-generated Swagger UI at `/docs` and ReDoc at `/redoc`

**API Structure**:
- RESTful API design with versioning (`/api/v1/`)
- Modular architecture with separate layers (endpoints, services, models)
- Environment-based configuration
- Docker support for containerized deployment

### Web Application (apps/web/)
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4
- **UI Components**: `@finpro/ui` (internal library)
- **Development**: Turbopack for fast dev builds

**Routing Structure**:
- Uses App Router with file-based routing in `app/` directory
- Dashboard routes nested under `/dashboard/` with shared layout
- Route example: `/dashboard/assets` → `app/dashboard/assets/page.tsx`

**Key Patterns**:
- Layout components provide shared UI across route segments
- Dashboard uses nested layouts for navigation and common elements
- TypeScript exports follow `export default function ComponentName()` pattern
- Tailwind CSS with utility-first approach and responsive design

### UI Library (packages/ui/)
- **React component library** built with Radix UI primitives
- **Build**: Vite in library mode (ESM + UMD output)
- **Styling**: Tailwind CSS v4 with custom CSS variables
- **Documentation**: Storybook with automated docs generation
- **Distribution**: Published as `@finpro/ui` with TypeScript definitions

**Component Architecture**:
- Radix UI for accessibility and behavior
- Polymorphic components using `Slot` with `asChild` prop
- Hybrid variant system (Material + shadcn/ui patterns)
- Custom color system based on finpro brand colors
- All components include Storybook stories for documentation

## Documentation Structure

### Requirements (`docs/requirements/`)
- **Product Requirements**: Complete product requirement documents for all modules
- **Feature Specs**: Detailed technical specifications

### Technical Documentation (`docs/technical/`)
- **Domain Model**: Entity relationships and business logic
- **Infrastructure Design**: System architecture and deployment
- **API Design**: Endpoint specifications and data contracts

### Project Management (`docs/project-management/`)
- **Roadmap**: Development timeline and milestones
- **Sprint Planning**: Task breakdown and priorities

## Technology Stack

### Frontend
- **React 19**: Latest React with concurrent features
- **TypeScript 5**: Strict typing with path aliases (`@/*`)
- **Tailwind CSS v4**: Latest version with improved performance
- **Next.js 15**: App Router, Turbopack, and latest optimizations
- **Vite**: Fast build tool for library development
- **Storybook**: Component documentation and testing

### Backend
- **Python 3.11+**: Modern Python with type hints
- **FastAPI**: High-performance async web framework
- **PostgreSQL 14+**: Relational database with JSON support
- **Redis**: Session caching and rate limiting
- **Docker**: Containerization for deployment

## Development Workflow

### Full-Stack Development
1. **Backend API**: Start FastAPI server on port 8000
2. **Frontend App**: Run Next.js dev server on port 3000
3. **UI Components**: Use Storybook for component development
4. **Database**: Use Alembic for schema migrations
5. **Testing**: Run pytest for backend, Jest for frontend

### Code Quality Standards
- **Python**: PEP 8, type hints, Black formatting
- **TypeScript**: Strict mode, ESLint, Prettier
- **Git**: Conventional commits, feature branches
- **Testing**: Unit tests, integration tests, E2E tests

## Project Requirements

### System Requirements
- Node.js ≥18.0.0
- Python ≥3.11
- PostgreSQL ≥14
- npm ≥8.0.0
- Poetry for Python dependency management

### Development Standards
- TypeScript strict mode enabled
- All components must include proper TypeScript interfaces
- Dashboard routes should follow nested layout patterns
- UI components must include Storybook stories
- Database models must have proper migrations
- All code must pass linting and type checking