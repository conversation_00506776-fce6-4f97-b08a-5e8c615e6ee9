# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Install dependencies
npm install

# Development
npm run dev                # Start Vite dev server for development
npm run build             # Build library for production (ESM + UMD)
npm run preview           # Preview production build

# Code Quality
npm run lint              # Run ESLint on all files

# Storybook
npm run storybook         # Start Storybook dev server on port 6006
npm run build-storybook   # Build Storybook for production
```

## Architecture Overview

### Tech Stack

- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite with library mode configuration
- **Styling**: Tailwind CSS v4 with custom CSS variables
- **UI Primitives**: Radix UI components for accessibility
- **Documentation**: Storybook with automated docs generation
- **Linting**: ESLint with TypeScript, React Hooks, and Storybook plugins

### Project Structure

This is a **React component library** built for distribution as both ESM and UMD modules:

```
src/
├── index.ts              # Main library entry point
├── styles.css           # Global styles with Tailwind imports
├── colors.css          # Custom color scheme from finpro.old
└── components/
    ├── index.ts         # Component exports
    ├── Button.tsx       # Button component implementation
    └── Button.stories.tsx # Storybook stories
```

#### Key Patterns

**Library Architecture**:
- Components are exported from `src/components/index.ts`
- Main entry point (`src/index.ts`) imports styles and re-exports components
- Built as library with external React/ReactDOM dependencies
- TypeScript declarations generated automatically

**Component Design**:
- Uses Radix UI's `Slot` for polymorphic component behavior (`asChild` prop)
- Combines multiple variant systems: Material-inspired (`contained`, `outlined`, `text`) with shadcn/ui variants (`default`, `destructive`, `ghost`, etc.)
- Comprehensive prop interfaces with loading states, icons, and accessibility features
- CSS-in-JS through Tailwind utility classes with conditional styling

**Color System**:
- Custom CSS variables defined in `colors.css` based on finpro.old color scheme
- Primary colors: Blue (#055efa), Dark Blue (#011746), Black (#040714), White (#e9edf1)
- Support colors: Purple, Green, Yellow, Orange, Red, Pink, Teal with RGB variants
- Grey scale from light to dark with semantic naming

### Build Configuration

**Vite Library Mode**:
- Entry: `src/index.ts`
- Output: `finpro-components.es.js` (ESM) and `finpro-components.umd.js`
- External dependencies: React and ReactDOM (peer dependencies)
- TypeScript declarations included

**TypeScript Configuration**:
- Strict mode enabled with unused parameter/variable checking
- Library mode with declaration generation
- ES2020 target with DOM libraries

### Development Workflow

1. **Component Development**: Use Storybook for isolated component development and testing
2. **Code Quality**: ESLint runs automatically with React Hooks and Storybook rules
3. **Documentation**: Stories are automatically documented with `autodocs` tag
4. **Library Distribution**: Build produces both ESM and UMD bundles with TypeScript definitions

### Storybook Integration

- Stories located alongside components (`*.stories.tsx`)
- Automated documentation generation via `autodocs`
- CSS imports through preview configuration
- Control definitions for interactive property testing

The library follows a hybrid component API approach, supporting both modern React patterns (polymorphic components, compound components) and traditional Material Design component interfaces for maximum flexibility in different usage contexts.