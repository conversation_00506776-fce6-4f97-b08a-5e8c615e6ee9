@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=SF+Pro:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 2px);
  --radius-md: var(--radius);
  --radius-lg: calc(var(--radius) + 2px);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-button: var(--radius-button);
  --radius-card: var(--radius-card);
  --box-shadow-sm: var(--elevation-sm);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.01em;
  }
}

h1 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

h2 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

h3 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

h4 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

p {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

span {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  letter-spacing: 0.025em;
}

button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  letter-spacing: -0.005em;
}

input {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

html {
  font-size: var(--font-size);
}

:root {
  --font-size: 14px;
  /* Typography scale variables */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  
  /* Professional color palette */
  /* The default background color for the application. */
  --background: rgba(255, 255, 255, 1);
  /* The default color for elements or text that appears on top of the background. */
  --foreground: rgba(23, 24, 25, 1);
  /* The background color for cards, modals, and other containers. */
  --card: rgba(255, 255, 255, 1);
  /* The default color for text or elements that appear on top of cards, modals and other background containers. */
  --card-foreground: rgba(23, 24, 25, 1);
  /* The background color for dropdowns and tooltips. */
  --popover: rgba(255, 255, 255, 1);
  /* The default color for text or elements that appear on top of dropdowns and tooltips. */
  --popover-foreground: rgba(23, 24, 25, 1);
  /* The primary color used for buttons, links, and other interactive elements - Professional Blue */
  --primary: rgba(99, 91, 255, 1);
  /* The default color for text or elements that appear on top of primary colored elements. */
  --primary-foreground: rgba(255, 255, 255, 1);
  /* The secondary color used for secondary(but NOT-DESTRUCTIVE) buttons and interactive elements. */
  --secondary: rgba(248, 249, 250, 1);
  /* The default color for text or elements that appear on top of secondary colored elements. */
  --secondary-foreground: rgba(23, 24, 25, 1);
  /* The muted color used for less prominent elements, such as disabled buttons or disabled text. */
  --muted: rgba(248, 249, 250, 1);
  /* The default color for text or elements that appear on top of muted colored elements. */
  --muted-foreground: rgba(107, 114, 126, 1);
  /* The accent color used for highlights, links, and other interactive elements - Professional Teal */
  --accent: rgba(0, 213, 186, 1);
  /* The default color for text or elements that appear on top of accent colored elements. */
  --accent-foreground: rgba(255, 255, 255, 1);
  /* The color used for background in destructive actions, such as delete buttons or error messages. */
  --destructive: rgba(220, 38, 38, 1);
  /* The default color for text or elements that appear on top of destructive colored elements. */
  --destructive-foreground: rgba(255, 255, 255, 1);
  /* The default border color for elements such as inputs, buttons, and other containers. */
  --border: rgba(229, 231, 235, 1);
  /* The default background color for input fields once the text has been filled. Should be either transparent or match the input-background. */
  --input: rgba(255, 255, 255, 1);
  /* The default background color for input fields, text areas, and other input elements. */
  --input-background: rgba(255, 255, 255, 1);
  /* The color for focus rings, outlines, and other focus indicators. */
  --ring: rgba(99, 91, 255, 1);
  /* Shadow for small elevations, such as cards or modals - Professional subtle shadows */
  --elevation-sm: 0px 2px 4px 0px rgba(0, 0, 0, 0.05), 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  /* The color for chart elements, such as a bar or line in a chart. */
  --chart-1: rgba(99, 91, 255, 1);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-2: rgba(0, 213, 186, 1);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-3: rgba(245, 101, 101, 1);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-4: rgba(251, 191, 36, 1);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-5: rgba(139, 92, 246, 1);
  /* The default border radius for elements such as buttons, cards, and other containers. */
  --radius: 6px;
  /* Border radius for button-specific elements. */
  --radius-button: 6px;
  /* Border radius for card and modal elements. */
  --radius-card: 8px;
  /* The default font weight for bold text. */
  --font-weight-bold: 600;
  /* The default font weight for normal text. */
  --font-weight-normal: 400;
  /* The background color for sidebars, navigation menus, and other persistent elements. */
  --sidebar: rgba(248, 249, 250, 1);
  /* The default color for text or elements that appear on top of sidebars, navigation menus, and other persistent elements. */
  --sidebar-foreground: rgba(107, 114, 126, 1);
  /* The primary color used for buttons, links, and other interactive elements in sidebars and navigation menus. */
  --sidebar-primary: rgba(99, 91, 255, 1);
  /* The default color for text or elements that appear on top of primary colored elements in sidebars and navigation menus. */
  --sidebar-primary-foreground: rgba(255, 255, 255, 1);
  /* The accent color used for highlights, links, and other interactive elements in sidebars and navigation menus. */
  --sidebar-accent: rgba(0, 213, 186, 1);
  /* The default color for text or elements that appear on top of accent colored elements in sidebars and navigation menus. */
  --sidebar-accent-foreground: rgba(255, 255, 255, 1);
  /* The default border color for elements in sidebars and navigation menus. */
  --sidebar-border: rgba(229, 231, 235, 1);
  /* The default color for focus rings, outlines, and other focus indicators in sidebars and navigation menus. */
  --sidebar-ring: rgba(99, 91, 255, 1);
}

@theme {
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  --animate-collapsible-down: collapsible-down 0.2s ease-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-out;

  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  --animate-spin-dash: spin-dash 1.5s ease-in-out infinite;
  --animate-ellipsis: ellipsis 1.4s ease-in-out infinite;
  
  @keyframes spin-dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -125;
    }
  }
  
  @keyframes ellipsis {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}