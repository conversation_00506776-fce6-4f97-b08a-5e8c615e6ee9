import type { Meta, StoryObj } from '@storybook/react-vite'
import { SvgIcon } from './SvgIcon'
import { svgIconShapeKeys } from './SvgIconShapes'

const meta: Meta<typeof SvgIcon> = {
  title: 'Components/SvgIcon',
  component: SvgIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    shape: {
      control: { type: 'select' },
      options: svgIconShapeKeys,
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg', 'xl'],
    },
    color: {
      control: { type: 'color' },
    },
    width: {
      control: { type: 'number' },
    },
    height: {
      control: { type: 'number' },
    },
    viewBox: {
      control: { type: 'text' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    shape: 'account',
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <SvgIcon shape="account" size="sm" />
      <SvgIcon shape="account" size="default" />
      <SvgIcon shape="account" size="lg" />
      <SvgIcon shape="account" size="xl" />
    </div>
  ),
}

export const Colors: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <SvgIcon shape="account" color="#3b82f6" />
      <SvgIcon shape="account" color="#ef4444" />
      <SvgIcon shape="account" color="#10b981" />
      <SvgIcon shape="account" color="#f59e0b" />
      <SvgIcon shape="account" color="#8b5cf6" />
      <SvgIcon shape="account" color="#ec4899" />
    </div>
  ),
}

export const CustomDimensions: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <SvgIcon shape="account" width={16} height={16} />
      <SvgIcon shape="account" width={32} height={32} />
      <SvgIcon shape="account" width={48} height={48} />
      <SvgIcon shape="account" width={64} height={64} />
    </div>
  ),
}

export const CommonIcons: Story = {
  render: () => (
    <div className="grid grid-cols-6 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="account" />
        <span className="text-xs">account</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="arrow-back" />
        <span className="text-xs">arrow-back</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="arrow-forward" />
        <span className="text-xs">arrow-forward</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="calendar" />
        <span className="text-xs">calendar</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="checkmark" />
        <span className="text-xs">checkmark</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="close" />
        <span className="text-xs">close</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="dashboard" />
        <span className="text-xs">dashboard</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="dollar" />
        <span className="text-xs">dollar</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="menu" />
        <span className="text-xs">menu</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="search" />
        <span className="text-xs">search</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="settings" />
        <span className="text-xs">settings</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="user" />
        <span className="text-xs">user</span>
      </div>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
}

export const FinancialIcons: Story = {
  render: () => (
    <div className="grid grid-cols-6 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="assets" />
        <span className="text-xs">assets</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="budget" />
        <span className="text-xs">budget</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="calculator" />
        <span className="text-xs">calculator</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="debt" />
        <span className="text-xs">debt</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="dollar-sign" />
        <span className="text-xs">dollar-sign</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SvgIcon shape="investment" />
        <span className="text-xs">investment</span>
      </div>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
}

export const AllIcons: Story = {
  render: () => (
    <div className="grid grid-cols-8 gap-4 p-4 max-h-96 overflow-auto">
      {svgIconShapeKeys.map((shape) => (
        <div key={shape} className="flex flex-col items-center gap-2">
          <SvgIcon shape={shape} />
          <span className="text-xs text-center break-all">{shape}</span>
        </div>
      ))}
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
}

export const InContext: Story = {
  render: () => (
    <div className="space-y-6 p-4">
      <div className="flex items-center gap-3">
        <SvgIcon shape="user" size="sm" />
        <span className="text-sm">Small icon with text</span>
      </div>
      
      <div className="flex items-center gap-3">
        <SvgIcon shape="dashboard" />
        <span>Default icon with text</span>
      </div>
      
      <div className="flex items-center gap-3">
        <SvgIcon shape="settings" size="lg" />
        <span className="text-lg">Large icon with text</span>
      </div>
      
      <div className="flex items-center gap-3">
        <SvgIcon shape="calendar" size="xl" />
        <span className="text-xl">Extra large icon with text</span>
      </div>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
}

export const Interactive: Story = {
  args: {
    shape: 'account',
    size: 'default',
    color: '#3b82f6',
  },
}