import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'
import { svgIconShapes, SvgIconShapeKey } from './SvgIconShapes'

const svgIconVariants = cva(
  'svg-icon',
  {
    variants: {
      size: {
        sm: 'w-4 h-4',
        default: 'w-6 h-6',
        lg: 'w-8 h-8',
        xl: 'w-10 h-10',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
)

interface SvgIconProps
  extends Omit<React.SVGProps<SVGSVGElement>, 'width' | 'height'>,
    VariantProps<typeof svgIconVariants> {
  shape: SvgIconShapeKey
  color?: string
  width?: number | string
  height?: number | string
  viewBox?: string
}

/**
 * An SVG icon using one of the predefined shapes
 */
const SvgIcon = React.forwardRef<SVGSVGElement, SvgIconProps>(
  ({ 
    className,
    size,
    shape,
    color,
    width,
    height,
    viewBox,
    style,
    ...props 
  }, ref) => {
    const componentId = React.useId().replace(/[^a-zA-Z0-9]/g, '_')
    const elementId = `svg-icon-${componentId}`
    
    const styleObj = React.useMemo(() => {
      const baseStyle = color ? { color } : {}
      return { ...baseStyle, ...style }
    }, [color, style])
    
    const compWidth = React.useMemo(() => {
      if (width != null) {
        return typeof width === 'number' ? width : parseInt(String(width))
      }
      // Use CSS classes for size variants when no explicit width is provided
      return size ? undefined : 24
    }, [width, size])
    
    const compHeight = React.useMemo(() => {
      if (height != null) {
        return typeof height === 'number' ? height : parseInt(String(height))
      }
      // Use CSS classes for size variants when no explicit height is provided
      return size ? undefined : 24
    }, [height, size])
    
    const computedViewBox = React.useMemo(() => viewBox || '0 0 24 24', [viewBox])
    
    // Ensure each instance of this SVG on the page has unique ID tags
    const svgContent = React.useMemo(() => 
      svgIconShapes.get(shape)?.replace(/SVG_ID_/g, `SVG_${componentId}_`) || '',
      [shape, componentId]
    )
    
    return (
      <svg
        ref={ref}
        xmlns="http://www.w3.org/2000/svg"
        width={compWidth}
        height={compHeight}
        fill="none"
        viewBox={computedViewBox}
        dangerouslySetInnerHTML={{ __html: svgContent }}
        style={styleObj}
        id={elementId}
        className={clsx(svgIconVariants({ size }), className)}
        {...props}
      />
    )
  }
)
SvgIcon.displayName = 'SvgIcon'

export { SvgIcon }
export type { SvgIconProps }
export type { SvgIconShapeKey } from './SvgIconShapes'