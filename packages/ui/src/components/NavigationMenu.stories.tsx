import type { Meta, StoryObj } from '@storybook/react-vite'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuLinkItem,
} from './NavigationMenu'

const meta: Meta<typeof NavigationMenu> = {
  title: 'Components/NavigationMenu',
  component: NavigationMenu,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Getting started</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
              <li className="row-span-3">
                <NavigationMenuLink asChild>
                  <a
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-blue-50/50 to-blue-100 p-6 no-underline outline-none focus:shadow-md"
                    href="/"
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="h-6 w-6"
                    >
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                    </svg>
                    <div className="mb-2 mt-4 text-lg font-medium text-blue-900">
                      finpro/ui
                    </div>
                    <p className="text-sm leading-tight text-blue-800">
                      Beautifully designed components built with Radix UI and
                      Tailwind CSS.
                    </p>
                  </a>
                </NavigationMenuLink>
              </li>
              <NavigationMenuLinkItem
                href="/docs"
                title="Introduction"
                description="Re-usable components built using Radix UI and Tailwind CSS."
              />
              <NavigationMenuLinkItem
                href="/docs/installation"
                title="Installation"
                description="How to install dependencies and structure your app."
              />
              <NavigationMenuLinkItem
                href="/docs/primitives/typography"
                title="Typography"
                description="Styles for headings, paragraphs, lists...etc"
              />
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Components</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
              {components.map((component) => (
                <NavigationMenuLinkItem
                  key={component.title}
                  title={component.title}
                  href={component.href}
                  description={component.description}
                />
              ))}
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="/docs">
            Documentation
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
}

export const SimpleHorizontal: Story = {
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Products</NavigationMenuTrigger>
          <NavigationMenuContent variant="dropdown">
            <ul className="grid gap-3 p-4 w-[400px]">
              <NavigationMenuLinkItem
                href="#"
                title="Analytics"
                description="Get insights into your website traffic and performance."
              />
              <NavigationMenuLinkItem
                href="#"
                title="CRM"
                description="Manage your customer relationships with ease."
              />
              <NavigationMenuLinkItem
                href="#"
                title="E-commerce"
                description="Build and scale your online store."
              />
              <NavigationMenuLinkItem
                href="#"
                title="Marketing"
                description="Grow your business with powerful marketing tools."
              />
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Solutions</NavigationMenuTrigger>
          <NavigationMenuContent variant="dropdown">
            <ul className="grid gap-3 p-4 w-[300px]">
              <NavigationMenuLinkItem
                href="#"
                title="For Startups"
                description="Get your business off the ground quickly."
              />
              <NavigationMenuLinkItem
                href="#"
                title="For Enterprise"
                description="Scale your business with enterprise solutions."
              />
              <NavigationMenuLinkItem
                href="#"
                title="For Agencies"
                description="Manage multiple clients and projects."
              />
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="#pricing">
            Pricing
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="#about">
            About
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
}

export const MegaMenu: Story = {
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Shop</NavigationMenuTrigger>
          <NavigationMenuContent variant="mega">
            <div className="grid gap-6 p-6 md:w-[600px] lg:w-[800px] lg:grid-cols-4">
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900">Clothing</h3>
                <ul className="space-y-2">
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Shirts
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Pants
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Jackets
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Dresses
                    </NavigationMenuLink>
                  </li>
                </ul>
              </div>
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900">Shoes</h3>
                <ul className="space-y-2">
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Sneakers
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Boots
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Sandals
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Formal
                    </NavigationMenuLink>
                  </li>
                </ul>
              </div>
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900">Accessories</h3>
                <ul className="space-y-2">
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Bags
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Watches
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Jewelry
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink href="#" className="text-sm text-gray-600 hover:text-gray-900">
                      Sunglasses
                    </NavigationMenuLink>
                  </li>
                </ul>
              </div>
              <div className="space-y-3">
                <div className="rounded-lg bg-gradient-to-r from-purple-100 to-pink-100 p-4">
                  <h3 className="text-sm font-medium text-gray-900">New Collection</h3>
                  <p className="text-xs text-gray-600 mt-1">
                    Check out our latest spring collection with 20% off.
                  </p>
                  <NavigationMenuLink 
                    href="#" 
                    className="mt-2 inline-block text-xs font-medium text-purple-700 hover:text-purple-900"
                  >
                    Shop now →
                  </NavigationMenuLink>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="#sale">
            Sale
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="#brands">
            Brands
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
}

export const WithIcons: Story = {
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="mr-2 h-4 w-4"
            >
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            Account
          </NavigationMenuTrigger>
          <NavigationMenuContent variant="dropdown">
            <ul className="grid gap-2 p-4 w-[300px]">
              <NavigationMenuLinkItem
                href="#"
                title="Profile"
                description="Manage your account settings and preferences."
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  className="h-4 w-4 text-gray-400"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                  <circle cx="12" cy="7" r="4" />
                </svg>
              </NavigationMenuLinkItem>
              <NavigationMenuLinkItem
                href="#"
                title="Settings"
                description="Configure your application preferences."
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  className="h-4 w-4 text-gray-400"
                >
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
              </NavigationMenuLinkItem>
              <NavigationMenuLinkItem
                href="#"
                title="Billing"
                description="View and manage your billing information."
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  className="h-4 w-4 text-gray-400"
                >
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
              </NavigationMenuLinkItem>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink href="#help">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="mr-2 h-4 w-4"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
              <line x1="12" x2="12.01" y1="17" y2="17" />
            </svg>
            Help
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
}

export const TriggerVariants: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-4 text-sm font-medium">Default</h3>
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Default</NavigationMenuTrigger>
              <NavigationMenuContent variant="card">
                <div className="p-4">
                  <p className="text-sm">Default trigger style</p>
                </div>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>

      <div>
        <h3 className="mb-4 text-sm font-medium">Ghost</h3>
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger variant="ghost">Ghost</NavigationMenuTrigger>
              <NavigationMenuContent variant="card">
                <div className="p-4">
                  <p className="text-sm">Ghost trigger style</p>
                </div>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>

      <div>
        <h3 className="mb-4 text-sm font-medium">Outline</h3>
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger variant="outline">Outline</NavigationMenuTrigger>
              <NavigationMenuContent variant="card">
                <div className="p-4">
                  <p className="text-sm">Outline trigger style</p>
                </div>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>
    </div>
  ),
}

const components = [
  {
    title: 'Alert Dialog',
    href: '/docs/primitives/alert-dialog',
    description:
      'A modal dialog that interrupts the user with important content.',
  },
  {
    title: 'Hover Card',
    href: '/docs/primitives/hover-card',
    description:
      'For sighted users to preview content available behind a link.',
  },
  {
    title: 'Progress',
    href: '/docs/primitives/progress',
    description:
      'Displays an indicator showing the completion progress of a task.',
  },
  {
    title: 'Scroll-area',
    href: '/docs/primitives/scroll-area',
    description: 'Visually or semantically separates content.',
  },
  {
    title: 'Tabs',
    href: '/docs/primitives/tabs',
    description:
      'A set of layered sections of content—known as tab panels.',
  },
  {
    title: 'Tooltip',
    href: '/docs/primitives/tooltip',
    description:
      'A popup that displays information related to an element.',
  },
]