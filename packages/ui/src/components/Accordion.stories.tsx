import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from './Accordion'

const meta: Meta = {
  title: 'Components/Accordion',
  component: Accordion,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <div className="w-96">
      <Accordion type="single" variant="default" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>Is it accessible?</AccordionTrigger>
          <AccordionContent>
            Yes. It adheres to the WAI-ARIA design pattern and uses semantic HTML elements.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>Is it styled?</AccordionTrigger>
          <AccordionContent>
            Yes. It comes with default styles that matches the other components and can be customized with CSS.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger>Is it animated?</AccordionTrigger>
          <AccordionContent>
            Yes. It uses CSS animations to provide smooth transitions when opening and closing.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  ),
}

export const Multiple: Story = {
  render: () => (
    <div className="w-96">
      <Accordion type="multiple" variant="default">
        <AccordionItem value="item-1">
          <AccordionTrigger>Can I open multiple items?</AccordionTrigger>
          <AccordionContent>
            Yes! This accordion allows multiple items to be open simultaneously.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>Another question</AccordionTrigger>
          <AccordionContent>
            This item can be open at the same time as others when type is set to "multiple".
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger>Third item</AccordionTrigger>
          <AccordionContent>
            All items can be expanded independently in multiple mode.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  ),
}

export const Card: Story = {
  render: () => (
    <div className="w-96">
      <Accordion type="single" variant="card" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>Card variant</AccordionTrigger>
          <AccordionContent>
            This accordion uses the card variant with borders and shadow styling.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>Styled differently</AccordionTrigger>
          <AccordionContent>
            The card variant provides a more prominent visual appearance.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  ),
}