import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { RadioGroup, RadioGroupItem, RadioGroupOption } from './RadioGroup'
import { Label } from './Label'

const meta: Meta<typeof RadioGroup> = {
  title: 'Components/RadioGroup',
  component: RadioGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['vertical', 'horizontal'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <div className="space-y-2">
      <Label>Choose an option</Label>
      <RadioGroup defaultValue="option1">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="option1" id="option1" />
          <Label htmlFor="option1">Option 1</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="option2" id="option2" />
          <Label htmlFor="option2">Option 2</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="option3" id="option3" />
          <Label htmlFor="option3">Option 3</Label>
        </div>
      </RadioGroup>
    </div>
  ),
}

export const WithOptions: Story = {
  render: () => (
    <div className="space-y-2">
      <Label>Select your plan</Label>
      <RadioGroup defaultValue="pro">
        <RadioGroupOption
          value="basic"
          id="basic"
          label="Basic Plan"
          description="Perfect for individuals getting started"
        />
        <RadioGroupOption
          value="pro"
          id="pro"
          label="Pro Plan"
          description="Great for growing teams and businesses"
        />
        <RadioGroupOption
          value="enterprise"
          id="enterprise"
          label="Enterprise Plan"
          description="Advanced features for large organizations"
        />
      </RadioGroup>
    </div>
  ),
}

export const Horizontal: Story = {
  render: () => (
    <div className="space-y-2">
      <Label>Select size</Label>
      <RadioGroup orientation="horizontal" defaultValue="medium">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="small" id="small" />
          <Label htmlFor="small">Small</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="medium" id="medium" />
          <Label htmlFor="medium">Medium</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="large" id="large" />
          <Label htmlFor="large">Large</Label>
        </div>
      </RadioGroup>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <Label>Small</Label>
        <RadioGroup size="sm" defaultValue="option1">
          <RadioGroupOption
            value="option1"
            id="sm1"
            label="Small option"
            size="sm"
          />
          <RadioGroupOption
            value="option2"
            id="sm2"
            label="Another small option"
            size="sm"
          />
        </RadioGroup>
      </div>
      
      <div>
        <Label>Default</Label>
        <RadioGroup size="default" defaultValue="option1">
          <RadioGroupOption
            value="option1"
            id="def1"
            label="Default option"
            size="default"
          />
          <RadioGroupOption
            value="option2"
            id="def2"
            label="Another default option"
            size="default"
          />
        </RadioGroup>
      </div>
      
      <div>
        <Label>Large</Label>
        <RadioGroup size="lg" defaultValue="option1">
          <RadioGroupOption
            value="option1"
            id="lg1"
            label="Large option"
            size="lg"
          />
          <RadioGroupOption
            value="option2"
            id="lg2"
            label="Another large option"
            size="lg"
          />
        </RadioGroup>
      </div>
    </div>
  ),
}

export const ErrorState: Story = {
  render: () => (
    <div className="space-y-2">
      <Label>This field has an error</Label>
      <RadioGroup defaultValue="option2">
        <RadioGroupOption
          value="option1"
          id="err1"
          label="Option 1"
          variant="error"
        />
        <RadioGroupOption
          value="option2"
          id="err2"
          label="Option 2 (selected with error)"
          variant="error"
        />
      </RadioGroup>
      <p className="text-xs text-red-600">Please make a different selection</p>
    </div>
  ),
}

export const DisabledState: Story = {
  render: () => (
    <div className="space-y-2">
      <Label>Disabled options</Label>
      <RadioGroup defaultValue="option1">
        <RadioGroupOption
          value="option1"
          id="dis1"
          label="Available option"
        />
        <RadioGroupOption
          value="option2"
          id="dis2"
          label="Disabled option"
          disabled
        />
        <RadioGroupOption
          value="option3"
          id="dis3"
          label="Another disabled option"
          disabled
        />
      </RadioGroup>
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="w-96 space-y-6 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">Preferences</h3>
      
      <div>
        <Label variant="required">Email notifications</Label>
        <RadioGroup defaultValue="daily" name="notifications">
          <RadioGroupOption
            value="never"
            id="never"
            label="Never"
            description="You won't receive any email notifications"
          />
          <RadioGroupOption
            value="daily"
            id="daily"
            label="Daily digest"
            description="Receive a summary once per day"
          />
          <RadioGroupOption
            value="immediate"
            id="immediate"
            label="Immediate"
            description="Get notified as soon as something happens"
          />
        </RadioGroup>
      </div>

      <div>
        <Label variant="required">Theme preference</Label>
        <RadioGroup orientation="horizontal" defaultValue="auto" name="theme">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="light" id="light" />
            <Label htmlFor="light">Light</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="dark" id="dark" />
            <Label htmlFor="dark">Dark</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="auto" id="auto" />
            <Label htmlFor="auto">Auto</Label>
          </div>
        </RadioGroup>
      </div>
    </form>
  ),
}