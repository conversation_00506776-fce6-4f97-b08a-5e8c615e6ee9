import type { Meta, StoryObj } from '@storybook/react-vite'
import { Textarea } from './Textarea'
import { Label } from './Label'

const meta: Meta<typeof Textarea> = {
  title: 'Components/Textarea',
  component: Textarea,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'error', 'success'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
    resize: {
      control: { type: 'select' },
      options: ['none', 'vertical', 'horizontal', 'both'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    autoResize: {
      control: { type: 'boolean' },
    },
    showCharCount: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    placeholder: 'Enter your message...',
    variant: 'default',
    size: 'default',
  },
  render: (args) => (
    <div className="w-80">
      <Textarea {...args} />
    </div>
  ),
}

export const WithLabel: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label htmlFor="message">Message</Label>
      <Textarea id="message" placeholder="Enter your message..." />
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>Small</Label>
        <Textarea size="sm" placeholder="Small textarea" />
      </div>
      <div>
        <Label>Default</Label>
        <Textarea size="default" placeholder="Default textarea" />
      </div>
      <div>
        <Label>Large</Label>
        <Textarea size="lg" placeholder="Large textarea" />
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>Default</Label>
        <Textarea variant="default" placeholder="Default variant" />
      </div>
      <div>
        <Label>Error</Label>
        <Textarea variant="error" placeholder="Error variant" />
      </div>
      <div>
        <Label>Success</Label>
        <Textarea variant="success" placeholder="Success variant" />
      </div>
    </div>
  ),
}

export const AutoResize: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Auto-resizing textarea</Label>
      <Textarea
        autoResize
        placeholder="Type multiple lines and watch me grow..."
        helperText="This textarea will automatically adjust its height as you type"
      />
    </div>
  ),
}

export const WithCharacterCount: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>With character count</Label>
        <Textarea
          showCharCount
          placeholder="Type your message..."
          helperText="Character count is shown"
        />
      </div>
      <div>
        <Label>With maximum length</Label>
        <Textarea
          showCharCount
          maxLength={100}
          placeholder="Limited to 100 characters..."
          helperText="Try typing more than 100 characters"
        />
      </div>
    </div>
  ),
}

export const ResizeOptions: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>No resize</Label>
        <Textarea resize="none" placeholder="Cannot be resized" />
      </div>
      <div>
        <Label>Vertical resize</Label>
        <Textarea resize="vertical" placeholder="Can be resized vertically" />
      </div>
      <div>
        <Label>Both directions</Label>
        <Textarea resize="both" placeholder="Can be resized in both directions" />
      </div>
    </div>
  ),
}

export const WithHelperText: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>With helper text</Label>
        <Textarea
          placeholder="Enter your feedback..."
          helperText="Your feedback helps us improve our service"
        />
      </div>
      <div>
        <Label>With error</Label>
        <Textarea
          placeholder="Enter description..."
          errorText="Description is required and must be at least 10 characters"
        />
      </div>
      <div>
        <Label>With success</Label>
        <Textarea
          placeholder="Enter content..."
          successText="Looks good! Content meets all requirements"
        />
      </div>
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="w-96 space-y-6 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">Feedback Form</h3>
      
      <div>
        <Label htmlFor="feedback" variant="required">Feedback</Label>
        <Textarea
          id="feedback"
          placeholder="Tell us about your experience..."
          showCharCount
          maxLength={500}
          autoResize
          helperText="Please provide detailed feedback"
        />
      </div>

      <div>
        <Label htmlFor="suggestions">Suggestions</Label>
        <Textarea
          id="suggestions"
          placeholder="Any suggestions for improvement?"
          size="sm"
          helperText="Optional - share any ideas you have"
        />
      </div>
    </form>
  ),
}