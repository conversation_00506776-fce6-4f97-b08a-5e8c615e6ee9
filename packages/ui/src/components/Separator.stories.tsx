import type { Meta, StoryObj } from '@storybook/react-vite'
import { Separator } from './Separator'

const meta: Meta<typeof Separator> = {
  title: 'Components/Separator',
  component: Separator,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Horizontal: Story = {
  args: {
    orientation: 'horizontal',
  },
  render: (args) => (
    <div className="w-64 space-y-4">
      <div>
        <h4 className="text-sm font-medium">Section 1</h4>
        <p className="text-sm text-gray-600">Content above separator</p>
      </div>
      <Separator {...args} />
      <div>
        <h4 className="text-sm font-medium">Section 2</h4>
        <p className="text-sm text-gray-600">Content below separator</p>
      </div>
    </div>
  ),
}

export const Vertical: Story = {
  args: {
    orientation: 'vertical',
  },
  render: (args) => (
    <div className="flex h-24 items-center space-x-4">
      <div className="text-center">
        <p className="text-sm">Left</p>
      </div>
      <Separator {...args} />
      <div className="text-center">
        <p className="text-sm">Center</p>
      </div>
      <Separator {...args} />
      <div className="text-center">
        <p className="text-sm">Right</p>
      </div>
    </div>
  ),
}

export const InMenu: Story = {
  render: () => (
    <div className="w-48 p-2 border rounded-lg">
      <div className="py-2 px-1">
        <p className="text-sm">Profile</p>
      </div>
      <Separator />
      <div className="py-2 px-1">
        <p className="text-sm">Settings</p>
      </div>
      <div className="py-2 px-1">
        <p className="text-sm">Preferences</p>
      </div>
      <Separator />
      <div className="py-2 px-1">
        <p className="text-sm">Logout</p>
      </div>
    </div>
  ),
}