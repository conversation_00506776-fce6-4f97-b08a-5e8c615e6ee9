import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const inputVariants = cva(
  'flex w-full rounded-md border bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'border-gray-300 focus-visible:ring-blue-500/30',
        error: 'border-red-500 focus-visible:ring-red-500/30 text-red-900 placeholder:text-red-400',
        success: 'border-green-500 focus-visible:ring-green-500/30 text-green-900 placeholder:text-green-400',
      },
      size: {
        sm: 'h-8 text-xs px-2',
        default: 'h-10 text-sm px-3',
        lg: 'h-12 text-base px-4',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  startIcon?: React.ReactNode
  endIcon?: React.ReactNode
  loading?: boolean
  helperText?: string
  errorText?: string
  successText?: string
}

const LoadingSpinner = ({ size = 16 }: { size?: number }) => (
  <svg
    className="animate-spin"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="m12 2a10 10 0 0 1 10 10h-4a6 6 0 0 0-6-6v-4z"
    />
  </svg>
)

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    variant,
    size,
    type = 'text',
    startIcon,
    endIcon,
    loading = false,
    helperText,
    errorText,
    successText,
    disabled,
    ...props
  }, ref) => {
    const hasStartIcon = startIcon || loading
    const hasEndIcon = endIcon
    const isDisabled = disabled || loading
    
    // Determine the actual variant based on error/success states
    const actualVariant = errorText ? 'error' : successText ? 'success' : variant
    
    // Helper text priority: errorText > successText > helperText
    const displayHelperText = errorText || successText || helperText
    const helperTextColor = errorText ? 'text-red-600' : successText ? 'text-green-600' : 'text-gray-600'

    const iconSize = size === 'sm' ? 14 : size === 'lg' ? 18 : 16
    const paddingLeft = hasStartIcon ? (size === 'sm' ? 'pl-8' : size === 'lg' ? 'pl-12' : 'pl-10') : undefined
    const paddingRight = hasEndIcon ? (size === 'sm' ? 'pr-8' : size === 'lg' ? 'pr-12' : 'pr-10') : undefined

    return (
      <div className="w-full">
        <div className="relative">
          {hasStartIcon && (
            <div className={clsx(
              'absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none',
              size === 'sm' && 'left-2',
              size === 'lg' && 'left-4'
            )}>
              {loading ? (
                <LoadingSpinner size={iconSize} />
              ) : (
                startIcon
              )}
            </div>
          )}
          
          <input
            type={type}
            className={clsx(
              inputVariants({ variant: actualVariant, size }),
              paddingLeft,
              paddingRight,
              className
            )}
            ref={ref}
            disabled={isDisabled}
            {...props}
          />
          
          {hasEndIcon && (
            <div className={clsx(
              'absolute right-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none',
              size === 'sm' && 'right-2',
              size === 'lg' && 'right-4'
            )}>
              {endIcon}
            </div>
          )}
        </div>
        
        {displayHelperText && (
          <p className={clsx('mt-1 text-xs', helperTextColor)}>
            {displayHelperText}
          </p>
        )}
      </div>
    )
  }
)
Input.displayName = 'Input'

export type { InputProps }
export { Input }

