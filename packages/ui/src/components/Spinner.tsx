import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const spinnerVariants = cva(
  [
    'inline-flex items-center justify-center',
    'animate-spin'
  ],
  {
    variants: {
      size: {
        sm: 'h-4 w-4',
        md: 'h-6 w-6', 
        lg: 'h-8 w-8',
        xl: 'h-12 w-12'
      }
    },
    defaultVariants: {
      size: 'md'
    }
  }
)

const spinnerCircleVariants = cva(
  [
    'stroke-current stroke-[3] fill-none stroke-linecap-round',
    'animate-[spin-dash_1.5s_ease-in-out_infinite]'
  ]
)

interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  asChild?: boolean
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'div'
    
    return (
      <Comp
        className={clsx(spinnerVariants({ size }), className)}
        ref={ref}
        {...props}
      >
        <svg viewBox="0 0 50 50" className="h-full w-full">
          <circle
            cx="25"
            cy="25"
            r="20"
            className={spinnerCircleVariants()}
          />
        </svg>
      </Comp>
    )
  }
)
Spinner.displayName = 'Spinner'

export type { SpinnerProps }
export { Spinner }