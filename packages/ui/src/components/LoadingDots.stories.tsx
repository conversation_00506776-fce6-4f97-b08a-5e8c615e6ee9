import type { Meta, StoryObj } from '@storybook/react'
import { LoadingDots } from './LoadingDots'

const meta: Meta<typeof LoadingDots> = {
  title: 'Components/LoadingDots',
  component: LoadingDots,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the loading dots',
    },
    asChild: {
      control: { type: 'boolean' },
      description: 'Render as child component',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    size: 'md',
  },
}

export const Small: Story = {
  args: {
    size: 'sm',
  },
}

export const Medium: Story = {
  args: {
    size: 'md',
  },
}

export const Large: Story = {
  args: {
    size: 'lg',
  },
}

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
  },
}

export const CustomColor: Story = {
  args: {
    size: 'md',
    className: 'text-blue-500',
  },
}

export const MultipleColors: Story = {
  render: () => (
    <div className="flex items-center gap-6">
      <LoadingDots size="md" className="text-blue-500" />
      <LoadingDots size="md" className="text-green-500" />
      <LoadingDots size="md" className="text-red-500" />
      <LoadingDots size="md" className="text-purple-500" />
      <LoadingDots size="md" className="text-orange-500" />
    </div>
  ),
}

export const SizeComparison: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <LoadingDots size="sm" />
        <p className="mt-2 text-xs text-gray-600">Small</p>
      </div>
      <div className="text-center">
        <LoadingDots size="md" />
        <p className="mt-2 text-xs text-gray-600">Medium</p>
      </div>
      <div className="text-center">
        <LoadingDots size="lg" />
        <p className="mt-2 text-xs text-gray-600">Large</p>
      </div>
      <div className="text-center">
        <LoadingDots size="xl" />
        <p className="mt-2 text-xs text-gray-600">Extra Large</p>
      </div>
    </div>
  ),
}

export const InText: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <p className="flex items-center gap-2 text-sm">
        Loading content
        <LoadingDots size="sm" />
      </p>
      <p className="flex items-center gap-2">
        Processing request
        <LoadingDots size="md" className="text-blue-500" />
      </p>
      <p className="flex items-center gap-2 text-lg font-semibold">
        Please wait
        <LoadingDots size="lg" className="text-green-500" />
      </p>
    </div>
  ),
}

export const InButton: Story = {
  render: () => (
    <div className="flex gap-4">
      <button className="inline-flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50" disabled>
        Sending
        <LoadingDots size="sm" />
      </button>
      <button className="inline-flex items-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50 disabled:opacity-50" disabled>
        <LoadingDots size="sm" className="text-gray-500" />
        Loading
      </button>
    </div>
  ),
}

export const LoadingStates: Story = {
  render: () => (
    <div className="grid gap-4">
      <div className="flex items-center justify-between rounded-lg border p-4">
        <span>Connecting to server</span>
        <LoadingDots size="sm" className="text-blue-500" />
      </div>
      <div className="flex items-center justify-between rounded-lg border p-4">
        <span>Uploading files</span>
        <LoadingDots size="sm" className="text-green-500" />
      </div>
      <div className="flex items-center justify-between rounded-lg border p-4">
        <span>Processing data</span>
        <LoadingDots size="sm" className="text-orange-500" />
      </div>
    </div>
  ),
}