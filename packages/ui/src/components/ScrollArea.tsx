import * as React from 'react'
import * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const scrollAreaVariants = cva(
  'relative overflow-hidden',
  {
    variants: {
      size: {
        sm: 'h-32',
        default: 'h-64',
        lg: 'h-96',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
)

const scrollbarVariants = cva(
  'flex touch-none select-none transition-colors',
  {
    variants: {
      orientation: {
        vertical: 'h-full w-2.5 border-l border-l-transparent p-[1px]',
        horizontal: 'h-2.5 flex-col border-t border-t-transparent p-[1px]',
      },
      width: {
        thin: '',
        default: '',
        thick: '',
      },
    },
    compoundVariants: [
      {
        orientation: 'vertical',
        width: 'thin',
        class: 'w-1.5',
      },
      {
        orientation: 'vertical',
        width: 'thick',
        class: 'w-4',
      },
      {
        orientation: 'horizontal',
        width: 'thin',
        class: 'h-1.5',
      },
      {
        orientation: 'horizontal',
        width: 'thick',
        class: 'h-4',
      },
    ],
    defaultVariants: {
      orientation: 'vertical',
      width: 'default',
    },
  }
)

const scrollThumbVariants = cva(
  'relative flex-1 rounded-full bg-gray-400 hover:bg-gray-500 transition-colors',
  {
    variants: {
      variant: {
        default: '',
        minimal: 'bg-gray-300 hover:bg-gray-400',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

interface ScrollAreaProps
  extends React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>,
    VariantProps<typeof scrollAreaVariants> {}

const ScrollArea = React.forwardRef<
  React.ComponentRef<typeof ScrollAreaPrimitive.Root>,
  ScrollAreaProps
>(({ className, size, ...props }, ref) => (
  <ScrollAreaPrimitive.Root
    ref={ref}
    className={clsx(scrollAreaVariants({ size, className }))}
    {...props}
  />
))
ScrollArea.displayName = 'ScrollArea'

const ScrollAreaViewport = React.forwardRef<
  React.ComponentRef<typeof ScrollAreaPrimitive.Viewport>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Viewport>
>(({ className, ...props }, ref) => (
  <ScrollAreaPrimitive.Viewport
    ref={ref}
    className={clsx('h-full w-full rounded-[inherit]', className)}
    {...props}
  />
))
ScrollAreaViewport.displayName = 'ScrollAreaViewport'

interface ScrollAreaScrollbarProps
  extends Omit<React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Scrollbar>, 'orientation'>,
    VariantProps<typeof scrollbarVariants> {}

const ScrollAreaScrollbar = React.forwardRef<
  React.ComponentRef<typeof ScrollAreaPrimitive.Scrollbar>,
  ScrollAreaScrollbarProps
>(({ className, orientation = 'vertical', width, ...props }, ref) => (
  <ScrollAreaPrimitive.Scrollbar
    ref={ref}
    orientation={orientation ?? undefined}
    className={clsx(scrollbarVariants({ orientation, width, className }))}
    {...props}
  />
))
ScrollAreaScrollbar.displayName = 'ScrollAreaScrollbar'

interface ScrollAreaThumbProps
  extends React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Thumb>,
    VariantProps<typeof scrollThumbVariants> {}

const ScrollAreaThumb = React.forwardRef<
  React.ComponentRef<typeof ScrollAreaPrimitive.Thumb>,
  ScrollAreaThumbProps
>(({ className, variant, ...props }, ref) => (
  <ScrollAreaPrimitive.Thumb
    ref={ref}
    className={clsx(scrollThumbVariants({ variant, className }))}
    {...props}
  />
))
ScrollAreaThumb.displayName = 'ScrollAreaThumb'

const ScrollAreaCorner = React.forwardRef<
  React.ComponentRef<typeof ScrollAreaPrimitive.Corner>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Corner>
>(({ className, ...props }, ref) => (
  <ScrollAreaPrimitive.Corner
    ref={ref}
    className={clsx('bg-gray-200', className)}
    {...props}
  />
))
ScrollAreaCorner.displayName = 'ScrollAreaCorner'

// Type exports
export type { ScrollAreaProps, ScrollAreaScrollbarProps, ScrollAreaThumbProps }

// Component exports
export {
  ScrollArea,
  ScrollAreaViewport,
  ScrollAreaScrollbar,
  ScrollAreaThumb,
  ScrollAreaCorner,
}