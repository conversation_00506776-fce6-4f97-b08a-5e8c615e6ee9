import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { <PERSON><PERSON> } from './Button'

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['contained', 'outlined', 'text', 'default', 'destructive', 'secondary', 'ghost', 'link'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large', 'default', 'sm', 'lg', 'icon'],
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'error', 'warning', 'info', 'success'],
    },
    loading: {
      control: { type: 'boolean' },
    },
    loadingPosition: {
      control: { type: 'select' },
      options: ['start', 'end', 'center'],
    },
    fullWidth: {
      control: { type: 'boolean' },
    },
    disableElevation: {
      control: { type: 'boolean' },
    },
    disableRipple: {
      control: { type: 'boolean' },
    },
    asChild: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

const HeartIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
  </svg>
)

const ArrowIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M5 12h14M12 5l7 7-7 7"/>
  </svg>
)

export const Default: Story = {
  args: {
    children: 'Button',
  },
}

export const Contained: Story = {
  args: {
    variant: 'contained',
    children: 'Contained',
  },
}

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: 'Outlined',
  },
}

export const Text: Story = {
  args: {
    variant: 'text',
    children: 'Text',
  },
}

export const Colors: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" color="primary">Primary</Button>
      <Button variant="contained" color="secondary">Secondary</Button>
      <Button variant="contained" color="error">Error</Button>
      <Button variant="contained" color="warning">Warning</Button>
      <Button variant="contained" color="info">Info</Button>
      <Button variant="contained" color="success">Success</Button>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" color="primary">Contained</Button>
      <Button variant="outlined" color="primary">Outlined</Button>
      <Button variant="text" color="primary">Text</Button>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex flex-wrap items-center gap-4">
      <Button variant="contained" size="small">Small</Button>
      <Button variant="contained" size="medium">Medium</Button>
      <Button variant="contained" size="large">Large</Button>
    </div>
  ),
}

export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" startIcon={<HeartIcon />}>
        Like
      </Button>
      <Button variant="outlined" endIcon={<ArrowIcon />}>
        Next
      </Button>
      <Button variant="contained" startIcon={<HeartIcon />} endIcon={<ArrowIcon />}>
        Both Icons
      </Button>
    </div>
  ),
}

export const Loading: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" loading>
        Loading Start
      </Button>
      <Button variant="contained" loading loadingPosition="end">
        Loading End
      </Button>
      <Button variant="contained" loading loadingPosition="center">
        Loading Center
      </Button>
    </div>
  ),
}

export const LoadingWithIcons: Story = {
  args: {
    variant: 'contained',
    loading: true,
    startIcon: <HeartIcon />,
    children: 'Loading...',
  },
}

export const FullWidth: Story = {
  args: {
    variant: 'contained',
    fullWidth: true,
    children: 'Full Width Button',
  },
  parameters: {
    layout: 'padded',
  },
}

export const DisabledStates: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" disabled>
        Disabled
      </Button>
      <Button variant="outlined" disabled>
        Disabled
      </Button>
      <Button variant="text" disabled>
        Disabled
      </Button>
    </div>
  ),
}

export const NoElevation: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" color="primary">
        With Elevation
      </Button>
      <Button variant="contained" color="primary" disableElevation>
        No Elevation
      </Button>
    </div>
  ),
}

export const NoRipple: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="contained" color="primary">
        With Ripple
      </Button>
      <Button variant="contained" color="primary" disableRipple>
        No Ripple
      </Button>
    </div>
  ),
}

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Delete',
  },
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Button',
  },
}

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Button',
  },
}

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Link',
  },
}

export const Icon: Story = {
  args: {
    size: 'icon',
    variant: 'contained',
    children: <HeartIcon />,
  },
}