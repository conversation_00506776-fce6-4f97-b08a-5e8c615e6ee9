import type { Meta, StoryObj } from '@storybook/react-vite'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
  HoverCardHeader,
  HoverCardAvatar,
  HoverCardTitle,
  HoverCardDescription,
  HoverCardFooter,
} from './HoverCard'

const meta: Meta<typeof HoverCard> = {
  title: 'Components/HoverCard',
  component: HoverCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <HoverCard>
      <HoverCardTrigger asChild>
        <button className="inline-flex items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          @nextjs
        </button>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <HoverCardHeader>
          <HoverCardAvatar
            src="https://github.com/vercel.png"
            alt="Vercel"
            fallback="V"
          />
          <div className="space-y-1">
            <HoverCardTitle>@nextjs</HoverCardTitle>
            <HoverCardDescription>
              The React Framework – created and maintained by @vercel.
            </HoverCardDescription>
          </div>
        </HoverCardHeader>
        <HoverCardFooter>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className="h-3 w-3"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
              </svg>
              <span>0 Followers</span>
            </div>
            <div className="flex items-center space-x-1">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className="h-3 w-3"
              >
                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" />
              </svg>
              <span>20k+ Stars</span>
            </div>
          </div>
        </HoverCardFooter>
      </HoverCardContent>
    </HoverCard>
  ),
}

export const WithoutAvatar: Story = {
  render: () => (
    <HoverCard>
      <HoverCardTrigger asChild>
        <a
          className="inline-block rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          href="#"
        >
          Hover me
        </a>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="space-y-2">
          <HoverCardTitle>Quick Info</HoverCardTitle>
          <HoverCardDescription>
            This is a hover card without an avatar. It can contain any content you need to display in a floating card that appears on hover.
          </HoverCardDescription>
        </div>
      </HoverCardContent>
    </HoverCard>
  ),
}

export const UserProfile: Story = {
  render: () => (
    <div className="space-y-4">
      <p className="text-gray-600">Hover over the users below to see their profiles:</p>
      <div className="flex space-x-4">
        <HoverCard>
          <HoverCardTrigger asChild>
            <button className="flex items-center space-x-2 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm hover:bg-gray-50">
              <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center text-white text-xs font-medium">
                JD
              </div>
              <span>John Doe</span>
            </button>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <HoverCardHeader>
              <HoverCardAvatar fallback="JD" />
              <div className="space-y-1">
                <HoverCardTitle>John Doe</HoverCardTitle>
                <HoverCardDescription>
                  Senior Software Engineer at Acme Corp. Passionate about React, TypeScript, and building great user experiences.
                </HoverCardDescription>
              </div>
            </HoverCardHeader>
            <HoverCardFooter>
              <div className="text-xs text-gray-500">
                Joined March 2021
              </div>
            </HoverCardFooter>
          </HoverCardContent>
        </HoverCard>

        <HoverCard>
          <HoverCardTrigger asChild>
            <button className="flex items-center space-x-2 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm hover:bg-gray-50">
              <div className="h-6 w-6 rounded-full bg-green-600 flex items-center justify-center text-white text-xs font-medium">
                AS
              </div>
              <span>Alice Smith</span>
            </button>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <HoverCardHeader>
              <HoverCardAvatar fallback="AS" />
              <div className="space-y-1">
                <HoverCardTitle>Alice Smith</HoverCardTitle>
                <HoverCardDescription>
                  Product Designer focused on creating intuitive and accessible interfaces. Currently working on design systems.
                </HoverCardDescription>
              </div>
            </HoverCardHeader>
            <HoverCardFooter>
              <div className="text-xs text-gray-500">
                Joined January 2022
              </div>
            </HoverCardFooter>
          </HoverCardContent>
        </HoverCard>
      </div>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex space-x-8">
      <HoverCard>
        <HoverCardTrigger asChild>
          <button className="rounded-md bg-gray-100 px-3 py-2 text-sm">
            Small
          </button>
        </HoverCardTrigger>
        <HoverCardContent size="sm">
          <HoverCardTitle>Small Card</HoverCardTitle>
          <HoverCardDescription>
            This is a small hover card.
          </HoverCardDescription>
        </HoverCardContent>
      </HoverCard>

      <HoverCard>
        <HoverCardTrigger asChild>
          <button className="rounded-md bg-gray-100 px-3 py-2 text-sm">
            Default
          </button>
        </HoverCardTrigger>
        <HoverCardContent>
          <HoverCardTitle>Default Card</HoverCardTitle>
          <HoverCardDescription>
            This is a default sized hover card with more content space.
          </HoverCardDescription>
        </HoverCardContent>
      </HoverCard>

      <HoverCard>
        <HoverCardTrigger asChild>
          <button className="rounded-md bg-gray-100 px-3 py-2 text-sm">
            Large
          </button>
        </HoverCardTrigger>
        <HoverCardContent size="lg">
          <HoverCardTitle>Large Card</HoverCardTitle>
          <HoverCardDescription>
            This is a large hover card that can contain much more detailed information and content for comprehensive details.
          </HoverCardDescription>
        </HoverCardContent>
      </HoverCard>
    </div>
  ),
}

export const ProductPreview: Story = {
  render: () => (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Product Catalog</h3>
      <div className="grid grid-cols-2 gap-4">
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="cursor-pointer rounded-lg border bg-white p-4 hover:shadow-md transition-shadow">
              <div className="h-20 w-full rounded bg-gradient-to-br from-blue-100 to-blue-200 mb-2"></div>
              <h4 className="font-medium">MacBook Pro</h4>
              <p className="text-sm text-gray-600">$1,999</p>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="space-y-3">
              <div className="h-40 w-full rounded-lg bg-gradient-to-br from-blue-100 to-blue-200"></div>
              <div>
                <HoverCardTitle>MacBook Pro 14"</HoverCardTitle>
                <HoverCardDescription>
                  Supercharged by M2 Pro and M2 Max chips. With a stunning Liquid Retina XDR display, all the ports you need, and all-day battery life.
                </HoverCardDescription>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-2xl font-bold">$1,999</span>
                <button className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700">
                  Learn More
                </button>
              </div>
            </div>
          </HoverCardContent>
        </HoverCard>

        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="cursor-pointer rounded-lg border bg-white p-4 hover:shadow-md transition-shadow">
              <div className="h-20 w-full rounded bg-gradient-to-br from-purple-100 to-purple-200 mb-2"></div>
              <h4 className="font-medium">iPhone 15</h4>
              <p className="text-sm text-gray-600">$799</p>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="space-y-3">
              <div className="h-40 w-full rounded-lg bg-gradient-to-br from-purple-100 to-purple-200"></div>
              <div>
                <HoverCardTitle>iPhone 15</HoverCardTitle>
                <HoverCardDescription>
                  A camera that captures your wildest imagination. A chip that runs console-level games. And a USB-C connector that charges fast.
                </HoverCardDescription>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-2xl font-bold">$799</span>
                <button className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700">
                  Learn More
                </button>
              </div>
            </div>
          </HoverCardContent>
        </HoverCard>
      </div>
    </div>
  ),
}