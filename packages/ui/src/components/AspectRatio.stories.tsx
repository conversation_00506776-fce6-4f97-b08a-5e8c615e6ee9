import type { Meta, StoryObj } from '@storybook/react-vite'
import { AspectRatio } from './AspectRatio'

const meta = {
  title: 'Components/AspectRatio',
  component: AspectRatio,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    ratio: {
      control: 'select',
      options: ['square', 'photo', 'video', 'cinema', 'portrait', 'wide'],
      description: 'Predefined aspect ratio or custom number',
    },
  },
} satisfies Meta<typeof AspectRatio>

export default meta
type Story = StoryObj<typeof meta>

const PlaceholderContent = ({ children }: { children: React.ReactNode }) => (
  <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-500">
    {children}
  </div>
)

export const Default: Story = {
  args: {
    ratio: 'video',
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>16:9 Video</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const Square: Story = {
  args: {
    ratio: 'square',
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>1:1 Square</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const Photo: Story = {
  args: {
    ratio: 'photo',
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>4:3 Photo</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const Cinema: Story = {
  args: {
    ratio: 'cinema',
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>21:9 Cinema</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const Portrait: Story = {
  args: {
    ratio: 'portrait',
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>3:4 Portrait</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const CustomRatio: Story = {
  args: {
    ratio: 2.5,
  },
  render: (args) => (
    <div className="w-80">
      <AspectRatio {...args}>
        <PlaceholderContent>2.5:1 Custom</PlaceholderContent>
      </AspectRatio>
    </div>
  ),
}

export const WithImage: Story = {
  render: () => (
    <div className="w-80">
      <AspectRatio ratio="photo">
        <img
          src="https://picsum.photos/400/300"
          alt="Sample image"
          className="h-full w-full object-cover"
        />
      </AspectRatio>
    </div>
  ),
}

export const WithChart: Story = {
  render: () => (
    <div className="w-80">
      <AspectRatio ratio="video">
        <div className="flex h-full w-full items-end justify-around bg-gradient-to-t from-blue-50 to-white p-4">
          <div className="h-1/4 w-6 bg-blue-500 rounded-t" />
          <div className="h-3/4 w-6 bg-blue-500 rounded-t" />
          <div className="h-1/2 w-6 bg-blue-500 rounded-t" />
          <div className="h-5/6 w-6 bg-blue-500 rounded-t" />
          <div className="h-2/3 w-6 bg-blue-500 rounded-t" />
        </div>
      </AspectRatio>
    </div>
  ),
}

export const Responsive: Story = {
  render: () => (
    <div className="max-w-4xl">
      <AspectRatio ratio="video" className="rounded-lg overflow-hidden">
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 text-white flex items-center justify-center text-lg font-semibold">
          Responsive 16:9 Container
        </div>
      </AspectRatio>
    </div>
  ),
}

export const AllRatios: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 w-full max-w-4xl">
      <div>
        <h3 className="mb-2 text-sm font-medium">Square (1:1)</h3>
        <AspectRatio ratio="square">
          <PlaceholderContent>1:1</PlaceholderContent>
        </AspectRatio>
      </div>
      <div>
        <h3 className="mb-2 text-sm font-medium">Photo (4:3)</h3>
        <AspectRatio ratio="photo">
          <PlaceholderContent>4:3</PlaceholderContent>
        </AspectRatio>
      </div>
      <div>
        <h3 className="mb-2 text-sm font-medium">Video (16:9)</h3>
        <AspectRatio ratio="video">
          <PlaceholderContent>16:9</PlaceholderContent>
        </AspectRatio>
      </div>
      <div>
        <h3 className="mb-2 text-sm font-medium">Cinema (21:9)</h3>
        <AspectRatio ratio="cinema">
          <PlaceholderContent>21:9</PlaceholderContent>
        </AspectRatio>
      </div>
      <div>
        <h3 className="mb-2 text-sm font-medium">Portrait (3:4)</h3>
        <AspectRatio ratio="portrait">
          <PlaceholderContent>3:4</PlaceholderContent>
        </AspectRatio>
      </div>
      <div>
        <h3 className="mb-2 text-sm font-medium">Wide (2:1)</h3>
        <AspectRatio ratio="wide">
          <PlaceholderContent>2:1</PlaceholderContent>
        </AspectRatio>
      </div>
    </div>
  ),
}