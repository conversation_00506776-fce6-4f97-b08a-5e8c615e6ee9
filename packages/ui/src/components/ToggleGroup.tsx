import * as React from 'react'
import * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const toggleGroupVariants = cva(
  'flex items-center justify-center gap-1',
  {
    variants: {
      variant: {
        default: '',
        outline: 'rounded-md border border-gray-200 p-1',
        ghost: '',
      },
      size: {
        default: '',
        sm: '',
        lg: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const toggleGroupItemVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer aria-pressed:bg-primary aria-pressed:text-primary-foreground aria-pressed:shadow-sm aria-checked:bg-primary aria-checked:text-primary-foreground aria-checked:shadow-sm',
  {
    variants: {
      variant: {
        default: 'hover:bg-gray-100 hover:text-gray-900',
        outline: 'border border-gray-200 bg-transparent hover:bg-gray-100 hover:text-gray-900 aria-pressed:border-primary aria-checked:border-primary',
        ghost: 'hover:bg-gray-100 hover:text-gray-900',
      },
      size: {
        default: 'h-10 px-3',
        sm: 'h-9 px-2.5 text-xs',
        lg: 'h-11 px-5',
        icon: 'h-10 w-10 p-0',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export type ToggleGroupSingleProps = Omit<ToggleGroupPrimitive.ToggleGroupSingleProps, 'type'> &
  VariantProps<typeof toggleGroupVariants>;

export type ToggleGroupMultipleProps = Omit<ToggleGroupPrimitive.ToggleGroupMultipleProps, 'type'> &
  VariantProps<typeof toggleGroupVariants>;

const ToggleGroup = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Root>,
  ToggleGroupSingleProps
>(({ className, variant, size, children, ...props }, ref) => (
  <ToggleGroupPrimitive.Root
    ref={ref}
    type='single'
    className={clsx(toggleGroupVariants({ variant, size, className }))}
    {...props}
  >
    {children}
  </ToggleGroupPrimitive.Root>
))

ToggleGroup.displayName = 'ToggleGroup'

const ToggleMultiGroup = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Root>,
  ToggleGroupMultipleProps
>(({ className, variant, size, children, ...props }, ref) => (
  <ToggleGroupPrimitive.Root
    ref={ref}
    type='multiple'
    className={clsx(toggleGroupVariants({ variant, size, className }))}
    {...props}
  >
    {children}
  </ToggleGroupPrimitive.Root>
))

ToggleMultiGroup.displayName = ToggleGroupPrimitive.Root.displayName

interface ToggleGroupItemProps
  extends React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item>,
    VariantProps<typeof toggleGroupItemVariants> {}

const ToggleGroupItem = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Item>,
  ToggleGroupItemProps
>(({ className, variant, size, ...props }, ref) => (
  <ToggleGroupPrimitive.Item
    ref={ref}
    className={clsx(toggleGroupItemVariants({ variant, size, className }))}
    {...props}
  />
))

ToggleGroupItem.displayName = 'ToggleGroupItem'

// Type exports
export type { ToggleGroupItemProps }

// Component exports
export {
  ToggleGroup,
  ToggleMultiGroup,
  ToggleGroupItem,
}