import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { <PERSON>lider } from './Slider'
import { Label } from './Label'

const meta: Meta<typeof Slider> = {
  title: 'Components/Slider',
  component: Slider,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'success', 'warning', 'error'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    showValue: {
      control: { type: 'boolean' },
    },
    showTicks: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    defaultValue: [50],
    max: 100,
    step: 1,
    orientation: 'horizontal',
    size: 'default',
    variant: 'default',
  },
  render: (args) => (
    <div className="w-80 space-y-2">
      <Label>Volume</Label>
      <Slider {...args} />
    </div>
  ),
}

export const WithValue: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Temperature</Label>
      <Slider
        defaultValue={[22]}
        min={0}
        max={40}
        step={1}
        showValue
      />
    </div>
  ),
}

export const RangeSlider: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Price range</Label>
      <Slider
        defaultValue={[25, 75]}
        max={100}
        step={5}
        showValue
      />
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <Label>Small</Label>
        <Slider size="sm" defaultValue={[30]} showValue />
      </div>
      <div>
        <Label>Default</Label>
        <Slider size="default" defaultValue={[50]} showValue />
      </div>
      <div>
        <Label>Large</Label>
        <Slider size="lg" defaultValue={[70]} showValue />
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <Label>Default</Label>
        <Slider variant="default" defaultValue={[40]} showValue />
      </div>
      <div>
        <Label>Success</Label>
        <Slider variant="success" defaultValue={[60]} showValue />
      </div>
      <div>
        <Label>Warning</Label>
        <Slider variant="warning" defaultValue={[80]} showValue />
      </div>
      <div>
        <Label>Error</Label>
        <Slider variant="error" defaultValue={[90]} showValue />
      </div>
    </div>
  ),
}

export const WithTicks: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Brightness</Label>
      <Slider
        defaultValue={[3]}
        min={0}
        max={5}
        step={1}
        showTicks
        showValue
      />
    </div>
  ),
}

export const WithMarks: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Rating</Label>
      <Slider
        defaultValue={[3]}
        min={1}
        max={5}
        step={1}
        marks={[
          { value: 1, label: 'Poor' },
          { value: 2, label: 'Fair' },
          { value: 3, label: 'Good' },
          { value: 4, label: 'Very Good' },
          { value: 5, label: 'Excellent' },
        ]}
        showValue
      />
    </div>
  ),
}

export const Vertical: Story = {
  render: () => (
    <div className="flex space-x-8 items-start">
      <div className="space-y-2">
        <Label>Volume</Label>
        <Slider
          orientation="vertical"
          defaultValue={[60]}
          max={100}
          showValue
        />
      </div>
      <div className="space-y-2">
        <Label>EQ Range</Label>
        <Slider
          orientation="vertical"
          defaultValue={[20, 80]}
          max={100}
          showValue
        />
      </div>
    </div>
  ),
}

export const Disabled: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label>Disabled slider</Label>
      <Slider
        defaultValue={[40]}
        disabled
        showValue
      />
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="w-96 space-y-6 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">Audio Settings</h3>
      
      <div>
        <Label>Master Volume</Label>
        <Slider
          defaultValue={[75]}
          max={100}
          showValue
          variant="default"
        />
      </div>

      <div>
        <Label>Bass/Treble Balance</Label>
        <Slider
          defaultValue={[30, 70]}
          max={100}
          showValue
          variant="success"
        />
      </div>

      <div>
        <Label>Fade (Front/Rear)</Label>
        <Slider
          defaultValue={[50]}
          min={0}
          max={100}
          marks={[
            { value: 0, label: 'Front' },
            { value: 50, label: 'Center' },
            { value: 100, label: 'Rear' },
          ]}
          showValue
        />
      </div>

      <div>
        <Label>Equalizer Preset</Label>
        <Slider
          defaultValue={[2]}
          min={0}
          max={4}
          step={1}
          marks={[
            { value: 0, label: 'Rock' },
            { value: 1, label: 'Pop' },
            { value: 2, label: 'Jazz' },
            { value: 3, label: 'Classical' },
            { value: 4, label: 'Custom' },
          ]}
        />
      </div>
    </form>
  ),
}