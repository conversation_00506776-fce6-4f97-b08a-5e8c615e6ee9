import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Skeleton } from './Skeleton'

const meta: Meta<typeof Skeleton> = {
  title: 'Components/Skeleton',
  component: Skeleton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'text', 'circular', 'rectangular'],
      description: 'Shape variant of the skeleton',
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Height size of the skeleton',
    },
    width: {
      control: { type: 'text' },
      description: 'Custom width (string or number)',
    },
    height: {
      control: { type: 'text' },
      description: 'Custom height (string or number)',
    },
    asChild: {
      control: { type: 'boolean' },
      description: 'Render as child component',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    variant: 'default',
    size: 'md',
    width: '200px',
  },
}

export const Text: Story = {
  args: {
    variant: 'text',
    size: 'md',
    width: '180px',
  },
}

export const Circular: Story = {
  args: {
    variant: 'circular',
    width: '40px',
    height: '40px',
  },
}

export const Rectangular: Story = {
  args: {
    variant: 'rectangular',
    width: '200px',
    height: '120px',
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Small</p>
        <Skeleton size="sm" width="200px" />
      </div>
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Medium</p>
        <Skeleton size="md" width="200px" />
      </div>
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Large</p>
        <Skeleton size="lg" width="200px" />
      </div>
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Extra Large</p>
        <Skeleton size="xl" width="200px" />
      </div>
    </div>
  ),
}

export const TextContent: Story = {
  render: () => (
    <div className="max-w-sm space-y-2">
      <Skeleton variant="text" size="lg" width="80%" />
      <Skeleton variant="text" size="md" width="100%" />
      <Skeleton variant="text" size="md" width="90%" />
      <Skeleton variant="text" size="sm" width="60%" />
    </div>
  ),
}

export const CardLayout: Story = {
  render: () => (
    <div className="w-80 rounded-lg border p-6 shadow">
      <div className="flex items-center space-x-4">
        <Skeleton variant="circular" width="50px" height="50px" />
        <div className="space-y-2 flex-1">
          <Skeleton variant="text" size="md" width="120px" />
          <Skeleton variant="text" size="sm" width="80px" />
        </div>
      </div>
      <div className="mt-6 space-y-3">
        <Skeleton variant="rectangular" width="100%" height="200px" />
        <div className="space-y-2">
          <Skeleton variant="text" size="md" width="100%" />
          <Skeleton variant="text" size="md" width="90%" />
          <Skeleton variant="text" size="md" width="75%" />
        </div>
      </div>
    </div>
  ),
}

export const ListItems: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
          <Skeleton variant="circular" width="40px" height="40px" />
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" size="md" width="60%" />
            <Skeleton variant="text" size="sm" width="40%" />
          </div>
          <Skeleton variant="default" width="60px" height="24px" />
        </div>
      ))}
    </div>
  ),
}

export const TableRows: Story = {
  render: () => (
    <div className="w-full max-w-2xl">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Name</th>
            <th className="text-left p-2">Email</th>
            <th className="text-left p-2">Role</th>
            <th className="text-left p-2">Status</th>
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: 5 }).map((_, i) => (
            <tr key={i} className="border-b">
              <td className="p-2">
                <Skeleton variant="text" size="md" width="120px" />
              </td>
              <td className="p-2">
                <Skeleton variant="text" size="md" width="180px" />
              </td>
              <td className="p-2">
                <Skeleton variant="text" size="md" width="80px" />
              </td>
              <td className="p-2">
                <Skeleton variant="default" width="60px" height="20px" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
}

export const CustomDimensions: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Custom width and height</p>
        <Skeleton width="300px" height="60px" />
      </div>
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Responsive width</p>
        <Skeleton width="100%" height="40px" className="max-w-lg" />
      </div>
      <div className="space-y-2">
        <p className="text-sm text-gray-600">Numeric dimensions</p>
        <Skeleton width={250} height={80} />
      </div>
    </div>
  ),
}