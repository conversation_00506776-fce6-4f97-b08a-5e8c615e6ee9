import * as React from 'react'
import * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden'
import { clsx } from 'clsx'

interface VisuallyHiddenProps
  extends React.ComponentPropsWithoutRef<typeof VisuallyHiddenPrimitive.Root> {}

const VisuallyHidden = React.forwardRef<
  React.ComponentRef<typeof VisuallyHiddenPrimitive.Root>,
  VisuallyHiddenProps
>(({ className, ...props }, ref) => (
  <VisuallyHiddenPrimitive.Root
    ref={ref}
    className={clsx(className)}
    {...props}
  />
))
VisuallyHidden.displayName = VisuallyHiddenPrimitive.Root.displayName

export type { VisuallyHiddenProps }
export { VisuallyHidden }