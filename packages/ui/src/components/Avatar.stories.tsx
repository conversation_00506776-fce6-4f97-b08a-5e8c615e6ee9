import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { Avatar, AvatarImage, AvatarFallback } from './Avatar'

const meta: Meta<typeof Avatar> = {
  title: 'Components/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg', 'xl'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    size: 'default',
  },
  render: (args) => (
    <Avatar {...args}>
      <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=128&h=128&fit=crop&crop=face" alt="User" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
}

export const WithFallback: Story = {
  args: {
    size: 'default',
  },
  render: (args) => (
    <Avatar {...args}>
      <AvatarImage src="https://broken-image-url.jpg" alt="User" />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <div className="text-center">
        <Avatar size="sm">
          <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=64&h=64&fit=crop&crop=face" alt="Small" />
          <AvatarFallback>SM</AvatarFallback>
        </Avatar>
        <p className="text-xs mt-2">Small</p>
      </div>
      <div className="text-center">
        <Avatar size="default">
          <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=80&h=80&fit=crop&crop=face" alt="Default" />
          <AvatarFallback>DF</AvatarFallback>
        </Avatar>
        <p className="text-xs mt-2">Default</p>
      </div>
      <div className="text-center">
        <Avatar size="lg">
          <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=128&h=128&fit=crop&crop=face" alt="Large" />
          <AvatarFallback>LG</AvatarFallback>
        </Avatar>
        <p className="text-xs mt-2">Large</p>
      </div>
      <div className="text-center">
        <Avatar size="xl">
          <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=160&h=160&fit=crop&crop=face" alt="Extra Large" />
          <AvatarFallback>XL</AvatarFallback>
        </Avatar>
        <p className="text-xs mt-2">Extra Large</p>
      </div>
    </div>
  ),
}

export const FallbackExamples: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <Avatar>
        <AvatarFallback>AB</AvatarFallback>
      </Avatar>
      <Avatar>
        <AvatarFallback>CD</AvatarFallback>
      </Avatar>
      <Avatar>
        <AvatarFallback>EF</AvatarFallback>
      </Avatar>
      <Avatar>
        <AvatarFallback>GH</AvatarFallback>
      </Avatar>
    </div>
  ),
}

export const Group: Story = {
  render: () => (
    <div className="flex -space-x-2">
      <Avatar className="border-2 border-white">
        <AvatarImage src="https://images.unsplash.com/photo-1492633423870-43d1cd2775eb?w=80&h=80&fit=crop&crop=face" alt="User 1" />
        <AvatarFallback>U1</AvatarFallback>
      </Avatar>
      <Avatar className="border-2 border-white">
        <AvatarImage src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=80&h=80&fit=crop&crop=face" alt="User 2" />
        <AvatarFallback>U2</AvatarFallback>
      </Avatar>
      <Avatar className="border-2 border-white">
        <AvatarImage src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" alt="User 3" />
        <AvatarFallback>U3</AvatarFallback>
      </Avatar>
      <Avatar className="border-2 border-white">
        <AvatarFallback>+2</AvatarFallback>
      </Avatar>
    </div>
  ),
}