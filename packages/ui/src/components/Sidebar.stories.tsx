import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"
import {
  Sidebar,
  SidebarProvider,
  SidebarTrigger,
  SidebarRail,
  SidebarInset,
  SidebarInput,
  SidebarHeader,
  SidebarFooter,
  SidebarSeparator,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "./Sidebar"

const meta = {
  title: "Components/Sidebar",
  component: Sidebar,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: "A highly composable, accessible sidebar component with support for collapsible states, keyboard shortcuts (Cmd/Ctrl+B), tooltips, submenus, and more.",
      },
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Sidebar>

export default meta
type Story = StoryObj<typeof meta>

// Enhanced navigation data
interface NavigationItem {
  title: string
  url: string
  icon: string
  isActive?: boolean
  badge?: string
  subItems?: { title: string; url: string }[]
}

interface NavigationSection {
  title: string
  items: NavigationItem[]
}

const navigation: NavigationSection[] = [
  {
    title: "Getting Started",
    items: [
      { title: "Overview", url: "#", icon: "📊" },
      { title: "Installation", url: "#", icon: "⚙️" },
      { title: "Quick Start", url: "#", icon: "🚀" },
    ],
  },
  {
    title: "Components",
    items: [
      { title: "Button", url: "#", icon: "🔘" },
      { title: "Input", url: "#", icon: "📝" },
      { title: "Dialog", url: "#", icon: "💬" },
      { 
        title: "Sidebar", 
        url: "#", 
        icon: "📋", 
        isActive: true,
        badge: "New",
        subItems: [
          { title: "Basic Usage", url: "#" },
          { title: "Advanced Config", url: "#" },
          { title: "Theming", url: "#" },
        ]
      },
    ],
  },
  {
    title: "Advanced",
    items: [
      { title: "Theming", url: "#", icon: "🎨", badge: "12" },
      { title: "Configuration", url: "#", icon: "⚙️" },
      { title: "API Reference", url: "#", icon: "📚" },
    ],
  },
]

// Enhanced sidebar demo component
const SidebarDemo = () => (
  <>
    <SidebarHeader>
      <div className="flex items-center gap-2 px-2 py-1">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <span className="text-sm font-bold">F</span>
        </div>
        <span className="font-semibold">FinPro UI</span>
      </div>
      <SidebarInput placeholder="Search components..." />
    </SidebarHeader>
    <SidebarContent>
      {navigation.map((section) => (
        <SidebarGroup key={section.title}>
          <SidebarGroupLabel>{section.title}</SidebarGroupLabel>
          <SidebarGroupAction>
            <span>+</span>
          </SidebarGroupAction>
          <SidebarGroupContent>
            <SidebarMenu>
              {section.items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={item.isActive}
                    tooltip={item.title}
                  >
                    <a href={item.url}>
                      <span>{item.icon}</span>
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                  {item.badge && (
                    <SidebarMenuBadge>
                      {item.badge}
                    </SidebarMenuBadge>
                  )}
                  <SidebarMenuAction showOnHover>
                    <span>⋯</span>
                  </SidebarMenuAction>
                  {item.subItems && (
                    <SidebarMenuSub>
                      {item.subItems.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem.url}>
                              {subItem.title}
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      ))}
      <SidebarSeparator />
      <SidebarGroup>
        <SidebarGroupLabel>Loading Example</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuSkeleton showIcon />
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuSkeleton />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton tooltip="User Profile">
            <span>👤</span>
            <span>John Doe</span>
          </SidebarMenuButton>
          <SidebarMenuAction>
            <span>⚙️</span>
          </SidebarMenuAction>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
    <SidebarRail />
  </>
)

export const Default: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar>
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Dashboard</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                Enhanced sidebar with search, badges, tooltips, submenus, and keyboard shortcuts (Cmd/Ctrl+B to toggle).
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const Floating: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar variant="floating">
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Floating Sidebar</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                The floating variant adds margin and shadow to the sidebar for a card-like appearance.
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const RightSide: Story = {
  render: () => (
    <SidebarProvider>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <h1 className="text-lg font-semibold">Right Sidebar</h1>
          <div className="ml-auto">
            <SidebarTrigger />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                The sidebar can be positioned on the right side of the screen.
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
      <Sidebar side="right">
        <SidebarDemo />
      </Sidebar>
    </SidebarProvider>
  ),
}

export const CollapsibleIcon: Story = {
  render: () => (
    <SidebarProvider defaultOpen={false}>
      <Sidebar collapsible="icon">
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Icon Collapsible</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                When collapsed, the sidebar shows only icons with tooltips on hover. Submenus and badges are hidden.
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const Inset: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar variant="inset">
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Inset Variant</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                The inset variant provides a contained layout with rounded corners and shadow.
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const WithSubmenus: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <div className="flex items-center gap-2 px-2 py-1">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-sm font-bold">F</span>
            </div>
            <span className="font-semibold">FinPro UI</span>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Navigation</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton tooltip="Dashboard">
                    <span>🏠</span>
                    <span>Dashboard</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton tooltip="Components">
                    <span>🧩</span>
                    <span>Components</span>
                  </SidebarMenuButton>
                  <SidebarMenuBadge>24</SidebarMenuBadge>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton>
                        Button
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton isActive>
                        Sidebar
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton>
                        Dialog
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton>
                        Form
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton tooltip="Settings">
                    <span>⚙️</span>
                    <span>Settings</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarRail />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Submenu Example</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="rounded-xl bg-muted/50 p-4">
            <p className="text-muted-foreground">
              This example shows nested submenus that are hidden when the sidebar is collapsed to icon mode.
            </p>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const LoadingStates: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <div className="flex items-center gap-2 px-2 py-1">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-sm font-bold">F</span>
            </div>
            <span className="font-semibold">FinPro UI</span>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Loading Menu Items</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuSkeleton showIcon />
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuSkeleton showIcon />
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuSkeleton />
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuSkeleton showIcon />
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
          <SidebarSeparator />
          <SidebarGroup>
            <SidebarGroupLabel>Loaded Items</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton tooltip="Dashboard">
                    <span>🏠</span>
                    <span>Dashboard</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton tooltip="Analytics">
                    <span>📊</span>
                    <span>Analytics</span>
                  </SidebarMenuButton>
                  <SidebarMenuBadge>3</SidebarMenuBadge>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarRail />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Loading States</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="rounded-xl bg-muted/50 p-4">
            <p className="text-muted-foreground">
              Skeleton components provide loading states for menu items with random widths for natural appearance.
            </p>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const KeyboardShortcuts: Story = {
  parameters: {
    docs: {
      description: {
        story: "Use Cmd/Ctrl+B to toggle the sidebar. This works from anywhere in the application.",
      },
    },
  },
  render: () => (
    <SidebarProvider>
      <Sidebar>
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Keyboard Shortcuts</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="rounded-xl bg-muted/50 p-4">
            <h3 className="font-semibold mb-2">Available Shortcuts:</h3>
            <ul className="text-muted-foreground space-y-1">
              <li><kbd className="px-2 py-1 bg-muted rounded text-xs">Cmd</kbd> + <kbd className="px-2 py-1 bg-muted rounded text-xs">B</kbd> - Toggle sidebar</li>
              <li><kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl</kbd> + <kbd className="px-2 py-1 bg-muted rounded text-xs">B</kbd> - Toggle sidebar (Windows/Linux)</li>
            </ul>
            <p className="text-muted-foreground mt-4">
              Try pressing the keyboard shortcut to toggle the sidebar from anywhere in the application.
            </p>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const InteractiveRail: Story = {
  parameters: {
    docs: {
      description: {
        story: "Click the rail on the edge of the sidebar to toggle it. The rail provides a larger interaction area.",
      },
    },
  },
  render: () => (
    <SidebarProvider>
      <Sidebar>
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger />
          <h1 className="text-lg font-semibold">Interactive Rail</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="rounded-xl bg-muted/50 p-4">
            <p className="text-muted-foreground">
              Look for the interactive rail on the right edge of the sidebar. Click it to toggle the sidebar state.
              The rail becomes more visible on hover and provides cursor feedback.
            </p>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}

export const NonCollapsible: Story = {
  render: () => (
    <SidebarProvider>
      <Sidebar collapsible="none">
        <SidebarDemo />
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <h1 className="text-lg font-semibold">Non-Collapsible Sidebar</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <div className="p-4">
              <p className="text-muted-foreground">
                This sidebar cannot be collapsed and remains visible at all times on desktop.
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
}