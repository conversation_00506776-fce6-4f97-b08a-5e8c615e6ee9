import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { Badge } from './Badge'

const meta: Meta<typeof Badge> = {
  title: 'Components/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'secondary', 'destructive', 'outline'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
    asChild: {
      control: { type: 'boolean' },
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes to apply to the badge',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Badge',
    variant: 'default',
  },
}

export const Secondary: Story = {
  args: {
    children: 'Secondary',
    variant: 'secondary',
  },
}

export const Destructive: Story = {
  args: {
    children: 'Destructive',
    variant: 'destructive',
  },
}

export const Outline: Story = {
  args: {
    children: 'Outline',
    variant: 'outline',
  },
}

export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap">
      <Badge variant="default">Default</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="destructive">Destructive</Badge>
      <Badge variant="outline">Outline</Badge>
    </div>
  ),
}

export const WithIcon: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge variant="default">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
        Success
      </Badge>
      <Badge variant="destructive">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
        Error
      </Badge>
      <Badge variant="secondary">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
        Info
      </Badge>
    </div>
  ),
}

export const Numbers: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge variant="default">1</Badge>
      <Badge variant="secondary">23</Badge>
      <Badge variant="outline">456</Badge>
      <Badge variant="destructive">1,234</Badge>
    </div>
  ),
}

export const StatusBadges: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge variant="default">Active</Badge>
      <Badge variant="secondary">Pending</Badge>
      <Badge variant="destructive">Failed</Badge>
      <Badge variant="outline">Draft</Badge>
    </div>
  ),
}

export const AsLink: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge asChild variant="default">
        <a href="#" className="cursor-pointer">
          Clickable Badge
        </a>
      </Badge>
      <Badge asChild variant="outline">
        <button type="button" className="cursor-pointer">
          Button Badge
        </button>
      </Badge>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge size="sm">Small</Badge>
      <Badge size="md">Medium</Badge>
      <Badge size="lg">Large</Badge>
    </div>
  ),
}

export const SizeVariants: Story = {
  render: () => (
    <div className="flex gap-4 flex-wrap items-center">
      <div className="flex flex-col gap-2 items-center">
        <Badge variant="default" size="sm">Default Small</Badge>
        <Badge variant="secondary" size="sm">Secondary Small</Badge>
        <Badge variant="destructive" size="sm">Destructive Small</Badge>
        <Badge variant="outline" size="sm">Outline Small</Badge>
      </div>
      <div className="flex flex-col gap-2 items-center">
        <Badge variant="default" size="md">Default Medium</Badge>
        <Badge variant="secondary" size="md">Secondary Medium</Badge>
        <Badge variant="destructive" size="md">Destructive Medium</Badge>
        <Badge variant="outline" size="md">Outline Medium</Badge>
      </div>
      <div className="flex flex-col gap-2 items-center">
        <Badge variant="default" size="lg">Default Large</Badge>
        <Badge variant="secondary" size="lg">Secondary Large</Badge>
        <Badge variant="destructive" size="lg">Destructive Large</Badge>
        <Badge variant="outline" size="lg">Outline Large</Badge>
      </div>
    </div>
  ),
}

export const CustomColors: Story = {
  render: () => (
    <div className="flex gap-2 flex-wrap items-center">
      <Badge className="bg-green-500 text-white border-green-600">
        Custom Green
      </Badge>
      <Badge className="bg-purple-100 text-purple-800 border-purple-300">
        Custom Purple
      </Badge>
      <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-transparent">
        Gradient
      </Badge>
      <Badge variant="outline" className="border-2 border-orange-500 text-orange-600 hover:bg-orange-50">
        Orange Outline
      </Badge>
      <Badge className="bg-red-50 text-red-700 border-red-200">
        Light Red
      </Badge>
    </div>
  ),
}