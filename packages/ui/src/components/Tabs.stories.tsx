import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from './Tabs'

const meta: Meta<typeof Tabs> = {
  title: 'Components/Tabs',
  component: Tabs,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <div className="w-96">
      <Tabs defaultValue="account">
        <TabsList>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        <TabsContent value="account">
          <div className="p-4 border rounded-lg mt-2">
            <h3 className="text-lg font-semibold mb-2">Account</h3>
            <p className="text-sm text-gray-600">
              Make changes to your account here. Click save when you're done.
            </p>
          </div>
        </TabsContent>
        <TabsContent value="password">
          <div className="p-4 border rounded-lg mt-2">
            <h3 className="text-lg font-semibold mb-2">Password</h3>
            <p className="text-sm text-gray-600">
              Change your password here. After saving, you'll be logged out.
            </p>
          </div>
        </TabsContent>
        <TabsContent value="settings">
          <div className="p-4 border rounded-lg mt-2">
            <h3 className="text-lg font-semibold mb-2">Settings</h3>
            <p className="text-sm text-gray-600">
              Update your preferences and settings here.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  ),
}

export const Underline: Story = {
  render: () => (
    <div className="w-96">
      <Tabs defaultValue="overview">
        <TabsList variant="underline">
          <TabsTrigger variant="underline" value="overview">Overview</TabsTrigger>
          <TabsTrigger variant="underline" value="analytics">Analytics</TabsTrigger>
          <TabsTrigger variant="underline" value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <div className="p-4 mt-4">
            <h3 className="text-lg font-semibold mb-2">Overview</h3>
            <p className="text-sm text-gray-600">
              Here's an overview of your dashboard.
            </p>
          </div>
        </TabsContent>
        <TabsContent value="analytics">
          <div className="p-4 mt-4">
            <h3 className="text-lg font-semibold mb-2">Analytics</h3>
            <p className="text-sm text-gray-600">
              View your analytics and metrics here.
            </p>
          </div>
        </TabsContent>
        <TabsContent value="reports">
          <div className="p-4 mt-4">
            <h3 className="text-lg font-semibold mb-2">Reports</h3>
            <p className="text-sm text-gray-600">
              Generate and view reports here.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  ),
}