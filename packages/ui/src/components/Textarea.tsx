import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const textareaVariants = cva(
  'flex min-h-[80px] w-full rounded-md border bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset disabled:cursor-not-allowed disabled:opacity-50 resize-none',
  {
    variants: {
      variant: {
        default: 'border-gray-300 focus-visible:ring-blue-500/30',
        error: 'border-red-500 focus-visible:ring-red-500/30 text-red-900 placeholder:text-red-400',
        success: 'border-green-500 focus-visible:ring-green-500/30 text-green-900 placeholder:text-green-400',
      },
      size: {
        sm: 'min-h-[60px] text-xs px-2 py-1',
        default: 'min-h-[80px] text-sm px-3 py-2',
        lg: 'min-h-[100px] text-base px-4 py-3',
      },
      resize: {
        none: 'resize-none',
        vertical: 'resize-y',
        horizontal: 'resize-x',
        both: 'resize',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      resize: 'vertical',
    },
  }
)

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {
  helperText?: string
  errorText?: string
  successText?: string
  showCharCount?: boolean
  maxLength?: number
  autoResize?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({
    className,
    variant,
    size,
    resize,
    helperText,
    errorText,
    successText,
    showCharCount = false,
    maxLength,
    autoResize = false,
    disabled,
    onChange,
    value,
    ...props
  }, ref) => {
    const [charCount, setCharCount] = React.useState(0)
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    
    // Merge refs
    React.useImperativeHandle(ref, () => textareaRef.current!)

    // Handle auto-resize
    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current
      if (textarea && autoResize) {
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
    }, [autoResize])

    // Handle character count and auto-resize
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value
      setCharCount(newValue.length)
      
      if (onChange) {
        onChange(e)
      }
      
      // Auto-resize after state update
      if (autoResize) {
        setTimeout(adjustHeight, 0)
      }
    }

    // Initialize character count
    React.useEffect(() => {
      const currentValue = value?.toString() || ''
      setCharCount(currentValue.length)
    }, [value])

    // Initial auto-resize setup
    React.useEffect(() => {
      if (autoResize) {
        adjustHeight()
      }
    }, [adjustHeight, autoResize])

    // Determine the actual variant based on error/success states
    const actualVariant = errorText ? 'error' : successText ? 'success' : variant
    
    // Helper text priority: errorText > successText > helperText
    const displayHelperText = errorText || successText || helperText
    const helperTextColor = errorText ? 'text-red-600' : successText ? 'text-green-600' : 'text-gray-600'

    // Character count color
    const charCountColor = maxLength && charCount > maxLength ? 'text-red-600' : 'text-gray-500'

    return (
      <div className="w-full">
        <textarea
          className={clsx(
            textareaVariants({ 
              variant: actualVariant, 
              size, 
              resize: autoResize ? 'none' : resize 
            }),
            autoResize && 'overflow-hidden',
            className
          )}
          ref={textareaRef}
          disabled={disabled}
          maxLength={maxLength}
          value={value}
          onChange={handleChange}
          {...props}
        />
        
        <div className="flex justify-between items-start mt-1">
          {displayHelperText && (
            <p className={clsx('text-xs flex-1', helperTextColor)}>
              {displayHelperText}
            </p>
          )}
          
          {showCharCount && (
            <p className={clsx('text-xs ml-2 flex-shrink-0', charCountColor)}>
              {charCount}
              {maxLength && `/${maxLength}`}
            </p>
          )}
        </div>
      </div>
    )
  }
)
Textarea.displayName = 'Textarea'

export type { TextareaProps }
export { Textarea }

