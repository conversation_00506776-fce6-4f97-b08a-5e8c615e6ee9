import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { Checkbox } from './Checkbox'
import { Label } from './Label'

const meta: Meta<typeof Checkbox> = {
  title: 'Components/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'destructive'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    checked: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    variant: 'default',
    size: 'default',
  },
}

export const WithLabel: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Checkbox id="terms" />
      <Label htmlFor="terms">Accept terms and conditions</Label>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox size="sm" id="small" />
        <Label htmlFor="small">Small checkbox</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox size="default" id="default" />
        <Label htmlFor="default">Default checkbox</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox size="lg" id="large" />
        <Label htmlFor="large">Large checkbox</Label>
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox variant="default" id="default-variant" defaultChecked />
        <Label htmlFor="default-variant">Default variant</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox variant="destructive" id="destructive-variant" defaultChecked />
        <Label htmlFor="destructive-variant">Destructive variant</Label>
      </div>
    </div>
  ),
}

export const States: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox id="unchecked" />
        <Label htmlFor="unchecked">Unchecked</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="checked" defaultChecked />
        <Label htmlFor="checked">Checked</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="disabled" disabled />
        <Label htmlFor="disabled">Disabled</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="disabled-checked" disabled defaultChecked />
        <Label htmlFor="disabled-checked">Disabled & Checked</Label>
      </div>
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="space-y-4 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">Preferences</h3>
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Checkbox id="marketing" />
          <Label htmlFor="marketing">
            Send me marketing emails
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id="newsletter" defaultChecked />
          <Label htmlFor="newsletter">
            Subscribe to newsletter
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id="notifications" />
          <Label htmlFor="notifications">
            Push notifications
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id="required" defaultChecked disabled />
          <Label htmlFor="required">
            Security updates (required)
          </Label>
        </div>
      </div>
    </form>
  ),
}