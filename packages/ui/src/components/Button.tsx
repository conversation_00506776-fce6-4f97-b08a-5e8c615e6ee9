import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const buttonVariants = cva(
  [
    'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium',
    'ring-offset-background transition-all duration-200 ease-in-out cursor-pointer',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50'
  ],
  {
    variants: {
      variant: {
        // Material Design variants
        contained: '',
        outlined: 'border-2',
        text: '',
        // shadcn/ui variants
        default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline'
      },
      size: {
        small: 'h-8 px-3 text-xs',
        medium: 'h-10 px-4 text-sm',
        large: 'h-12 px-6 text-base',
        default: 'h-10 px-4 text-sm',
        sm: 'h-9 px-3 text-sm',
        lg: 'h-11 px-8 text-sm',
        icon: 'h-10 w-10 p-0'
      },
      color: {
        primary: '',
        secondary: '',
        error: '',
        warning: '',
        info: '',
        success: ''
      },
      fullWidth: {
        true: 'w-full',
        false: ''
      },
      elevation: {
        true: 'shadow-md hover:shadow-lg',
        false: ''
      },
      ripple: {
        true: 'active:scale-[0.98]',
        false: ''
      }
    },
    compoundVariants: [
      // Material Design contained variants
      {
        variant: 'contained',
        color: 'primary',
        class: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
      },
      {
        variant: 'contained',
        color: 'secondary',
        class: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500'
      },
      {
        variant: 'contained',
        color: 'error',
        class: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
      },
      {
        variant: 'contained',
        color: 'warning',
        class: 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500'
      },
      {
        variant: 'contained',
        color: 'info',
        class: 'bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500'
      },
      {
        variant: 'contained',
        color: 'success',
        class: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
      },
      // Material Design outlined variants
      {
        variant: 'outlined',
        color: 'primary',
        class: 'border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
      },
      {
        variant: 'outlined',
        color: 'secondary',
        class: 'border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
      },
      {
        variant: 'outlined',
        color: 'error',
        class: 'border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500'
      },
      {
        variant: 'outlined',
        color: 'warning',
        class: 'border-orange-600 text-orange-600 hover:bg-orange-50 focus:ring-orange-500'
      },
      {
        variant: 'outlined',
        color: 'info',
        class: 'border-cyan-600 text-cyan-600 hover:bg-cyan-50 focus:ring-cyan-500'
      },
      {
        variant: 'outlined',
        color: 'success',
        class: 'border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500'
      },
      // Material Design text variants
      {
        variant: 'text',
        color: 'primary',
        class: 'text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
      },
      {
        variant: 'text',
        color: 'secondary',
        class: 'text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
      },
      {
        variant: 'text',
        color: 'error',
        class: 'text-red-600 hover:bg-red-50 focus:ring-red-500'
      },
      {
        variant: 'text',
        color: 'warning',
        class: 'text-orange-600 hover:bg-orange-50 focus:ring-orange-500'
      },
      {
        variant: 'text',
        color: 'info',
        class: 'text-cyan-600 hover:bg-cyan-50 focus:ring-cyan-500'
      },
      {
        variant: 'text',
        color: 'success',
        class: 'text-green-600 hover:bg-green-50 focus:ring-green-500'
      },
      // Add elevation to contained variants by default
      {
        variant: 'contained',
        elevation: true,
        class: 'shadow-md hover:shadow-lg'
      }
    ],
    defaultVariants: {
      variant: 'default',
      size: 'default',
      color: 'primary',
      fullWidth: false,
      elevation: true,
      ripple: true
    }
  }
)

interface ButtonProps 
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingPosition?: 'start' | 'end' | 'center'
  startIcon?: React.ReactNode
  endIcon?: React.ReactNode
  disableElevation?: boolean
  disableRipple?: boolean
}

const LoadingSpinner = ({ size = 16 }: { size?: number }) => (
  <svg 
    className="animate-spin" 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle 
      className="opacity-25" 
      cx="12" 
      cy="12" 
      r="10" 
      stroke="currentColor" 
      strokeWidth="4"
    />
    <path 
      className="opacity-75" 
      fill="currentColor" 
      d="m12 2a10 10 0 0 1 10 10h-4a6 6 0 0 0-6-6v-4z"
    />
  </svg>
)

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant,
    size,
    color,
    fullWidth,
    elevation,
    ripple,
    loading = false,
    loadingPosition = 'start',
    startIcon,
    endIcon,
    disableElevation = false,
    disableRipple = false,
    asChild = false, 
    children,
    disabled,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : 'button'
    const isDisabled = disabled || loading
    
    const classes = clsx(
      buttonVariants({
        variant,
        size,
        color,
        fullWidth,
        elevation: disableElevation ? false : elevation,
        ripple: disableRipple ? false : ripple
      }),
      className
    )
    
    const renderIcon = (icon: React.ReactNode, position: 'start' | 'end') => {
      if (loading && loadingPosition === position) {
        const spinnerSize = size === 'small' || size === 'sm' ? 14 : size === 'large' || size === 'lg' ? 18 : 16
        return <LoadingSpinner size={spinnerSize} />
      }
      return icon
    }
    
    const shouldShowStartIcon = startIcon || (loading && loadingPosition === 'start')
    const shouldShowEndIcon = endIcon || (loading && loadingPosition === 'end')
    const shouldShowCenterLoader = loading && loadingPosition === 'center'
    
    return (
      <Comp
        className={classes}
        ref={ref}
        disabled={isDisabled}
        aria-busy={loading}
        {...props}
      >
        {shouldShowStartIcon && renderIcon(startIcon, 'start')}
        {shouldShowCenterLoader ? (
          <LoadingSpinner size={size === 'small' || size === 'sm' ? 14 : size === 'large' || size === 'lg' ? 18 : 16} />
        ) : (
          children
        )}
        {shouldShowEndIcon && renderIcon(endIcon, 'end')}
      </Comp>
    )
  }
)

Button.displayName = 'Button'

// Additional export for Button
export { Button }

export type { ButtonProps }