import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { Toggle } from './Toggle'

const meta: Meta<typeof Toggle> = {
  title: 'Components/Toggle',
  component: Toggle,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'outline', 'ghost'],
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'sm', 'lg', 'icon'],
    },
    pressed: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Toggle',
  },
}

export const Pressed: Story = {
  args: {
    children: 'Pressed',
    pressed: true,
  },
}

export const WithIcon: Story = {
  args: {
    children: (
      <>
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="mr-2 h-4 w-4"
        >
          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z" />
        </svg>
        Like
      </>
    ),
  },
}

export const IconOnly: Story = {
  args: {
    size: 'icon',
    'aria-label': 'Bold',
    children: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        className="h-4 w-4"
      >
        <path d="M14 12a4 4 0 0 0 0-8H6v16h8a4 4 0 0 0 0-8z" />
        <path d="M6 12h8" />
      </svg>
    ),
  },
}

export const Variants: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <Toggle variant="default">Default</Toggle>
      <Toggle variant="outline">Outline</Toggle>
      <Toggle variant="ghost">Ghost</Toggle>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <Toggle size="sm">Small</Toggle>
      <Toggle size="default">Default</Toggle>
      <Toggle size="lg">Large</Toggle>
      <Toggle size="icon" aria-label="Bold">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M14 12a4 4 0 0 0 0-8H6v16h8a4 4 0 0 0 0-8z" />
          <path d="M6 12h8" />
        </svg>
      </Toggle>
    </div>
  ),
}

export const States: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <Toggle>Default</Toggle>
      <Toggle pressed>Pressed</Toggle>
      <Toggle disabled>Disabled</Toggle>
      <Toggle pressed disabled>
        Pressed & Disabled
      </Toggle>
    </div>
  ),
}

export const TextFormatting: Story = {
  render: () => (
    <div className="flex items-center space-x-1 border rounded-md p-1">
      <Toggle size="sm" variant="ghost" aria-label="Bold">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M14 12a4 4 0 0 0 0-8H6v16h8a4 4 0 0 0 0-8z" />
          <path d="M6 12h8" />
        </svg>
      </Toggle>
      <Toggle size="sm" variant="ghost" aria-label="Italic">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="19" x2="10" y1="4" y2="4" />
          <line x1="14" x2="5" y1="20" y2="20" />
          <line x1="15" x2="9" y1="4" y2="20" />
        </svg>
      </Toggle>
      <Toggle size="sm" variant="ghost" aria-label="Underline">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M6 4v6a6 6 0 0 0 12 0V4" />
          <line x1="4" x2="20" y1="20" y2="20" />
        </svg>
      </Toggle>
      <Toggle size="sm" variant="ghost" aria-label="Strikethrough">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M16 4H9a3 3 0 0 0-2.83 4" />
          <path d="M14 12a4 4 0 0 1 0 8H6" />
          <line x1="4" x2="20" y1="12" y2="12" />
        </svg>
      </Toggle>
    </div>
  ),
}

export const MediaControls: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Toggle variant="outline" size="icon" aria-label="Previous">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <polygon points="19,20 9,12 19,4" />
          <line x1="5" x2="5" y1="19" y2="5" />
        </svg>
      </Toggle>
      <Toggle variant="outline" size="icon" aria-label="Play" pressed>
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <polygon points="5,3 19,12 5,21" />
        </svg>
      </Toggle>
      <Toggle variant="outline" size="icon" aria-label="Next">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <polygon points="5,4 15,12 5,20" />
          <line x1="19" x2="19" y1="5" y2="19" />
        </svg>
      </Toggle>
      <Toggle variant="outline" size="icon" aria-label="Shuffle">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <polyline points="16,3 21,3 21,8" />
          <line x1="4" x2="21" y1="20" y2="3" />
          <polyline points="21,16 21,21 16,21" />
          <line x1="15" x2="21" y1="15" y2="21" />
          <line x1="4" x2="9" y1="4" y2="9" />
        </svg>
      </Toggle>
      <Toggle variant="outline" size="icon" aria-label="Repeat">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="m17 2 4 4-4 4" />
          <path d="M3 11v-1a4 4 0 0 1 4-4h14" />
          <path d="m7 22-4-4 4-4" />
          <path d="M21 13v1a4 4 0 0 1-4 4H3" />
        </svg>
      </Toggle>
    </div>
  ),
}

export const SocialActions: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Toggle aria-label="Like">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="mr-2 h-4 w-4"
        >
          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z" />
        </svg>
        Like
      </Toggle>
      <Toggle aria-label="Bookmark">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="mr-2 h-4 w-4"
        >
          <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z" />
        </svg>
        Save
      </Toggle>
      <Toggle aria-label="Share">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="mr-2 h-4 w-4"
        >
          <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
          <polyline points="16,6 12,2 8,6" />
          <line x1="12" x2="12" y1="2" y2="15" />
        </svg>
        Share
      </Toggle>
    </div>
  ),
}