import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const skeletonVariants = cva(
  [
    'animate-pulse bg-gray-200 dark:bg-gray-800'
  ],
  {
    variants: {
      variant: {
        default: 'rounded-md',
        text: 'rounded',
        circular: 'rounded-full',
        rectangular: 'rounded-none'
      },
      size: {
        sm: 'h-4',
        md: 'h-6',
        lg: 'h-8',
        xl: 'h-12'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'md'
    }
  }
)

interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {
  asChild?: boolean
  width?: string | number
  height?: string | number
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant, size, asChild = false, width, height, style, ...props }, ref) => {
    const Comp = asChild ? Slot : 'div'
    
    const inlineStyles = {
      ...style,
      ...(width && { width: typeof width === 'number' ? `${width}px` : width }),
      ...(height && { height: typeof height === 'number' ? `${height}px` : height })
    }
    
    return (
      <Comp
        className={clsx(skeletonVariants({ variant, size }), className)}
        style={inlineStyles}
        ref={ref}
        {...props}
      />
    )
  }
)
Skeleton.displayName = 'Skeleton'

export type { SkeletonProps }
export { Skeleton }