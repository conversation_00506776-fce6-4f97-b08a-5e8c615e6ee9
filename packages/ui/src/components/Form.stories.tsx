import type { Meta, StoryObj } from '@storybook/react-vite'
import { useState } from 'react'
import { 
  Form, 
  FormField, 
  FormLabel, 
  FormControl, 
  FormMessage, 
  FormSubmit, 
  FormDescription,
  FormValidityState 
} from './Form'
import { Button } from './Button'

const meta: Meta<typeof Form> = {
  title: 'Components/Form',
  component: Form,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <Form className="w-96 space-y-4">
      <FormField name="email">
        <FormLabel required>Email</FormLabel>
        <FormControl type="email" placeholder="Enter your email" required />
        <FormMessage match="valueMissing">Please enter your email</FormMessage>
        <FormMessage match="typeMismatch">Please enter a valid email</FormMessage>
      </FormField>
      
      <FormField name="password">
        <FormLabel required>Password</FormLabel>
        <FormControl type="password" placeholder="Enter your password" required minLength={8} />
        <FormMessage match="valueMissing">Please enter your password</FormMessage>
        <FormMessage match="tooShort">Password must be at least 8 characters</FormMessage>
      </FormField>
      
      <FormSubmit>Sign In</FormSubmit>
    </Form>
  ),
}

export const WithDescriptions: Story = {
  render: () => (
    <Form className="w-96 space-y-4">
      <FormField name="username">
        <FormLabel required>Username</FormLabel>
        <FormControl 
          type="text" 
          placeholder="Enter username" 
          required 
          minLength={3}
          pattern="[a-zA-Z0-9_]+"
        />
        <FormDescription>
          Username must be at least 3 characters and contain only letters, numbers, and underscores.
        </FormDescription>
        <FormMessage match="valueMissing">Please enter a username</FormMessage>
        <FormMessage match="tooShort">Username must be at least 3 characters</FormMessage>
        <FormMessage match="patternMismatch">Username can only contain letters, numbers, and underscores</FormMessage>
      </FormField>
      
      <FormField name="email">
        <FormLabel required>Email Address</FormLabel>
        <FormControl type="email" placeholder="<EMAIL>" required />
        <FormDescription variant="muted">
          We'll use this email to send you important updates.
        </FormDescription>
        <FormMessage match="valueMissing">Please enter your email address</FormMessage>
        <FormMessage match="typeMismatch">Please enter a valid email address</FormMessage>
      </FormField>
      
      <FormSubmit>Create Account</FormSubmit>
    </Form>
  ),
}

export const CustomValidation: Story = {
  render: () => {
    const [serverError, setServerError] = useState<string>('')
    
    const handleSubmit = async (event: React.FormEvent) => {
      event.preventDefault()
      setServerError('')
      
      // Simulate server validation
      const formData = new FormData(event.target as HTMLFormElement)
      const email = formData.get('email') as string
      
      if (email === '<EMAIL>') {
        setServerError('This email address is already taken')
        return
      }
      
      // Simulate successful submission
      alert('Form submitted successfully!')
      setServerError('')
    }
    
    return (
      <Form className="w-96 space-y-4" onSubmit={handleSubmit}>
        <FormField name="email" serverInvalid={!!serverError}>
          <FormLabel required>Email</FormLabel>
          <FormControl 
            type="email" 
            placeholder="Enter your email" 
            required 
          />
          <FormMessage match="valueMissing">Please enter your email</FormMessage>
          <FormMessage match="typeMismatch">Please enter a valid email</FormMessage>
          {serverError && (
            <FormMessage variant="error" forceMatch>
              {serverError}
            </FormMessage>
          )}
        </FormField>
        
        <FormDescription>
          Try entering "<EMAIL>" to see server-side validation error.
        </FormDescription>
        
        <FormSubmit>Register</FormSubmit>
      </Form>
    )
  },
}

export const FormSizes: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="w-96">
        <h4 className="mb-4 font-medium">Small Size</h4>
        <Form className="space-y-3">
          <FormField name="small-email">
            <FormLabel>Email</FormLabel>
            <FormControl type="email" placeholder="Small input" />
          </FormField>
          <Button size="sm" type="submit">Submit</Button>
        </Form>
      </div>
      
      <div className="w-96">
        <h4 className="mb-4 font-medium">Default Size</h4>
        <Form className="space-y-3">
          <FormField name="default-email">
            <FormLabel>Email</FormLabel>
            <FormControl type="email" placeholder="Default input" />
          </FormField>
          <Button type="submit">Submit</Button>
        </Form>
      </div>
      
      <div className="w-96">
        <h4 className="mb-4 font-medium">Large Size</h4>
        <Form className="space-y-3">
          <FormField name="large-email">
            <FormLabel>Email</FormLabel>
            <FormControl type="email" placeholder="Large input" />
          </FormField>
          <Button size="lg" type="submit">Submit</Button>
        </Form>
      </div>
    </div>
  ),
}

export const InlineForm: Story = {
  render: () => (
    <Form className="w-96">
      <FormField variant="inline" name="subscribe">
        <FormLabel>Subscribe to newsletter</FormLabel>
        <div className="flex-1">
          <FormControl type="email" placeholder="Enter email" required />
          <FormMessage match="valueMissing">Email required</FormMessage>
          <FormMessage match="typeMismatch">Invalid email</FormMessage>
        </div>
        <FormSubmit>Subscribe</FormSubmit>
      </FormField>
    </Form>
  ),
}

export const ComplexForm: Story = {
  render: () => (
    <Form className="w-96 space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Personal Information</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <FormField name="firstName">
            <FormLabel required>First Name</FormLabel>
            <FormControl type="text" placeholder="John" required />
            <FormMessage match="valueMissing">First name is required</FormMessage>
          </FormField>
          
          <FormField name="lastName">
            <FormLabel required>Last Name</FormLabel>
            <FormControl type="text" placeholder="Doe" required />
            <FormMessage match="valueMissing">Last name is required</FormMessage>
          </FormField>
        </div>
        
        <FormField name="email">
          <FormLabel required>Email Address</FormLabel>
          <FormControl type="email" placeholder="<EMAIL>" required />
          <FormMessage match="valueMissing">Email is required</FormMessage>
          <FormMessage match="typeMismatch">Please enter a valid email</FormMessage>
        </FormField>
        
        <FormField name="phone">
          <FormLabel>Phone Number</FormLabel>
          <FormControl 
            type="tel" 
            placeholder="+****************"
            pattern="[\+]?[\d\s\(\)\-]+"
          />
          <FormMessage match="patternMismatch">Please enter a valid phone number</FormMessage>
        </FormField>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Account Settings</h3>
        
        <FormField name="password">
          <FormLabel required>Password</FormLabel>
          <FormControl 
            type="password" 
            placeholder="Enter password" 
            required 
            minLength={8}
          />
          <FormDescription>
            Password must be at least 8 characters long.
          </FormDescription>
          <FormMessage match="valueMissing">Password is required</FormMessage>
          <FormMessage match="tooShort">Password must be at least 8 characters</FormMessage>
        </FormField>
        
        <FormField name="confirmPassword">
          <FormLabel required>Confirm Password</FormLabel>
          <FormControl 
            type="password" 
            placeholder="Confirm password" 
            required 
          />
          <FormMessage match="valueMissing">Please confirm your password</FormMessage>
        </FormField>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Preferences</h3>
        
        <FormField name="notifications">
          <div className="flex items-center space-x-2">
            <FormControl type="checkbox" className="h-4 w-4" />
            <FormLabel>Send me email notifications</FormLabel>
          </div>
        </FormField>
        
        <FormField name="terms">
          <div className="flex items-center space-x-2">
            <FormControl type="checkbox" className="h-4 w-4" required />
            <FormLabel required>I agree to the terms and conditions</FormLabel>
          </div>
          <FormMessage match="valueMissing">You must agree to the terms</FormMessage>
        </FormField>
      </div>
      
      <div className="flex space-x-3">
        <FormSubmit>Create Account</FormSubmit>
        <Button type="button" variant="outlined">
          Cancel
        </Button>
      </div>
    </Form>
  ),
}

export const ValidationStates: Story = {
  render: () => (
    <Form className="w-96 space-y-4">
      <FormField name="valid-field">
        <FormLabel>Valid Field</FormLabel>
        <FormControl type="text" defaultValue="Valid input" />
        <FormMessage variant="success" forceMatch>
          ✓ This field is valid
        </FormMessage>
      </FormField>
      
      <FormField name="error-field" serverInvalid>
        <FormLabel variant="error">Field with Error</FormLabel>
        <FormControl variant="error" type="text" defaultValue="Invalid input" />
        <FormMessage variant="error" forceMatch>
          ✗ This field has an error
        </FormMessage>
      </FormField>
      
      <FormField name="warning-field">
        <FormLabel>Field with Warning</FormLabel>
        <FormControl type="text" defaultValue="Potentially problematic input" />
        <FormDescription>
          ⚠️ This input might cause issues
        </FormDescription>
      </FormField>
      
      <FormSubmit>Submit</FormSubmit>
    </Form>
  ),
}

export const RealTimeValidation: Story = {
  render: () => (
    <Form className="w-96 space-y-4">
      <FormField name="username">
        <FormLabel required>Username</FormLabel>
        <FormControl 
          type="text" 
          placeholder="Enter username" 
          required 
          minLength={3}
          pattern="^[a-zA-Z0-9_]+$"
        />
        <FormValidityState>
          {(validity) => (
            <div className="space-y-1">
              {validity?.valueMissing && (
                <FormMessage variant="error" forceMatch>
                  Username is required
                </FormMessage>
              )}
              {validity?.tooShort && (
                <FormMessage variant="error" forceMatch>
                  Username must be at least 3 characters
                </FormMessage>
              )}
              {validity?.patternMismatch && (
                <FormMessage variant="error" forceMatch>
                  Username can only contain letters, numbers, and underscores
                </FormMessage>
              )}
              {validity?.valid && (
                <FormMessage variant="success" forceMatch>
                  ✓ Username is available
                </FormMessage>
              )}
            </div>
          )}
        </FormValidityState>
      </FormField>
      
      <FormField name="email">
        <FormLabel required>Email</FormLabel>
        <FormControl type="email" placeholder="Enter email" required />
        <FormValidityState>
          {(validity) => (
            <>
              {validity?.valueMissing && (
                <FormMessage variant="error" forceMatch>Email is required</FormMessage>
              )}
              {validity?.typeMismatch && (
                <FormMessage variant="error" forceMatch>Please enter a valid email</FormMessage>
              )}
              {validity?.valid && (
                <FormMessage variant="success" forceMatch>✓ Email looks good</FormMessage>
              )}
            </>
          )}
        </FormValidityState>
      </FormField>
      
      <FormSubmit>Register</FormSubmit>
    </Form>
  ),
}

export const FileUpload: Story = {
  render: () => (
    <Form className="w-96 space-y-4">
      <FormField name="avatar">
        <FormLabel>Profile Picture</FormLabel>
        <FormControl 
          type="file" 
          accept="image/*"
          className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        <FormDescription>
          Choose an image file (JPG, PNG, GIF). Max size: 5MB.
        </FormDescription>
      </FormField>
      
      <FormField name="resume">
        <FormLabel>Resume</FormLabel>
        <FormControl 
          type="file" 
          accept=".pdf,.doc,.docx"
          required
          className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100"
        />
        <FormMessage match="valueMissing">Please upload your resume</FormMessage>
        <FormDescription>
          Accepted formats: PDF, DOC, DOCX
        </FormDescription>
      </FormField>
      
      <FormSubmit>Upload Files</FormSubmit>
    </Form>
  ),
}

export const WithAsChild: Story = {
  render: () => (
    <div className="w-96 space-y-6">
      <div>
        <h4 className="mb-4 font-medium">Using asChild with Custom Components</h4>
        
        <Form className="space-y-4">
          <FormField name="email">
            <FormLabel asChild>
              <label className="flex items-center space-x-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="h-4 w-4">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22,6 12,13 2,6"/>
                </svg>
                <span>Email Address</span>
              </label>
            </FormLabel>
            <FormControl type="email" placeholder="Enter your email" required />
            <FormMessage match="valueMissing">Email is required</FormMessage>
            <FormMessage match="typeMismatch">Please enter a valid email</FormMessage>
          </FormField>
          
          <FormField name="password">
            <FormLabel asChild>
              <label className="flex items-center space-x-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="h-4 w-4">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                  <circle cx="12" cy="16" r="1"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
                <span>Password</span>
              </label>
            </FormLabel>
            <FormControl asChild>
              <input 
                type="password" 
                placeholder="Enter password"
                required
                minLength={8}
                className="flex w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors"
              />
            </FormControl>
            <FormMessage match="valueMissing">Password is required</FormMessage>
            <FormMessage match="tooShort">Password must be at least 8 characters</FormMessage>
          </FormField>
          
          <FormSubmit asChild>
            <Button variant="contained" color="primary" className="w-full">
              Sign In with Custom Button
            </Button>
          </FormSubmit>
        </Form>
      </div>
      
      <div>
        <h4 className="mb-4 font-medium">Form as Child Element</h4>
        
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
          <Form asChild>
            <div className="space-y-4 bg-white rounded-md border p-4 shadow-sm">
              <FormField name="newsletter">
                <FormLabel>Subscribe to Newsletter</FormLabel>
                <div className="flex space-x-2">
                  <FormControl 
                    type="email" 
                    placeholder="Enter email" 
                    required 
                    className="flex-1"
                  />
                  <FormSubmit asChild>
                    <Button variant="contained" size="sm">
                      Subscribe
                    </Button>
                  </FormSubmit>
                </div>
                <FormMessage match="valueMissing">Email is required</FormMessage>
                <FormMessage match="typeMismatch">Please enter a valid email</FormMessage>
              </FormField>
            </div>
          </Form>
        </div>
      </div>
    </div>
  ),
}