import type { Meta, StoryObj } from '@storybook/react-vite'
import { VisuallyHidden } from './VisuallyHidden'
import { <PERSON><PERSON> } from './Button'

const meta: Meta<typeof VisuallyHidden> = {
  title: 'Utilities/VisuallyHidden',
  component: VisuallyHidden,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
}

export default meta
type Story = StoryObj<typeof meta>

// Icons for demonstration
const SettingsIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    className="h-4 w-4"
  >
    <circle cx="12" cy="12" r="3" />
    <path d="M12 1v6m0 6v6" />
    <path d="m4.2 4.2 4.2 4.2m5.6 0 4.2-4.2" />
    <path d="M1 12h6m6 0h6" />
    <path d="m4.2 19.8 4.2-4.2m5.6 0 4.2 4.2" />
  </svg>
)

const HeartIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="h-4 w-4"
  >
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
  </svg>
)

const EditIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    className="h-4 w-4"
  >
    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
  </svg>
)

const DeleteIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    className="h-4 w-4"
  >
    <polyline points="3,6 5,6 21,6" />
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
    <line x1="10" y1="11" x2="10" y2="17" />
    <line x1="14" y1="11" x2="14" y2="17" />
  </svg>
)

export const Default: Story = {
  render: () => (
    <div className="text-center">
      <Button variant="contained" size="icon">
        <SettingsIcon />
        <VisuallyHidden>Settings</VisuallyHidden>
      </Button>
      <p className="mt-4 text-sm text-gray-600">
        This button has an icon with visually hidden text "Settings" for screen readers.
        <br />
        Try using a screen reader to hear the accessible text.
      </p>
    </div>
  ),
}

export const IconButtons: Story = {
  render: () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Icon Buttons with Screen Reader Labels</h3>
      <p className="text-sm text-gray-600 mb-4">
        These buttons appear as icon-only to sighted users, but provide descriptive text to screen readers.
      </p>
      
      <div className="flex space-x-2">
        <Button variant="contained" size="icon">
          <HeartIcon />
          <VisuallyHidden>Add to favorites</VisuallyHidden>
        </Button>
        
        <Button variant="contained" size="icon">
          <EditIcon />
          <VisuallyHidden>Edit item</VisuallyHidden>
        </Button>
        
        <Button variant="contained" size="icon" color="error">
          <DeleteIcon />
          <VisuallyHidden>Delete item</VisuallyHidden>
        </Button>
        
        <Button variant="contained" size="icon">
          <SettingsIcon />
          <VisuallyHidden>Open settings</VisuallyHidden>
        </Button>
      </div>
    </div>
  ),
}

export const FormLabels: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <h3 className="text-lg font-semibold">Form with Visually Hidden Labels</h3>
      <p className="text-sm text-gray-600 mb-4">
        These inputs use placeholder text visually, but have proper labels for screen readers.
      </p>
      
      <div className="space-y-3">
        <div>
          <VisuallyHidden>
            <label htmlFor="email">Email Address</label>
          </VisuallyHidden>
          <input
            id="email"
            type="email"
            placeholder="Enter your email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div>
          <VisuallyHidden>
            <label htmlFor="password">Password</label>
          </VisuallyHidden>
          <input
            id="password"
            type="password"
            placeholder="Enter your password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <Button variant="contained" className="w-full">
          Sign In
        </Button>
      </div>
    </div>
  ),
}

export const NavigationSkipLink: Story = {
  render: () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Skip Navigation Link</h3>
      <p className="text-sm text-gray-600">
        Screen reader users can skip directly to main content. Try tabbing to focus the skip link.
      </p>
      
      <div className="border border-gray-200 rounded-lg p-4">
        {/* Skip link - only visible when focused */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
        >
          Skip to main content
        </a>
        
        {/* Simulated navigation */}
        <nav className="mb-4">
          <ul className="flex space-x-4 text-sm">
            <li><a href="#" className="text-blue-600 hover:underline">Home</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">About</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">Services</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">Contact</a></li>
          </ul>
        </nav>
        
        {/* Main content */}
        <main id="main-content">
          <h4 className="font-semibold mb-2">Main Content Area</h4>
          <p className="text-sm text-gray-600">
            This is where the main content would be located. Screen reader users 
            can skip directly here using the skip link above.
          </p>
        </main>
      </div>
    </div>
  ),
}

export const DataVisualization: Story = {
  render: () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Accessible Data Visualization</h3>
      <p className="text-sm text-gray-600 mb-4">
        Visual charts should include alternative text descriptions for screen readers.
      </p>
      
      <div className="border border-gray-200 rounded-lg p-4">
        {/* Simulated chart */}
        <div className="flex items-end space-x-2 h-32 mb-4">
          <div className="w-8 bg-blue-500 h-16" title="January: 25%"></div>
          <div className="w-8 bg-blue-500 h-24" title="February: 35%"></div>
          <div className="w-8 bg-blue-500 h-32" title="March: 45%"></div>
          <div className="w-8 bg-blue-500 h-20" title="April: 30%"></div>
        </div>
        
        <VisuallyHidden>
          Chart data: Sales performance for first quarter. January: 25%, February: 35%, March: 45%, April: 30%. 
          March shows the highest performance at 45%, followed by February at 35%.
        </VisuallyHidden>
        
        <p className="text-sm text-gray-600">
          Chart: Q1 Sales Performance (visually hidden description available for screen readers)
        </p>
      </div>
    </div>
  ),
}

export const StatusIndicators: Story = {
  render: () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Status Indicators</h3>
      <p className="text-sm text-gray-600 mb-4">
        Visual status indicators should include text descriptions for screen readers.
      </p>
      
      <div className="space-y-3">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span>System Status</span>
          <VisuallyHidden>: Online</VisuallyHidden>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <span>Database</span>
          <VisuallyHidden>: Warning - High load</VisuallyHidden>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span>API Server</span>
          <VisuallyHidden>: Offline - Service unavailable</VisuallyHidden>
        </div>
      </div>
    </div>
  ),
}

export const ComplexInteractions: Story = {
  render: () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Complex Interactions</h3>
      <p className="text-sm text-gray-600 mb-4">
        Provide additional context for complex interactions that may not be obvious to screen reader users.
      </p>
      
      <div className="border border-gray-200 rounded-lg p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span>Enable notifications</span>
          <button 
            className="w-12 h-6 bg-blue-600 rounded-full relative focus:outline-none focus:ring-2 focus:ring-blue-500"
            role="switch"
            aria-checked="true"
          >
            <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
            <VisuallyHidden>Toggle notifications on or off. Currently enabled.</VisuallyHidden>
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <span>Dark mode</span>
          <button 
            className="w-12 h-6 bg-gray-300 rounded-full relative focus:outline-none focus:ring-2 focus:ring-blue-500"
            role="switch"
            aria-checked="false"
          >
            <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
            <VisuallyHidden>Toggle dark mode on or off. Currently disabled.</VisuallyHidden>
          </button>
        </div>
      </div>
    </div>
  ),
}