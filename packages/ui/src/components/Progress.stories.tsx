import * as React from 'react'
import type { Meta, StoryObj } from '@storybook/react-vite'
import { Progress, CircularProgress } from './Progress'
import { Label } from './Label'

const meta: Meta<typeof Progress> = {
  title: 'Components/Progress',
  component: Progress,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg', 'xl'],
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'success', 'warning', 'error', 'gradient'],
    },
    value: {
      control: { type: 'range', min: 0, max: 100 },
    },
    showValue: {
      control: { type: 'boolean' },
    },
    showLabel: {
      control: { type: 'boolean' },
    },
    animated: {
      control: { type: 'boolean' },
    },
    indeterminate: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    value: 60,
    size: 'default',
    variant: 'default',
  },
  render: (args) => (
    <div className="w-80">
      <Progress {...args} />
    </div>
  ),
}

export const WithLabel: Story = {
  render: () => (
    <div className="w-80">
      <Progress
        value={75}
        showLabel
        showValue
        label="Upload Progress"
      />
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <Label>Small</Label>
        <Progress size="sm" value={30} showValue />
      </div>
      <div>
        <Label>Default</Label>
        <Progress size="default" value={50} showValue />
      </div>
      <div>
        <Label>Large</Label>
        <Progress size="lg" value={70} showValue />
      </div>
      <div>
        <Label>Extra Large</Label>
        <Progress size="xl" value={90} showValue />
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <Label>Default</Label>
        <Progress variant="default" value={40} showValue />
      </div>
      <div>
        <Label>Success</Label>
        <Progress variant="success" value={100} showValue />
      </div>
      <div>
        <Label>Warning</Label>
        <Progress variant="warning" value={75} showValue />
      </div>
      <div>
        <Label>Error</Label>
        <Progress variant="error" value={25} showValue />
      </div>
      <div>
        <Label>Gradient</Label>
        <Progress variant="gradient" value={85} showValue />
      </div>
    </div>
  ),
}

export const Indeterminate: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <Label>Loading...</Label>
        <Progress indeterminate />
      </div>
      <div>
        <Label>Processing with animation</Label>
        <Progress indeterminate animated variant="success" />
      </div>
    </div>
  ),
}

export const CircularProgressStory: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <CircularProgress value={25} size={40} showValue />
        <CircularProgress value={50} size={60} showValue variant="success" />
        <CircularProgress value={75} size={80} showValue variant="warning" />
        <CircularProgress value={100} size={100} showValue variant="error" />
      </div>
      
      <div className="flex items-center space-x-4">
        <div className="text-center">
          <CircularProgress indeterminate size={40} />
          <p className="text-xs mt-2">Loading</p>
        </div>
        <div className="text-center">
          <CircularProgress indeterminate size={60} variant="success" />
          <p className="text-xs mt-2">Success</p>
        </div>
      </div>
    </div>
  ),
}

export const RealTimeProgress: Story = {
  render: () => {
    const [progress, setProgress] = React.useState(0)
    
    React.useEffect(() => {
      const timer = setInterval(() => {
        setProgress((prevProgress) => {
          if (prevProgress >= 100) {
            return 0
          }
          return prevProgress + 1
        })
      }, 100)
      
      return () => clearInterval(timer)
    }, [])
    
    return (
      <div className="w-80 space-y-4">
        <Progress
          value={progress}
          showLabel
          showValue
          label="Auto Progress"
          variant="gradient"
        />
        
        <div className="flex justify-center">
          <CircularProgress
            value={progress}
            size={80}
            showValue
            variant="default"
          />
        </div>
      </div>
    )
  },
}

export const FormExample: Story = {
  render: () => (
    <div className="w-96 space-y-6 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">File Upload Status</h3>
      
      <div>
        <Progress
          value={100}
          showLabel
          showValue
          label="document.pdf"
          variant="success"
        />
      </div>

      <div>
        <Progress
          value={45}
          showLabel
          showValue
          label="image.jpg"
          variant="default"
        />
      </div>

      <div>
        <Progress
          value={15}
          showLabel
          showValue
          label="video.mp4"
          variant="warning"
        />
      </div>

      <div>
        <Progress
          value={5}
          showLabel
          showValue
          label="archive.zip"
          variant="error"
        />
      </div>

      <div className="flex items-center justify-between pt-4">
        <span className="text-sm text-gray-600">Overall Progress</span>
        <CircularProgress value={65} size={60} showValue />
      </div>
    </div>
  ),
}