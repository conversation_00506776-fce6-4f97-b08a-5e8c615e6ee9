import * as React from 'react'
import * as ProgressPrimitive from '@radix-ui/react-progress'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const progressVariants = cva(
  'relative overflow-hidden rounded-full bg-gray-200',
  {
    variants: {
      size: {
        sm: 'h-1',
        default: 'h-2',
        lg: 'h-3',
        xl: 'h-4',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
)

const progressIndicatorVariants = cva(
  'h-full w-full flex-1 transition-all duration-200 ease-in-out',
  {
    variants: {
      variant: {
        default: 'bg-blue-600',
        success: 'bg-green-600',
        warning: 'bg-orange-600',
        error: 'bg-red-600',
        gradient: 'bg-gradient-to-r from-blue-500 to-purple-600',
      },
      animated: {
        true: 'animate-pulse',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      animated: false,
    },
  }
)

interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'gradient'
  animated?: boolean
  showValue?: boolean
  showLabel?: boolean
  label?: string
  indeterminate?: boolean
}

const Progress = React.forwardRef<
  React.ComponentRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({
  className,
  value = 0,
  size,
  variant = 'default',
  animated = false,
  showValue = false,
  showLabel = false,
  label,
  indeterminate = false,
  ...props
}, ref) => {
  const [displayValue, setDisplayValue] = React.useState(0)
  
  // Animate value changes
  React.useEffect(() => {
    if (!indeterminate && value !== undefined && value !== null) {
      const timer = setTimeout(() => setDisplayValue(value), 100)
      return () => clearTimeout(timer)
    }
  }, [value, indeterminate])

  const progressValue = indeterminate ? undefined : displayValue

  return (
    <div className="w-full">
      {(showLabel || showValue) && (
        <div className="flex justify-between items-center mb-2">
          {showLabel && label && (
            <span className="text-sm font-medium text-gray-700">{label}</span>
          )}
          {showValue && !indeterminate && (
            <span className="text-sm text-gray-600">{Math.round(displayValue)}%</span>
          )}
        </div>
      )}
      
      <ProgressPrimitive.Root
        ref={ref}
        className={clsx(progressVariants({ size, className }))}
        value={progressValue}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={clsx(
            progressIndicatorVariants({ variant, animated }),
            indeterminate && 'animate-pulse origin-left-right'
          )}
          style={
            indeterminate
              ? {
                  transform: 'translateX(-50%)',
                  animation: 'progress-indeterminate 1.5s infinite linear',
                }
              : { transform: `translateX(-${100 - displayValue}%)` }
          }
        />
      </ProgressPrimitive.Root>
      
    </div>
  )
})

Progress.displayName = ProgressPrimitive.Root.displayName

// Circular Progress Component
interface CircularProgressProps {
  value?: number
  size?: number
  strokeWidth?: number
  variant?: 'default' | 'success' | 'warning' | 'error'
  showValue?: boolean
  indeterminate?: boolean
  className?: string
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  value = 0,
  size = 40,
  strokeWidth = 4,
  variant = 'default',
  showValue = false,
  indeterminate = false,
  className,
}) => {
  const [displayValue, setDisplayValue] = React.useState(0)
  
  React.useEffect(() => {
    if (!indeterminate) {
      const timer = setTimeout(() => setDisplayValue(value), 100)
      return () => clearTimeout(timer)
    }
  }, [value, indeterminate])

  const radius = (size - strokeWidth) / 2
  const circumference = 2 * Math.PI * radius
  const offset = circumference - (displayValue / 100) * circumference

  const colorMap = {
    default: '#2563eb', // blue-600
    success: '#16a34a', // green-600
    warning: '#ea580c', // orange-600
    error: '#dc2626',   // red-600
  }

  return (
    <div className={clsx('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        className={indeterminate ? 'animate-spin' : ''}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#e5e7eb"
          strokeWidth={strokeWidth}
          fill="none"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={colorMap[variant]}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={indeterminate ? circumference * 0.75 : offset}
          className="transition-all duration-300 ease-in-out"
          style={{
            transform: 'rotate(-90deg)',
            transformOrigin: '50% 50%',
          }}
        />
      </svg>
      
      {showValue && !indeterminate && (
        <span className="absolute text-xs font-medium">
          {Math.round(displayValue)}%
        </span>
      )}
    </div>
  )
}
CircularProgress.displayName = 'CircularProgress'

export type { ProgressProps, CircularProgressProps }
export { Progress, CircularProgress }

