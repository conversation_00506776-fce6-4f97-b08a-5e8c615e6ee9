import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const loadingDotsVariants = cva(
  [
    'inline-flex items-center justify-center gap-1'
  ],
  {
    variants: {
      size: {
        sm: 'h-4',
        md: 'h-6', 
        lg: 'h-8',
        xl: 'h-12'
      }
    },
    defaultVariants: {
      size: 'md'
    }
  }
)

const dotVariants = cva(
  [
    'rounded-full bg-current',
    'animate-[ellipsis_1.4s_ease-in-out_infinite]'
  ],
  {
    variants: {
      size: {
        sm: 'h-1 w-1',
        md: 'h-1.5 w-1.5',
        lg: 'h-2 w-2',
        xl: 'h-3 w-3'
      }
    }
  }
)

interface LoadingDotsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingDotsVariants> {
  asChild?: boolean
}

const LoadingDots = React.forwardRef<HTMLDivElement, LoadingDotsProps>(
  ({ className, size = 'md', asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'div'
    
    return (
      <Comp
        className={clsx(loadingDotsVariants({ size }), className)}
        ref={ref}
        {...props}
      >
        <div 
          className={clsx(dotVariants({ size }))}
          style={{ animationDelay: '0s' }}
        />
        <div 
          className={clsx(dotVariants({ size }))}
          style={{ animationDelay: '0.28s' }}
        />
        <div 
          className={clsx(dotVariants({ size }))}
          style={{ animationDelay: '0.56s' }}
        />
      </Comp>
    )
  }
)
LoadingDots.displayName = 'LoadingDots'

export type { LoadingDotsProps }
export { LoadingDots }