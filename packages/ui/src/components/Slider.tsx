import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const sliderVariants = cva(
  'relative flex touch-none select-none items-center pt-2 pb-1',
  {
    variants: {
      orientation: {
        horizontal: 'w-full',
        vertical: 'h-full flex-col',
      },
      size: {
        sm: '',
        default: '',
        lg: '',
      },
    },
    defaultVariants: {
      orientation: 'horizontal',
      size: 'default',
    },
  }
)

const trackVariants = cva(
  'relative grow rounded-full bg-gray-200',
  {
    variants: {
      orientation: {
        horizontal: 'h-2 w-full',
        vertical: 'w-2 h-full',
      },
      size: {
        sm: '',
        default: '',
        lg: '',
      },
    },
    compoundVariants: [
      {
        orientation: 'horizontal',
        size: 'sm',
        class: 'h-1',
      },
      {
        orientation: 'horizontal',
        size: 'default',
        class: 'h-2',
      },
      {
        orientation: 'horizontal',
        size: 'lg',
        class: 'h-3',
      },
      {
        orientation: 'vertical',
        size: 'sm',
        class: 'w-1',
      },
      {
        orientation: 'vertical',
        size: 'default',
        class: 'w-2',
      },
      {
        orientation: 'vertical',
        size: 'lg',
        class: 'w-3',
      },
    ],
    defaultVariants: {
      orientation: 'horizontal',
      size: 'default',
    },
  }
)

const rangeVariants = cva(
  'absolute rounded-full bg-blue-600',
  {
    variants: {
      orientation: {
        horizontal: 'h-full',
        vertical: 'w-full',
      },
      variant: {
        default: 'bg-blue-600',
        success: 'bg-green-600',
        warning: 'bg-orange-600',
        error: 'bg-red-600',
      },
    },
    defaultVariants: {
      orientation: 'horizontal',
      variant: 'default',
    },
  }
)

const thumbVariants = cva(
  'block rounded-full border-2 border-blue-600 bg-white ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      size: {
        sm: 'h-4 w-4',
        default: 'h-5 w-5',
        lg: 'h-6 w-6',
      },
      variant: {
        default: 'border-blue-600 focus-visible:ring-blue-600',
        success: 'border-green-600 focus-visible:ring-green-600',
        warning: 'border-orange-600 focus-visible:ring-orange-600',
        error: 'border-red-600 focus-visible:ring-red-600',
      },
    },
    defaultVariants: {
      size: 'default',
      variant: 'default',
    },
  }
)

interface SliderProps
  extends Omit<React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>, 'orientation'>,
    VariantProps<typeof sliderVariants> {
  variant?: 'default' | 'success' | 'warning' | 'error'
  showValue?: boolean
  showTicks?: boolean
  marks?: Array<{ value: number; label?: string }>
}

const Slider = React.forwardRef<
  React.ComponentRef<typeof SliderPrimitive.Root>,
  SliderProps
>(({ 
  className, 
  orientation = 'horizontal',
  size = 'default',
  variant = 'default',
  showValue = false,
  showTicks = false,
  marks = [],
  value,
  onValueChange,
  min = 0,
  max = 100,
  step = 1,
  ...props 
}, ref) => {
  const [internalValue, setInternalValue] = React.useState(value || [0])
  const currentValue = value || internalValue

  const handleValueChange = (newValue: number[]) => {
    setInternalValue(newValue)
    if (onValueChange) {
      onValueChange(newValue)
    }
  }

  const isVertical = orientation === 'vertical'

  return (
    <div className={clsx('relative', isVertical ? 'h-48' : 'w-full')}>
      <SliderPrimitive.Root
        ref={ref}
        className={clsx(sliderVariants({ orientation, size, className }))}
        orientation={orientation || 'horizontal'}
        value={currentValue}
        onValueChange={handleValueChange}
        min={min}
        max={max}
        step={step}
        {...props}
      >
        <SliderPrimitive.Track className={trackVariants({ orientation, size })}>
          <SliderPrimitive.Range className={rangeVariants({ orientation, variant })} />
        </SliderPrimitive.Track>
        
        {currentValue.map((_, index) => (
          <SliderPrimitive.Thumb
            key={index}
            className={thumbVariants({ size, variant })}
          />
        ))}
      </SliderPrimitive.Root>

      {/* Show current value(s) */}
      {showValue && (
        <div className={clsx(
          'absolute text-xs text-gray-600',
          isVertical ? '-right-8 top-0' : 'left-0 -bottom-6'
        )}>
          {currentValue.length === 1 ? (
            <span>{currentValue[0]}</span>
          ) : (
            <span>{currentValue[0]} - {currentValue[1]}</span>
          )}
        </div>
      )}

      {/* Show ticks for steps */}
      {showTicks && (
        <div className={clsx(
          'absolute flex',
          isVertical ? 'flex-col h-full -left-2 top-0' : 'w-full -bottom-2 left-0'
        )}>
          {Array.from({ length: Math.floor((max - min) / step) + 1 }, (_, i) => {
            const tickValue = min + i * step
            const percentage = ((tickValue - min) / (max - min)) * 100
            
            return (
              <div
                key={i}
                className={clsx(
                  'absolute w-1 h-1 bg-gray-400 rounded-full',
                  isVertical ? 'left-0' : 'top-0'
                )}
                style={isVertical ? { top: `${100 - percentage}%` } : { left: `${percentage}%` }}
              />
            )
          })}
        </div>
      )}

      {/* Show custom marks */}
      {marks.length > 0 && (
        <div className={clsx(
          'absolute',
          isVertical ? 'h-full -right-4 top-0' : 'w-full -bottom-8 left-0'
        )}>
          {marks.map((mark, index) => {
            const percentage = ((mark.value - min) / (max - min)) * 100
            
            return (
              <div
                key={index}
                className="absolute text-xs text-gray-600"
                style={isVertical ? { top: `${100 - percentage}%` } : { left: `${percentage}%` }}
              >
                {mark.label || mark.value}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
})

Slider.displayName = 'Slider'

// Type exports
export type { SliderProps }

// Component exports
export { Slider }

