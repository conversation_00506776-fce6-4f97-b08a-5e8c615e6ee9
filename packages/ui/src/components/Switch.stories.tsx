import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { Switch } from './Switch'
import { Label } from './Label'

const meta: Meta<typeof Switch> = {
  title: 'Components/Switch',
  component: Switch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'destructive'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    variant: 'default',
    size: 'default',
  },
}

export const WithLabel: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Switch id="airplane-mode" />
      <Label htmlFor="airplane-mode">Airplane Mode</Label>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch size="sm" id="small-switch" />
        <Label htmlFor="small-switch">Small</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch size="default" id="default-switch" />
        <Label htmlFor="default-switch">Default</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch size="lg" id="large-switch" />
        <Label htmlFor="large-switch">Large</Label>
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch variant="default" id="default-variant" defaultChecked />
        <Label htmlFor="default-variant">Default variant</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch variant="destructive" id="destructive-variant" defaultChecked />
        <Label htmlFor="destructive-variant">Destructive variant</Label>
      </div>
    </div>
  ),
}