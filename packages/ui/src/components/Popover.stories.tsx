import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { But<PERSON> } from './Button'
import { Label } from './Label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from './Popover'

const meta: Meta<typeof Popover> = {
  title: 'Components/Popover',
  component: Popover,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outlined">Open popover</Button>
      </PopoverTrigger>
      <PopoverContent>
        <div className="space-y-2">
          <h4 className="font-medium leading-none">Dimensions</h4>
          <p className="text-sm text-gray-600">
            Set the dimensions for the layer.
          </p>
        </div>
        <div className="grid gap-2 mt-3">
          <div className="grid grid-cols-3 items-center gap-4">
            <Label htmlFor="width">Width</Label>
            <input
              id="width"
              defaultValue="100%"
              className="col-span-2 h-8 px-2 text-sm border rounded"
            />
          </div>
          <div className="grid grid-cols-3 items-center gap-4">
            <Label htmlFor="height">Height</Label>
            <input
              id="height"
              defaultValue="25px"
              className="col-span-2 h-8 px-2 text-sm border rounded"
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  ),
}

export const WithForm: Story = {
  render: () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outlined">Update profile</Button>
      </PopoverTrigger>
      <PopoverContent align="start">
        <form className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Update Profile</h4>
            <p className="text-sm text-gray-600">
              Make changes to your profile information.
            </p>
          </div>
          <div className="space-y-3">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <input
                id="name"
                placeholder="Your name"
                className="w-full h-8 px-2 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="email">Email</Label>
              <input
                id="email"
                type="email"
                placeholder="Your email"
                className="w-full h-8 px-2 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button size="sm" variant="outlined">Cancel</Button>
            <Button size="sm">Save</Button>
          </div>
        </form>
      </PopoverContent>
    </Popover>
  ),
}