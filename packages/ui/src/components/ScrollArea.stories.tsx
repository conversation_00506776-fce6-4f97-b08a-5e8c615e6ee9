import type { Meta, StoryObj } from '@storybook/react-vite'
import { ScrollArea, ScrollAreaViewport, ScrollAreaScrollbar, ScrollAreaThumb } from './ScrollArea'

const meta: Meta<typeof ScrollArea> = {
  title: 'Components/ScrollArea',
  component: ScrollArea,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Sample content for demonstrations
const sampleText = `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur?`

const chatMessages = [
  { id: 1, user: 'Alice', message: 'Hey there! How are you doing?', time: '10:30 AM' },
  { id: 2, user: 'Bob', message: 'Hi Alice! I\'m doing great, thanks for asking. How about you?', time: '10:32 AM' },
  { id: 3, user: 'Alice', message: 'I\'m good too! Working on a new project using Radix UI components.', time: '10:33 AM' },
  { id: 4, user: 'Bob', message: 'That sounds interesting! Radix UI has some really nice primitives.', time: '10:35 AM' },
  { id: 5, user: 'Alice', message: 'Absolutely! The ScrollArea component we\'re building is particularly useful.', time: '10:37 AM' },
  { id: 6, user: 'Bob', message: 'I love how customizable their components are while maintaining accessibility.', time: '10:38 AM' },
  { id: 7, user: 'Alice', message: 'Exactly! And the styling with Tailwind makes it so much easier to customize.', time: '10:40 AM' },
  { id: 8, user: 'Bob', message: 'Have you tried their other components like Dialog and DropdownMenu?', time: '10:42 AM' },
  { id: 9, user: 'Alice', message: 'Yes! We\'ve implemented quite a few of them already. They work great together.', time: '10:43 AM' },
  { id: 10, user: 'Bob', message: 'That\'s awesome! I should definitely check out your component library.', time: '10:45 AM' },
]

export const Default: Story = {
  render: (args) => (
    <ScrollArea {...args}>
      <ScrollAreaViewport>
        <div className="p-4">
          <h4 className="mb-4 text-sm font-medium leading-none">Scrollable Text</h4>
          <p className="text-sm text-gray-600">{sampleText}</p>
        </div>
      </ScrollAreaViewport>
      <ScrollAreaScrollbar orientation="vertical">
        <ScrollAreaThumb />
      </ScrollAreaScrollbar>
    </ScrollArea>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex space-x-4">
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Small</h4>
        <ScrollArea size="sm" className="w-48 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText}</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ScrollArea className="w-48 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText}</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Large</h4>
        <ScrollArea size="lg" className="w-48 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText}</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
    </div>
  ),
}

export const HorizontalScroll: Story = {
  render: () => (
    <ScrollArea className="w-96 h-24 border rounded-md">
      <ScrollAreaViewport>
        <div className="flex w-max space-x-4 p-4">
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={i}
              className="flex h-12 w-24 shrink-0 items-center justify-center rounded-md bg-gray-100 text-sm font-medium"
            >
              Item {i + 1}
            </div>
          ))}
        </div>
      </ScrollAreaViewport>
      <ScrollAreaScrollbar orientation="horizontal">
        <ScrollAreaThumb />
      </ScrollAreaScrollbar>
    </ScrollArea>
  ),
}

export const BothDirections: Story = {
  render: () => (
    <ScrollArea className="w-96 h-64 border rounded-md">
      <ScrollAreaViewport>
        <div className="p-4">
          <div className="grid grid-cols-8 gap-2 w-max">
            {Array.from({ length: 100 }, (_, i) => (
              <div
                key={i}
                className="flex h-16 w-20 shrink-0 items-center justify-center rounded-md bg-gray-100 text-xs font-medium"
              >
                {i + 1}
              </div>
            ))}
          </div>
        </div>
      </ScrollAreaViewport>
      <ScrollAreaScrollbar orientation="vertical">
        <ScrollAreaThumb />
      </ScrollAreaScrollbar>
      <ScrollAreaScrollbar orientation="horizontal">
        <ScrollAreaThumb />
      </ScrollAreaScrollbar>
    </ScrollArea>
  ),
}

export const ScrollbarWidths: Story = {
  render: () => (
    <div className="flex space-x-4">
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Thin</h4>
        <ScrollArea className="w-48 h-32 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText.slice(0, 200)}...</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical" width="thin">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ScrollArea className="w-48 h-32 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText.slice(0, 200)}...</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical" width="default">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Thick</h4>
        <ScrollArea className="w-48 h-32 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText.slice(0, 200)}...</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical" width="thick">
            <ScrollAreaThumb />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
    </div>
  ),
}

export const ThumbVariants: Story = {
  render: () => (
    <div className="flex space-x-4">
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ScrollArea className="w-48 h-32 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText.slice(0, 200)}...</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical">
            <ScrollAreaThumb variant="default" />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
      <div className="text-center">
        <h4 className="mb-2 text-sm font-medium">Minimal</h4>
        <ScrollArea className="w-48 h-32 border rounded-md">
          <ScrollAreaViewport>
            <div className="p-4">
              <p className="text-sm text-gray-600">{sampleText.slice(0, 200)}...</p>
            </div>
          </ScrollAreaViewport>
          <ScrollAreaScrollbar orientation="vertical">
            <ScrollAreaThumb variant="minimal" />
          </ScrollAreaScrollbar>
        </ScrollArea>
      </div>
    </div>
  ),
}

export const ChatExample: Story = {
  render: () => (
    <ScrollArea className="w-80 h-96 border rounded-lg bg-white">
      <ScrollAreaViewport>
        <div className="p-4 space-y-4">
          <div className="border-b pb-2 mb-4">
            <h3 className="font-semibold text-gray-900">Chat Messages</h3>
            <p className="text-sm text-gray-500">Scroll to see more messages</p>
          </div>
          {chatMessages.map((msg) => (
            <div key={msg.id} className="flex flex-col space-y-1">
              <div className="flex items-center justify-between">
                <span className="font-medium text-sm text-blue-600">{msg.user}</span>
                <span className="text-xs text-gray-400">{msg.time}</span>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700">{msg.message}</p>
              </div>
            </div>
          ))}
        </div>
      </ScrollAreaViewport>
      <ScrollAreaScrollbar orientation="vertical">
        <ScrollAreaThumb />
      </ScrollAreaScrollbar>
    </ScrollArea>
  ),
}

export const CodeEditor: Story = {
  render: () => {
    const codeLines = [
      'import * as React from "react"',
      'import { ScrollArea } from "./ScrollArea"',
      '',
      'export const MyComponent = () => {',
      '  return (',
      '    <ScrollArea className="w-full h-64">',
      '      <ScrollAreaViewport>',
      '        <div className="p-4">',
      '          <h1>Hello World</h1>',
      '          <p>This is a scrollable code editor example.</p>',
      '          <pre>',
      '            <code>',
      '              const example = "code";',
      '              console.log(example);',
      '            </code>',
      '          </pre>',
      '        </div>',
      '      </ScrollAreaViewport>',
      '      <ScrollAreaScrollbar orientation="vertical">',
      '        <ScrollAreaThumb />',
      '      </ScrollAreaScrollbar>',
      '    </ScrollArea>',
      '  )',
      '}',
    ]

    return (
      <ScrollArea className="w-96 h-64 border rounded-lg bg-gray-900 text-gray-100">
        <ScrollAreaViewport>
          <div className="p-4 font-mono text-sm">
            {codeLines.map((line, index) => (
              <div key={index} className="flex">
                <span className="mr-4 text-gray-500 select-none w-6 text-right">
                  {line.trim() ? index + 1 : ''}
                </span>
                <span className="text-gray-100">{line || '\u00A0'}</span>
              </div>
            ))}
          </div>
        </ScrollAreaViewport>
        <ScrollAreaScrollbar orientation="vertical">
          <ScrollAreaThumb variant="minimal" />
        </ScrollAreaScrollbar>
      </ScrollArea>
    )
  },
}

export const DataList: Story = {
  render: () => {
    const users = Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: `User ${i + 1}`,
      email: `user${i + 1}@example.com`,
      status: i % 3 === 0 ? 'offline' : 'online',
    }))

    return (
      <ScrollArea className="w-80 h-80 border rounded-lg">
        <ScrollAreaViewport>
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-4">User Directory</h3>
            <div className="space-y-2">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600">
                        {user.name.charAt(0)}{user.name.split(' ')[1]?.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{user.name}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full ${
                      user.status === 'online' ? 'bg-green-400' : 'bg-gray-300'
                    }`} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </ScrollAreaViewport>
        <ScrollAreaScrollbar orientation="vertical">
          <ScrollAreaThumb />
        </ScrollAreaScrollbar>
      </ScrollArea>
    )
  },
}