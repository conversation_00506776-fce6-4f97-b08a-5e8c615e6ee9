import * as React from 'react'
import * as PortalPrimitive from '@radix-ui/react-portal'

interface PortalProps extends React.ComponentPropsWithoutRef<typeof PortalPrimitive.Root> {
  container?: HTMLElement
}

const Portal = React.forwardRef<
  React.ComponentRef<typeof PortalPrimitive.Root>,
  PortalProps
>(({ container, ...props }, ref) => (
  <PortalPrimitive.Root
    ref={ref}
    container={container}
    {...props}
  />
))
Portal.displayName = PortalPrimitive.Root.displayName

export { Portal }
export type { PortalProps }