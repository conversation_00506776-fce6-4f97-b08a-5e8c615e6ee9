import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import {
  Toast,
  ToastAction,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from './Toast'

const meta: Meta<typeof Toast> = {
  title: 'Components/Toast',
  component: Toast,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'destructive', 'success', 'warning'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    variant: 'default',
  },
  render: (args) => (
    <ToastProvider>
      <Toast {...args}>
        <div className="grid gap-1">
          <ToastTitle>Scheduled: Catch up</ToastTitle>
          <ToastDescription>Friday, February 10, 2023 at 5:57 PM</ToastDescription>
        </div>
        <ToastAction altText="Goto schedule to undo">Undo</ToastAction>
        <ToastClose />
      </Toast>
      <ToastViewport />
    </ToastProvider>
  ),
}

export const Simple: Story = {
  render: () => (
    <ToastProvider>
      <Toast>
        <ToastDescription>Your message has been sent.</ToastDescription>
        <ToastClose />
      </Toast>
      <ToastViewport />
    </ToastProvider>
  ),
}

export const WithAction: Story = {
  render: () => (
    <ToastProvider>
      <Toast>
        <div className="grid gap-1">
          <ToastTitle>Uh oh! Something went wrong.</ToastTitle>
          <ToastDescription>There was a problem with your request.</ToastDescription>
        </div>
        <ToastAction altText="Try again">Try again</ToastAction>
        <ToastClose />
      </Toast>
      <ToastViewport />
    </ToastProvider>
  ),
}

export const Variants: Story = {
  render: () => (
    <ToastProvider>
      <div className="space-y-4">
        <Toast variant="default">
          <ToastDescription>This is a default toast.</ToastDescription>
          <ToastClose />
        </Toast>
        
        <Toast variant="destructive">
          <ToastDescription>This is a destructive toast.</ToastDescription>
          <ToastClose />
        </Toast>
        
        <Toast variant="success">
          <ToastDescription>This is a success toast.</ToastDescription>
          <ToastClose />
        </Toast>
        
        <Toast variant="warning">
          <ToastDescription>This is a warning toast.</ToastDescription>
          <ToastClose />
        </Toast>
      </div>
      <ToastViewport />
    </ToastProvider>
  ),
}