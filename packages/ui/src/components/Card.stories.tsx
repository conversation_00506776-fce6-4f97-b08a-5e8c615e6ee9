import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { But<PERSON> } from './Button'
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter, 
  CardAction 
} from './Card'

const meta: Meta<typeof Card> = {
  title: 'Components/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes to apply to the card',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>
          This is a description of the card content. It provides context about what the card contains.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p>This is the main content of the card. You can put any content here.</p>
      </CardContent>
      <CardFooter>
        <Button variant="default">Action</Button>
      </CardFooter>
    </Card>
  ),
}

export const WithAction: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Settings</CardTitle>
        <CardDescription>
          Manage your account settings and preferences.
        </CardDescription>
        <CardAction>
          <Button variant="ghost" size="sm">Edit</Button>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Email notifications</span>
            <span className="text-muted-foreground">Enabled</span>
          </div>
          <div className="flex justify-between">
            <span>Theme</span>
            <span className="text-muted-foreground">Light</span>
          </div>
        </div>
      </CardContent>
    </Card>
  ),
}

export const Simple: Story = {
  render: () => (
    <Card className="w-80">
      <CardContent>
        <p>A simple card with just content, no header or footer.</p>
      </CardContent>
    </Card>
  ),
}

export const HeaderOnly: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Quick Stats</CardTitle>
        <CardDescription>Overview of your account</CardDescription>
      </CardHeader>
    </Card>
  ),
}

export const WithBorder: Story = {
  render: () => (
    <Card className="w-80 border-2 border-dashed border-gray-300">
      <CardHeader className="border-b">
        <CardTitle>Upload Files</CardTitle>
        <CardDescription>Drag and drop files here</CardDescription>
      </CardHeader>
      <CardContent className="py-8 text-center text-muted-foreground">
        <p>Drop your files here or click to browse</p>
      </CardContent>
    </Card>
  ),
}

export const MultipleActions: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Project Settings</CardTitle>
        <CardDescription>
          Configure your project preferences and settings.
        </CardDescription>
        <CardAction>
          <div className="flex gap-2">
            <Button variant="ghost" size="sm">Cancel</Button>
            <Button variant="default" size="sm">Save</Button>
          </div>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Project Name</label>
            <input 
              type="text" 
              className="w-full mt-1 px-3 py-2 border rounded-md" 
              defaultValue="My Project" 
            />
          </div>
          <div>
            <label className="text-sm font-medium">Description</label>
            <textarea 
              className="w-full mt-1 px-3 py-2 border rounded-md" 
              rows={3}
              defaultValue="Project description"
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t">
        <div className="flex w-full justify-between text-sm text-muted-foreground">
          <span>Last updated: 2 hours ago</span>
          <span>Auto-save enabled</span>
        </div>
      </CardFooter>
    </Card>
  ),
}