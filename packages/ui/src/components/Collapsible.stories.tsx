import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { useState } from 'react'
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from './Collapsible'
import { Button } from './Button'

const meta: Meta<typeof Collapsible> = {
  title: 'Components/Collapsible',
  component: Collapsible,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'card'],
    },
    open: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: (args) => (
    <div className="w-96">
      <Collapsible {...args}>
        <CollapsibleTrigger>Can I use this in my project?</CollapsibleTrigger>
        <CollapsibleContent>
          Yes. Free to use for personal and commercial projects. No attribution required.
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const Controlled: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <div className="w-96 space-y-4">
        <div className="flex items-center space-x-2">
          <Button 
            size="sm" 
            variant="outlined"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? 'Close' : 'Open'}
          </Button>
          <span className="text-sm text-gray-600">
            External control
          </span>
        </div>
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger>Is it styled?</CollapsibleTrigger>
          <CollapsibleContent>
            Yes. It comes with default styles that matches the other components' aesthetic.
            You can customize it further with CSS classes.
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  },
}

export const CardVariant: Story = {
  render: () => (
    <div className="w-96">
      <Collapsible variant="card">
        <CollapsibleTrigger>What is Radix UI?</CollapsibleTrigger>
        <CollapsibleContent>
          Radix UI is a low-level UI primitive library with a focus on accessibility, 
          customization and developer experience. You can use these components either as 
          the base layer of your design system, or adopt them incrementally.
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <Collapsible>
          <CollapsibleTrigger>How do I get started?</CollapsibleTrigger>
          <CollapsibleContent>
            Install the component from your command line and follow the setup guide.
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      <div>
        <h4 className="mb-2 text-sm font-medium">Card</h4>
        <Collapsible variant="card">
          <CollapsibleTrigger>How do I get started?</CollapsibleTrigger>
          <CollapsibleContent>
            Install the component from your command line and follow the setup guide.
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Small</h4>
        <Collapsible>
          <CollapsibleTrigger size="sm">Small trigger</CollapsibleTrigger>
          <CollapsibleContent size="sm">
            This is small content with reduced padding and font size.
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      <div>
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <Collapsible>
          <CollapsibleTrigger size="default">Default trigger</CollapsibleTrigger>
          <CollapsibleContent size="default">
            This is default content with standard padding and font size.
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      <div>
        <h4 className="mb-2 text-sm font-medium">Large</h4>
        <Collapsible>
          <CollapsibleTrigger size="lg">Large trigger</CollapsibleTrigger>
          <CollapsibleContent size="lg">
            This is large content with increased padding and font size.
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  ),
}

export const MultipleItems: Story = {
  render: () => (
    <div className="w-96 space-y-2">
      <Collapsible>
        <CollapsibleTrigger>Is it accessible?</CollapsibleTrigger>
        <CollapsibleContent>
          Yes. It adheres to the WAI-ARIA design pattern and uses roving tabindex 
          for keyboard navigation.
        </CollapsibleContent>
      </Collapsible>
      
      <div className="border-t border-gray-200" />
      
      <Collapsible>
        <CollapsibleTrigger>Can I use it with TypeScript?</CollapsibleTrigger>
        <CollapsibleContent>
          Yes. All components are built with TypeScript and include comprehensive 
          type definitions.
        </CollapsibleContent>
      </Collapsible>
      
      <div className="border-t border-gray-200" />
      
      <Collapsible>
        <CollapsibleTrigger>How do I customize the styling?</CollapsibleTrigger>
        <CollapsibleContent>
          You can customize the styling by passing custom className props or by 
          overriding the CSS variables. The components are built with Tailwind CSS.
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const RichContent: Story = {
  render: () => (
    <div className="w-96">
      <Collapsible variant="card">
        <CollapsibleTrigger>Project Requirements</CollapsibleTrigger>
        <CollapsibleContent>
          <div className="space-y-4">
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Technical Stack</h5>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>React 18+</li>
                <li>TypeScript</li>
                <li>Tailwind CSS</li>
                <li>Radix UI Primitives</li>
              </ul>
            </div>
            
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Features</h5>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Fully accessible components</li>
                <li>Keyboard navigation support</li>
                <li>Customizable styling</li>
                <li>TypeScript support</li>
              </ul>
            </div>
            
            <div className="flex space-x-2 pt-2">
              <Button size="sm" variant="contained">
                Get Started
              </Button>
              <Button size="sm" variant="outlined">
                Learn More
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const CodeSnippet: Story = {
  render: () => (
    <div className="w-96">
      <Collapsible>
        <CollapsibleTrigger>Show implementation</CollapsibleTrigger>
        <CollapsibleContent>
          <pre className="bg-gray-900 text-gray-100 p-4 rounded-md text-xs overflow-x-auto">
            <code>{`import { Collapsible, CollapsibleTrigger, CollapsibleContent } from './Collapsible'

function Example() {
  return (
    <Collapsible>
      <CollapsibleTrigger>
        Toggle Content
      </CollapsibleTrigger>
      <CollapsibleContent>
        Hidden content goes here...
      </CollapsibleContent>
    </Collapsible>
  )
}`}</code>
          </pre>
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const SettingsPanel: Story = {
  render: () => (
    <div className="w-96 space-y-2">
      <Collapsible variant="card">
        <CollapsibleTrigger>Account Settings</CollapsibleTrigger>
        <CollapsibleContent>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Name
              </label>
              <input 
                type="text" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                defaultValue="John Doe"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input 
                type="email" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                defaultValue="<EMAIL>"
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
      
      <Collapsible variant="card">
        <CollapsibleTrigger>Privacy Settings</CollapsibleTrigger>
        <CollapsibleContent>
          <div className="space-y-3">
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-sm">Make profile public</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              <span className="text-sm">Allow search engines to index</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-sm">Receive email notifications</span>
            </label>
          </div>
        </CollapsibleContent>
      </Collapsible>
      
      <Collapsible variant="card">
        <CollapsibleTrigger>Advanced Options</CollapsibleTrigger>
        <CollapsibleContent>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key
              </label>
              <input 
                type="password" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                defaultValue="••••••••••••••••"
              />
            </div>
            <div className="flex space-x-2">
              <Button size="sm" variant="contained" color="error">
                Delete Account
              </Button>
              <Button size="sm" variant="outlined">
                Export Data
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  ),
}

export const FAQSection: Story = {
  render: () => {
    const faqs = [
      {
        question: "What is your return policy?",
        answer: "We offer a 30-day return policy for all unused items. Items must be returned in their original packaging with all tags attached."
      },
      {
        question: "How long does shipping take?",
        answer: "Standard shipping takes 3-5 business days. Express shipping is available for 1-2 business days delivery."
      },
      {
        question: "Do you ship internationally?",
        answer: "Yes, we ship to over 50 countries worldwide. International shipping typically takes 7-14 business days."
      },
      {
        question: "How can I track my order?",
        answer: "Once your order ships, you'll receive a tracking number via email. You can use this number on our website or the carrier's website to track your package."
      }
    ]
    
    return (
      <div className="w-96 space-y-1">
        <h3 className="text-lg font-semibold mb-4">Frequently Asked Questions</h3>
        {faqs.map((faq, index) => (
          <div key={index}>
            <Collapsible>
              <CollapsibleTrigger>{faq.question}</CollapsibleTrigger>
              <CollapsibleContent>
                <p className="text-gray-600">{faq.answer}</p>
              </CollapsibleContent>
            </Collapsible>
            {index < faqs.length - 1 && <div className="border-t border-gray-200 my-1" />}
          </div>
        ))}
      </div>
    )
  },
}

export const DisabledState: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Enabled</h4>
        <Collapsible>
          <CollapsibleTrigger>This can be toggled</CollapsibleTrigger>
          <CollapsibleContent>
            This content can be expanded and collapsed.
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      <div>
        <h4 className="mb-2 text-sm font-medium">Disabled</h4>
        <Collapsible disabled>
          <CollapsibleTrigger>This is disabled</CollapsibleTrigger>
          <CollapsibleContent>
            This content cannot be toggled when disabled.
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  ),
}