import * as React from 'react'
import * as HoverCardPrimitive from '@radix-ui/react-hover-card'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const hoverCardContentVariants = cva(
  'z-50 w-64 rounded-md border bg-white p-4 text-gray-950 shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
  {
    variants: {
      variant: {
        default: 'border-gray-200',
        dark: 'border-gray-700 bg-gray-800 text-gray-50',
      },
      size: {
        sm: 'w-48 p-3',
        default: 'w-64 p-4',
        lg: 'w-80 p-6',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const HoverCard = HoverCardPrimitive.Root
const HoverCardTrigger = HoverCardPrimitive.Trigger

interface HoverCardContentProps
  extends React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>,
    VariantProps<typeof hoverCardContentVariants> {}

const HoverCardContent = React.forwardRef<
  React.ComponentRef<typeof HoverCardPrimitive.Content>,
  HoverCardContentProps
>(({ className, variant, size, sideOffset = 4, ...props }, ref) => (
  <HoverCardPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={clsx(hoverCardContentVariants({ variant, size }), className)}
    {...props}
  />
))
HoverCardContent.displayName = 'HoverCardContent'

const HoverCardArrow = React.forwardRef<
  React.ComponentRef<typeof HoverCardPrimitive.Arrow>,
  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Arrow>
>(({ className, ...props }, ref) => (
  <HoverCardPrimitive.Arrow
    ref={ref}
    className={clsx('fill-white', className)}
    {...props}
  />
))
HoverCardArrow.displayName = 'HoverCardArrow'

const HoverCardHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={clsx('flex space-x-4', className)}
    {...props}
  />
)
HoverCardHeader.displayName = 'HoverCardHeader'

const HoverCardAvatar = ({
  className,
  src,
  alt,
  fallback,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  src?: string
  alt?: string
  fallback?: string
}) => (
  <div
    className={clsx('h-12 w-12 rounded-full overflow-hidden flex-shrink-0', className)}
    {...props}
  >
    {src ? (
      <img 
        src={src} 
        alt={alt} 
        className="h-full w-full object-cover"
      />
    ) : (
      <div className="h-full w-full bg-gray-200 flex items-center justify-center text-gray-600 text-sm font-medium">
        {fallback}
      </div>
    )}
  </div>
)
HoverCardAvatar.displayName = 'HoverCardAvatar'

const HoverCardTitle = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => (
  <h3
    className={clsx('text-lg font-semibold', className)}
    {...props}
  />
)
HoverCardTitle.displayName = 'HoverCardTitle'

const HoverCardDescription = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) => (
  <p
    className={clsx('text-sm text-gray-600', className)}
    {...props}
  />
)
HoverCardDescription.displayName = 'HoverCardDescription'

const HoverCardFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={clsx('flex items-center pt-2', className)}
    {...props}
  />
)
HoverCardFooter.displayName = 'HoverCardFooter'

// Type exports
export type { HoverCardContentProps }

// Component exports
export {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
  HoverCardArrow,
  HoverCardHeader,
  HoverCardAvatar,
  HoverCardTitle,
  HoverCardDescription,
  HoverCardFooter,
}