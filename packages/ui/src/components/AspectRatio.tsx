import * as React from 'react'
import * as AspectRatioPrimitive from '@radix-ui/react-aspect-ratio'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const aspectRatioVariants = cva(
  'relative overflow-hidden',
  {
    variants: {
      ratio: {
        square: '', // 1:1
        photo: '', // 4:3
        video: '', // 16:9
        cinema: '', // 21:9
        portrait: '', // 3:4
        wide: '', // 2:1
      },
    },
    defaultVariants: {
      ratio: 'video',
    },
  }
)

const ratioMap = {
  square: 1,
  photo: 4 / 3,
  video: 16 / 9,
  cinema: 21 / 9,
  portrait: 3 / 4,
  wide: 2 / 1,
} as const

interface AspectRatioProps
  extends Omit<React.ComponentPropsWithoutRef<typeof AspectRatioPrimitive.Root>, 'ratio'>,
    Omit<VariantProps<typeof aspectRatioVariants>, 'ratio'> {
  ratio?: number | keyof typeof ratioMap
}

const AspectRatio = React.forwardRef<
  React.ComponentRef<typeof AspectRatioPrimitive.Root>,
  AspectRatioProps
>(({ className, ratio = 'video', ...props }, ref) => {
  const aspectRatio = typeof ratio === 'string' ? ratioMap[ratio] : ratio

  return (
    <AspectRatioPrimitive.Root
      ref={ref}
      ratio={aspectRatio}
      className={clsx(
        aspectRatioVariants({ 
          ratio: typeof ratio === 'string' ? ratio : undefined, 
          className 
        })
      )}
      {...props}
    />
  )
})
AspectRatio.displayName = AspectRatioPrimitive.Root.displayName

export type { AspectRatioProps }
export { AspectRatio }
