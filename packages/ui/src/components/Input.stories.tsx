import type { Meta, StoryObj } from '@storybook/react-vite'
import { Input } from './Input'
import { Label } from './Label'

const meta: Meta<typeof Input> = {
  title: 'Components/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'error', 'success'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    loading: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
    variant: 'default',
    size: 'default',
  },
}

export const WithLabel: Story = {
  render: () => (
    <div className="w-80 space-y-2">
      <Label htmlFor="email">Email address</Label>
      <Input id="email" type="email" placeholder="Enter your email" />
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>Small</Label>
        <Input size="sm" placeholder="Small input" />
      </div>
      <div>
        <Label>Default</Label>
        <Input size="default" placeholder="Default input" />
      </div>
      <div>
        <Label>Large</Label>
        <Input size="lg" placeholder="Large input" />
      </div>
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>Default</Label>
        <Input variant="default" placeholder="Default variant" />
      </div>
      <div>
        <Label>Error</Label>
        <Input variant="error" placeholder="Error variant" />
      </div>
      <div>
        <Label>Success</Label>
        <Input variant="success" placeholder="Success variant" />
      </div>
    </div>
  ),
}

export const WithIcons: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>With start icon</Label>
        <Input
          startIcon={
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          }
          placeholder="Search..."
        />
      </div>
      <div>
        <Label>With end icon</Label>
        <Input
          endIcon={
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 12l2 2 4-4" />
            </svg>
          }
          placeholder="Enter value"
        />
      </div>
    </div>
  ),
}

export const WithHelperText: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>With helper text</Label>
        <Input
          placeholder="Enter your name"
          helperText="This will be displayed publicly"
        />
      </div>
      <div>
        <Label>With error</Label>
        <Input
          placeholder="Enter email"
          errorText="Please enter a valid email address"
        />
      </div>
      <div>
        <Label>With success</Label>
        <Input
          placeholder="Enter password"
          successText="Password meets requirements"
        />
      </div>
    </div>
  ),
}

export const States: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <div>
        <Label>Normal</Label>
        <Input placeholder="Normal state" />
      </div>
      <div>
        <Label>Disabled</Label>
        <Input placeholder="Disabled state" disabled />
      </div>
      <div>
        <Label>Loading</Label>
        <Input placeholder="Loading state" loading />
      </div>
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="w-96 space-y-6 p-6 border rounded-lg">
      <h3 className="text-lg font-semibold">Contact Form</h3>
      
      <div>
        <Label htmlFor="name" variant="required">Full Name</Label>
        <Input
          id="name"
          placeholder="Enter your full name"
          helperText="As it appears on your ID"
        />
      </div>

      <div>
        <Label htmlFor="email" variant="required">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          startIcon={
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
              <polyline points="22,6 12,13 2,6" />
            </svg>
          }
        />
      </div>

      <div>
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          placeholder="(*************"
          helperText="Optional - for urgent matters only"
        />
      </div>
    </form>
  ),
}