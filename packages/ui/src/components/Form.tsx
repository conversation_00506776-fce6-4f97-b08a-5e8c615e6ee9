import * as React from 'react'
import * as FormPrimitive from '@radix-ui/react-form'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const formFieldVariants = cva(
  'space-y-2',
  {
    variants: {
      variant: {
        default: '',
        inline: 'flex items-center space-x-2 space-y-0',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

const formLabelVariants = cva(
  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
  {
    variants: {
      variant: {
        default: 'text-gray-900',
        error: 'text-red-600',
      },
      required: {
        true: 'after:content-["*"] after:ml-0.5 after:text-red-500',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      required: false,
    },
  }
)

const formControlVariants = cva(
  'flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/30 focus-visible:ring-inset disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        error: 'border-red-300 focus-visible:ring-red-500/30',
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-9 px-3 text-sm',
        lg: 'h-10 px-4 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const formMessageVariants = cva(
  'text-sm font-medium',
  {
    variants: {
      variant: {
        default: 'text-gray-600',
        error: 'text-red-600',
        success: 'text-green-600',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

const Form = FormPrimitive.Root

type FormFieldProps = FormPrimitive.FormFieldProps &
  { asChild?: boolean } &
  React.HTMLProps<HTMLDivElement> &
  VariantProps<typeof formFieldVariants>;

const FormField = React.forwardRef<
  React.ComponentRef<typeof FormPrimitive.Field>,
  FormFieldProps
>(({ className, variant, ...props }, ref) => (
  <FormPrimitive.Field
    ref={ref}
    className={clsx(formFieldVariants({ variant, className }))}
    {...props}
  />
))
FormField.displayName = 'FormField'

type FormLabelProps = FormPrimitive.FormLabelProps &
  { asChild?: boolean } &
  React.HTMLProps<HTMLLabelElement> &
  VariantProps<typeof formLabelVariants>;

const FormLabel = React.forwardRef<
  React.ComponentRef<typeof FormPrimitive.Label>,
  FormLabelProps
>(({ className, variant, required, ...props }, ref) => (
  <FormPrimitive.Label
    ref={ref}
    className={clsx(formLabelVariants({ variant, required, className }))}
    {...props}
  />
))
FormLabel.displayName = 'FormLabel'

type FormControlProps = FormPrimitive.FormControlProps &
  { asChild?: boolean } &
  React.HTMLProps<HTMLInputElement> &
  VariantProps<typeof formControlVariants>;

const FormControl = React.forwardRef<
  React.ComponentRef<typeof FormPrimitive.Control>,
  FormControlProps
>(({ className, variant, size, ...props }, ref) => (
  <FormPrimitive.Control
    ref={ref}
    className={clsx(formControlVariants({ variant, size, className }))}
    {...props}
  />
))
FormControl.displayName = 'FormControl'

type FormMessageProps = FormPrimitive.FormMessageProps &
  { asChild?: boolean } &
  React.HTMLProps<HTMLElement> &
  VariantProps<typeof formMessageVariants>;

const FormMessage = React.forwardRef<
  React.ComponentRef<typeof FormPrimitive.Message>,
  FormMessageProps
>(({ className, variant, children, ...props }, ref) => (
  <FormPrimitive.Message
    ref={ref}
    className={clsx(formMessageVariants({ variant, className }))}
    {...props}
  >
    {children}
  </FormPrimitive.Message>
))
FormMessage.displayName = 'FormMessage'

type FormSubmitProps = FormPrimitive.FormSubmitProps &
  { asChild?: boolean } &
  React.HTMLProps<HTMLButtonElement>;

const FormSubmit = React.forwardRef<
  React.ComponentRef<typeof FormPrimitive.Submit>,
  FormSubmitProps
>(({ className, ...props }, ref) => (
  <FormPrimitive.Submit
    ref={ref}
    className={clsx(
      'inline-flex h-10 items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white ring-offset-background transition-colors hover:bg-blue-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
      className
    )}
    {...props}
  />
))
FormSubmit.displayName = 'FormSubmit'

const FormValidityState = FormPrimitive.ValidityState

// Helper component for consistent form descriptions
interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  variant?: 'default' | 'muted'
}

const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  FormDescriptionProps
>(({ className, variant = 'default', ...props }, ref) => (
  <p
    ref={ref}
    className={clsx(
      'text-sm',
      variant === 'default' && 'text-gray-600',
      variant === 'muted' && 'text-gray-400',
      className
    )}
    {...props}
  />
))
FormDescription.displayName = 'FormDescription'

// Type exports
export type {
  FormFieldProps,
  FormLabelProps,
  FormControlProps,
  FormMessageProps,
  FormSubmitProps,
  FormDescriptionProps,
}

// Component exports
export {
  Form,
  FormField,
  FormLabel,
  FormControl,
  FormMessage,
  FormSubmit,
  FormValidityState,
  FormDescription,
}