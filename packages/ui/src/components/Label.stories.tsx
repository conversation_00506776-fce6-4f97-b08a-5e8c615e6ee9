import type { Meta, StoryObj } from '@storybook/react-vite'
import { Label } from './Label'

const meta: Meta<typeof Label> = {
  title: 'Components/Label',
  component: Label,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'required'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'default', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Default Label',
    variant: 'default',
    size: 'default',
  },
}

export const Required: Story = {
  args: {
    children: 'Required Field',
    variant: 'required',
    size: 'default',
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <Label size="sm">Small Label</Label>
      </div>
      <div>
        <Label size="default">Default Label</Label>
      </div>
      <div>
        <Label size="lg">Large Label</Label>
      </div>
    </div>
  ),
}

export const FormExample: Story = {
  render: () => (
    <form className="space-y-4 p-6 border rounded-lg max-w-md">
      <div className="space-y-2">
        <Label htmlFor="name" variant="required">
          Full Name
        </Label>
        <input
          id="name"
          type="text"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter your full name"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="email" variant="required">
          Email Address
        </Label>
        <input
          id="email"
          type="email"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter your email"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="phone">
          Phone Number
        </Label>
        <input
          id="phone"
          type="tel"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Optional phone number"
        />
      </div>
    </form>
  ),
}