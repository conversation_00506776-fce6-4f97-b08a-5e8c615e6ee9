import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { ToggleGroup, ToggleMultiGroup, ToggleGroupItem } from './ToggleGroup'

const meta: Meta<typeof ToggleGroup> = {
  title: 'Components/ToggleGroup',
  component: ToggleGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'outline', 'ghost'],
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'sm', 'lg'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    defaultValue: 'center',
  },
  render: (args) => (
    <ToggleGroup {...args}>
      <ToggleGroupItem value="left">Left</ToggleGroupItem>
      <ToggleGroupItem value="center">Center</ToggleGroupItem>
      <ToggleGroupItem value="right">Right</ToggleGroupItem>
    </ToggleGroup>
  ),
}

export const Multiple: Story = {
  render: () => (
    <ToggleMultiGroup defaultValue={['bold']}>
      <ToggleGroupItem value="bold" aria-label="Bold">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M14 12a4 4 0 0 0 0-8H6v16h8a4 4 0 0 0 0-8z" />
          <path d="M6 12h8" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="italic" aria-label="Italic">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="19" x2="10" y1="4" y2="4" />
          <line x1="14" x2="5" y1="20" y2="20" />
          <line x1="15" x2="9" y1="4" y2="20" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="underline" aria-label="Underline">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <path d="M6 4v6a6 6 0 0 0 12 0V4" />
          <line x1="4" x2="20" y1="20" y2="20" />
        </svg>
      </ToggleGroupItem>
    </ToggleMultiGroup>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ToggleGroup defaultValue="center">
          <ToggleGroupItem value="left">Left</ToggleGroupItem>
          <ToggleGroupItem value="center">Center</ToggleGroupItem>
          <ToggleGroupItem value="right">Right</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Outline</h4>
        <ToggleGroup variant="outline" defaultValue="center">
          <ToggleGroupItem value="left" variant="outline">
            Left
          </ToggleGroupItem>
          <ToggleGroupItem value="center" variant="outline">
            Center
          </ToggleGroupItem>
          <ToggleGroupItem value="right" variant="outline">
            Right
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Ghost</h4>
        <ToggleGroup variant="ghost" defaultValue="center">
          <ToggleGroupItem value="left" variant="ghost">
            Left
          </ToggleGroupItem>
          <ToggleGroupItem value="center" variant="ghost">
            Center
          </ToggleGroupItem>
          <ToggleGroupItem value="right" variant="ghost">
            Right
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Small</h4>
        <ToggleGroup size="sm" defaultValue="center">
          <ToggleGroupItem value="left" size="sm">
            Left
          </ToggleGroupItem>
          <ToggleGroupItem value="center" size="sm">
            Center
          </ToggleGroupItem>
          <ToggleGroupItem value="right" size="sm">
            Right
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ToggleGroup defaultValue="center">
          <ToggleGroupItem value="left">Left</ToggleGroupItem>
          <ToggleGroupItem value="center">Center</ToggleGroupItem>
          <ToggleGroupItem value="right">Right</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Large</h4>
        <ToggleGroup size="lg" defaultValue="center">
          <ToggleGroupItem value="left" size="lg">
            Left
          </ToggleGroupItem>
          <ToggleGroupItem value="center" size="lg">
            Center
          </ToggleGroupItem>
          <ToggleGroupItem value="right" size="lg">
            Right
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  ),
}

export const TextAlignment: Story = {
  render: () => (
    <ToggleGroup variant="outline" defaultValue="left">
      <ToggleGroupItem value="left" variant="outline" aria-label="Align left">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="21" x2="3" y1="6" y2="6" />
          <line x1="15" x2="3" y1="12" y2="12" />
          <line x1="17" x2="3" y1="18" y2="18" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="center" variant="outline" aria-label="Align center">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="18" x2="6" y1="6" y2="6" />
          <line x1="21" x2="3" y1="12" y2="12" />
          <line x1="16" x2="8" y1="18" y2="18" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="right" variant="outline" aria-label="Align right">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="21" x2="3" y1="6" y2="6" />
          <line x1="21" x2="9" y1="12" y2="12" />
          <line x1="21" x2="7" y1="18" y2="18" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="justify" variant="outline" aria-label="Justify">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="21" x2="3" y1="6" y2="6" />
          <line x1="21" x2="3" y1="12" y2="12" />
          <line x1="21" x2="3" y1="18" y2="18" />
        </svg>
      </ToggleGroupItem>
    </ToggleGroup>
  ),
}

export const ViewModes: Story = {
  render: () => (
    <ToggleGroup variant="outline" defaultValue="grid">
      <ToggleGroupItem value="list" variant="outline" aria-label="List view">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <line x1="8" x2="21" y1="6" y2="6" />
          <line x1="8" x2="21" y1="12" y2="12" />
          <line x1="8" x2="21" y1="18" y2="18" />
          <line x1="3" x2="3.01" y1="6" y2="6" />
          <line x1="3" x2="3.01" y1="12" y2="12" />
          <line x1="3" x2="3.01" y1="18" y2="18" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="grid" variant="outline" aria-label="Grid view">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <rect width="7" height="7" x="3" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="14" rx="1" />
          <rect width="7" height="7" x="3" y="14" rx="1" />
        </svg>
      </ToggleGroupItem>
      <ToggleGroupItem value="card" variant="outline" aria-label="Card view">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          className="h-4 w-4"
        >
          <rect width="18" height="18" x="3" y="3" rx="2" />
          <path d="M9 9h6v6H9z" />
        </svg>
      </ToggleGroupItem>
    </ToggleGroup>
  ),
}

export const Filtering: Story = {
  render: () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">Filter by category:</h4>
      <ToggleMultiGroup defaultValue={['all']}>
        <ToggleGroupItem value="all">All</ToggleGroupItem>
        <ToggleGroupItem value="documents">Documents</ToggleGroupItem>
        <ToggleGroupItem value="images">Images</ToggleGroupItem>
        <ToggleGroupItem value="videos">Videos</ToggleGroupItem>
        <ToggleGroupItem value="audio">Audio</ToggleGroupItem>
      </ToggleMultiGroup>
    </div>
  ),
}

export const States: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h4 className="mb-2 text-sm font-medium">Default</h4>
        <ToggleGroup>
          <ToggleGroupItem value="option1">Option 1</ToggleGroupItem>
          <ToggleGroupItem value="option2">Option 2</ToggleGroupItem>
          <ToggleGroupItem value="option3">Option 3</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Disabled</h4>
        <ToggleGroup disabled>
          <ToggleGroupItem value="option1">Option 1</ToggleGroupItem>
          <ToggleGroupItem value="option2">Option 2</ToggleGroupItem>
          <ToggleGroupItem value="option3">Option 3</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div>
        <h4 className="mb-2 text-sm font-medium">Individual disabled items</h4>
        <ToggleGroup defaultValue="option1">
          <ToggleGroupItem value="option1">Option 1</ToggleGroupItem>
          <ToggleGroupItem value="option2" disabled>
            Option 2 (Disabled)
          </ToggleGroupItem>
          <ToggleGroupItem value="option3">Option 3</ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  ),
}

export const MediaPlayer: Story = {
  render: () => (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">Media Controls:</h4>
      <ToggleMultiGroup variant="outline" defaultValue={['shuffle']}>
        <ToggleGroupItem value="shuffle" variant="outline" aria-label="Shuffle">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="h-4 w-4"
          >
            <polyline points="16,3 21,3 21,8" />
            <line x1="4" x2="21" y1="20" y2="3" />
            <polyline points="21,16 21,21 16,21" />
            <line x1="15" x2="21" y1="15" y2="21" />
            <line x1="4" x2="9" y1="4" y2="9" />
          </svg>
        </ToggleGroupItem>
        <ToggleGroupItem value="repeat" variant="outline" aria-label="Repeat">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="h-4 w-4"
          >
            <path d="m17 2 4 4-4 4" />
            <path d="M3 11v-1a4 4 0 0 1 4-4h14" />
            <path d="m7 22-4-4 4-4" />
            <path d="M21 13v1a4 4 0 0 1-4 4H3" />
          </svg>
        </ToggleGroupItem>
        <ToggleGroupItem value="favorites" variant="outline" aria-label="Favorites only">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="h-4 w-4"
          >
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" />
          </svg>
        </ToggleGroupItem>
      </ToggleMultiGroup>
    </div>
  ),
}