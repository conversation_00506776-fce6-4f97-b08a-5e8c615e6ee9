import * as React from 'react'
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const radioGroupVariants = cva(
  'grid gap-2',
  {
    variants: {
      orientation: {
        vertical: 'grid-cols-1',
        horizontal: 'grid-flow-col auto-cols-max gap-6',
      },
      size: {
        sm: 'gap-1',
        default: 'gap-2',
        lg: 'gap-3',
      },
    },
    defaultVariants: {
      orientation: 'vertical',
      size: 'default',
    },
  }
)

const radioItemVariants = cva(
  'aspect-square rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      size: {
        sm: 'h-3 w-3',
        default: 'h-4 w-4',
        lg: 'h-5 w-5',
      },
      variant: {
        default: 'border-gray-400 text-blue-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600',
        error: 'border-red-400 text-red-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600',
      },
    },
    defaultVariants: {
      size: 'default',
      variant: 'default',
    },
  }
)

interface RadioGroupProps
  extends Omit<React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>, 'orientation'>,
    VariantProps<typeof radioGroupVariants> {}

const RadioGroup = React.forwardRef<
  React.ComponentRef<typeof RadioGroupPrimitive.Root>,
  RadioGroupProps
>(({ className, orientation, size, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={clsx(radioGroupVariants({ orientation, size, className }))}
      {...props}
      ref={ref}
    />
  )
})
RadioGroup.displayName = 'RadioGroup'

interface RadioGroupItemProps
  extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>,
    VariantProps<typeof radioItemVariants> {}

const RadioGroupItem = React.forwardRef<
  React.ComponentRef<typeof RadioGroupPrimitive.Item>,
  RadioGroupItemProps
>(({ className, size, variant, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={clsx(radioItemVariants({ size, variant, className }))}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <svg
          width="6"
          height="6"
          viewBox="0 0 6 6"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className={clsx(
            'fill-current',
            size === 'sm' && 'w-1.5 h-1.5',
            size === 'default' && 'w-2 h-2',
            size === 'lg' && 'w-2.5 h-2.5'
          )}
        >
          <circle cx="3" cy="3" r="3" fill="white" />
        </svg>
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  )
})
RadioGroupItem.displayName = 'RadioGroupItem'

// Helper component for radio with label
interface RadioGroupOptionProps
  extends Omit<RadioGroupItemProps, 'children'> {
  label?: string
  description?: string
  children?: React.ReactNode
}

const RadioGroupOption = React.forwardRef<
  React.ComponentRef<typeof RadioGroupPrimitive.Item>,
  RadioGroupOptionProps
>(({ label, description, children, className, size, variant, ...props }, ref) => {
  return (
    <div className="flex items-start space-x-2">
      <RadioGroupItem
        ref={ref}
        size={size}
        variant={variant}
        className={clsx('mt-0.5', className)}
        {...props}
      />
      <div className="grid gap-1.5 leading-none">
        {label && (
          <label
            htmlFor={props.id}
            className={clsx(
              'font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
              size === 'sm' && 'text-xs',
              size === 'default' && 'text-sm',
              size === 'lg' && 'text-base'
            )}
          >
            {label}
          </label>
        )}
        {description && (
          <p className={clsx(
            'text-gray-600',
            size === 'sm' && 'text-xs',
            size === 'default' && 'text-xs',
            size === 'lg' && 'text-sm'
          )}>
            {description}
          </p>
        )}
        {children}
      </div>
    </div>
  )
})
RadioGroupOption.displayName = 'RadioGroupOption'

// Type exports
export type { RadioGroupProps, RadioGroupItemProps, RadioGroupOptionProps }

// Component exports
export {
  RadioGroup,
  RadioGroupItem,
  RadioGroupOption,
}

