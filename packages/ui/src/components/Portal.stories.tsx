import type { <PERSON>a, StoryObj } from '@storybook/react-vite'
import { Portal } from './Portal'
import { useState } from 'react'
import { Button } from './Button'

const meta: Meta<typeof Portal> = {
  title: 'Utilities/Portal',
  component: Portal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    asChild: {
      control: { type: 'boolean' },
    },
    container: {
      control: false,
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: (
      <div className="fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg">
        This content is portaled to document.body
      </div>
    ),
  },
}

export const CustomContainer: Story = {
  render: () => {
    const [container, setContainer] = useState<HTMLElement | null>(null)
    
    return (
      <div className="space-y-4">
        <div 
          ref={setContainer}
          className="border-2 border-dashed border-gray-300 p-4 min-h-32 relative"
        >
          <p className="text-gray-600">Custom container</p>
        </div>
        
        {container && (
          <Portal container={container}>
            <div className="absolute top-2 right-2 bg-green-600 text-white px-3 py-1 rounded text-sm">
              Portaled here!
            </div>
          </Portal>
        )}
      </div>
    )
  },
}

export const InteractiveModal: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <div>
        <Button onClick={() => setIsOpen(true)}>
          Open Modal
        </Button>
        
        {isOpen && (
          <Portal>
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                <h2 className="text-xl font-semibold mb-4">Portal Modal</h2>
                <p className="text-gray-600 mb-4">
                  This modal is rendered using a Portal, so it appears above all other content.
                </p>
                <div className="flex gap-2 justify-end">
                  <Button variant="ghost" onClick={() => setIsOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsOpen(false)}>
                    Confirm
                  </Button>
                </div>
              </div>
            </div>
          </Portal>
        )}
      </div>
    )
  },
  parameters: {
    layout: 'padded',
  },
}

export const Tooltip: Story = {
  render: () => {
    const [showTooltip, setShowTooltip] = useState(false)
    
    return (
      <div className="relative inline-block">
        <Button
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          Hover me
        </Button>
        
        {showTooltip && (
          <Portal>
            <div className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-3 py-2 rounded text-sm whitespace-nowrap">
              This tooltip is portaled
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </Portal>
        )}
      </div>
    )
  },
}

export const MultiplePortals: Story = {
  render: () => (
    <div>
      <Portal>
        <div className="fixed top-4 left-4 bg-red-600 text-white p-3 rounded">
          Portal 1
        </div>
      </Portal>
      
      <Portal>
        <div className="fixed top-4 right-4 bg-blue-600 text-white p-3 rounded">
          Portal 2
        </div>
      </Portal>
      
      <Portal>
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-green-600 text-white p-3 rounded">
          Portal 3
        </div>
      </Portal>
      
      <div className="text-center text-gray-600">
        Multiple portals can coexist
      </div>
    </div>
  ),
}

export const AsChild: Story = {
  render: () => (
    <Portal asChild>
      <button className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700">
        I'm a button rendered as Portal child
      </button>
    </Portal>
  ),
}