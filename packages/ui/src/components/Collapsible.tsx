import * as React from 'react'
import * as CollapsiblePrimitive from '@radix-ui/react-collapsible'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

const collapsibleVariants = cva(
  '',
  {
    variants: {
      variant: {
        default: '',
        card: 'border border-gray-200 rounded-lg bg-white shadow-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

const collapsibleTriggerVariants = cva(
  'flex w-full items-center justify-between py-4 text-left font-medium transition-all cursor-pointer hover:underline [&[data-state=open]>svg]:rotate-180',
  {
    variants: {
      variant: {
        default: '',
        card: 'px-6',
      },
      size: {
        sm: 'py-2 text-sm',
        default: 'py-4 text-base',
        lg: 'py-6 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const collapsibleContentVariants = cva(
  'overflow-hidden text-sm data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down',
  {
    variants: {
      variant: {
        default: '',
        card: 'px-6',
      },
      size: {
        sm: 'text-xs',
        default: 'text-sm',
        lg: 'text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

interface CollapsibleProps
  extends React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>,
    VariantProps<typeof collapsibleVariants> {}

const Collapsible = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.Root>,
  CollapsibleProps
>(({ className, variant, ...props }, ref) => (
  <CollapsiblePrimitive.Root
    ref={ref}
    className={clsx(collapsibleVariants({ variant, className }))}
    {...props}
  />
))
Collapsible.displayName = CollapsiblePrimitive.Root.displayName

interface CollapsibleTriggerProps
  extends React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>,
    VariantProps<typeof collapsibleTriggerVariants> {}

const CollapsibleTrigger = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.Trigger>,
  CollapsibleTriggerProps
>(({ className, variant, size, children, ...props }, ref) => (
  <CollapsiblePrimitive.Trigger
    ref={ref}
    className={clsx(collapsibleTriggerVariants({ variant, size, className }))}
    {...props}
  >
    {children}
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-4 w-4 shrink-0 text-gray-500 transition-transform duration-200"
    >
      <path
        d="m4.5 6 3 3 3-3"
        stroke="currentColor"
        strokeWidth="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </CollapsiblePrimitive.Trigger>
))
CollapsibleTrigger.displayName = CollapsiblePrimitive.Trigger.displayName

interface CollapsibleContentProps
  extends React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>,
    VariantProps<typeof collapsibleContentVariants> {}

const CollapsibleContent = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.Content>,
  CollapsibleContentProps
>(({ className, variant, size, children, ...props }, ref) => (
  <CollapsiblePrimitive.Content
    ref={ref}
    className={clsx(collapsibleContentVariants({ variant, size, className }))}
    {...props}
  >
    <div className={clsx('pb-4 pt-0', variant === 'card' && 'pb-6')}>{children}</div>
  </CollapsiblePrimitive.Content>
))
CollapsibleContent.displayName = CollapsiblePrimitive.Content.displayName

export type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps }
export { Collapsible, CollapsibleTrigger, CollapsibleContent }