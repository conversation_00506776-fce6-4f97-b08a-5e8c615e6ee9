import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Spinner } from './Spinner'

const meta: Meta<typeof Spinner> = {
  title: 'Components/Spinner',
  component: Spinner,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the spinner',
    },
    asChild: {
      control: { type: 'boolean' },
      description: 'Render as child component',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    size: 'md',
  },
}

export const Small: Story = {
  args: {
    size: 'sm',
  },
}

export const Medium: Story = {
  args: {
    size: 'md',
  },
}

export const Large: Story = {
  args: {
    size: 'lg',
  },
}

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
  },
}

export const CustomColor: Story = {
  args: {
    size: 'md',
    className: 'text-blue-500',
  },
}

export const MultipleColors: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Spinner size="md" className="text-blue-500" />
      <Spinner size="md" className="text-green-500" />
      <Spinner size="md" className="text-red-500" />
      <Spinner size="md" className="text-purple-500" />
      <Spinner size="md" className="text-orange-500" />
    </div>
  ),
}

export const SizeComparison: Story = {
  render: () => (
    <div className="flex items-center gap-6">
      <div className="text-center">
        <Spinner size="sm" />
        <p className="mt-2 text-xs text-gray-600">Small</p>
      </div>
      <div className="text-center">
        <Spinner size="md" />
        <p className="mt-2 text-xs text-gray-600">Medium</p>
      </div>
      <div className="text-center">
        <Spinner size="lg" />
        <p className="mt-2 text-xs text-gray-600">Large</p>
      </div>
      <div className="text-center">
        <Spinner size="xl" />
        <p className="mt-2 text-xs text-gray-600">Extra Large</p>
      </div>
    </div>
  ),
}

export const InButton: Story = {
  render: () => (
    <div className="flex gap-4">
      <button className="inline-flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50" disabled>
        <Spinner size="sm" />
        Loading...
      </button>
      <button className="inline-flex items-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50 disabled:opacity-50" disabled>
        <Spinner size="sm" className="text-gray-500" />
        Processing
      </button>
    </div>
  ),
}

export const WithCustomWrapper: Story = {
  render: () => (
    <div className="flex flex-col items-center gap-4 rounded-lg border p-6">
      <Spinner size="lg" className="text-blue-500" />
      <div className="text-center">
        <h3 className="font-semibold">Loading your content</h3>
        <p className="text-sm text-gray-600">Please wait while we fetch your data...</p>
      </div>
    </div>
  ),
}