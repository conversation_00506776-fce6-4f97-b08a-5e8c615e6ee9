openapi: 3.0.3
info:
  title: FinPro API
  description: |
    FinPro is a comprehensive financial planning and portfolio management platform.
    This API provides secure access to financial data, user authentication, and 
    portfolio management functionality for clients, advisors, and administrators.
    
    ## Authentication
    The API uses JWT (JSON Web Token) authentication with role-based access control (RBAC).
    Include the JWT token in the Authorization header: `Bearer <token>`
    
    ## User Roles
    - **CLIENT**: Individual users managing personal financial data
    - **ADVISOR**: Financial advisors managing multiple client accounts  
    - **ADMINISTRATOR**: System administrators with full access
    
    ## Rate Limiting
    - Authentication endpoints: 5 requests per minute per IP
    - General API endpoints: 100 requests per minute per user
    
    ## Security
    - All requests must use HTTPS (TLS 1.2+)
    - Passwords are hashed using bcrypt with salt
    - JWT tokens have 15-minute expiration with refresh token support
    - Email verification required for account activation
  version: "1.0.0"
  contact:
    name: FinPro API Support
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://finpro.com/license

servers:
  - url: https://api.finpro.com/api/v1
    description: Production server
  - url: https://staging-api.finpro.com/api/v1
    description: Staging server
  - url: http://localhost:8000/api/v1
    description: Development server

security:
  - bearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check endpoint
      description: Returns the current health status of the API
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  message:
                    type: string
                    example: "API is running"
                  timestamp:
                    type: string
                    format: date-time

  # Authentication Endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register new user account
      description: |
        Create a new user account with email and password.
        Email verification is required before the account can be used.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistration'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegistrationResponse'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '409':
          description: Email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConflictError'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RateLimitError'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Authenticate user with email/password
      description: |
        Authenticate user credentials and return JWT access and refresh tokens.
        Account must be email verified before login is allowed.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'
        '403':
          description: Account not verified
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnverifiedAccountError'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RateLimitError'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user and invalidate tokens
      description: |
        Logout the current user and invalidate all active tokens.
        The JWT token will be added to the blacklist.
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogoutResponse'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token using refresh token
      description: |
        Exchange a valid refresh token for a new access token.
        The old access token will be invalidated.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/me:
    get:
      tags:
        - Authentication
      summary: Get current authenticated user info
      description: |
        Retrieve information about the currently authenticated user.
        Includes user profile, role, and permissions.
      responses:
        '200':
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CurrentUser'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'

  /auth/verify-email:
    post:
      tags:
        - Authentication
      summary: Verify email with token
      description: |
        Verify user email address using the verification token sent via email.
        This activates the user account for login.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailVerificationRequest'
      responses:
        '200':
          description: Email verified successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationResponse'
        '400':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/resend-verification:
    post:
      tags:
        - Authentication
      summary: Resend email verification
      description: |
        Resend email verification to the specified email address.
        Limited to 3 attempts per hour per email address.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResendVerificationRequest'
      responses:
        '200':
          description: Verification email sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResendVerificationResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RateLimitError'

  /auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Initiate password reset
      description: |
        Send password reset email to the specified email address.
        Reset token expires after 1 hour for security.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset email sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPasswordResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RateLimitError'

  /auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Complete password reset with token
      description: |
        Reset user password using the reset token sent via email.
        Token is single-use and expires after 1 hour.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password reset successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetPasswordResponse'
        '400':
          description: Invalid or expired reset token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/change-password:
    post:
      tags:
        - Authentication
      summary: Change password for authenticated user
      description: |
        Change password for the currently authenticated user.
        Requires current password for security verification.
        All existing tokens will be invalidated after password change.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangePasswordResponse'
        '400':
          description: Current password incorrect or validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'

  # User Management Endpoints
  /users:
    get:
      tags:
        - Users
      summary: List users (Admin only)
      description: |
        Retrieve a paginated list of all users in the system.
        Only accessible by administrators.
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: role
          in: query
          description: Filter by user role
          required: false
          schema:
            $ref: '#/components/schemas/UserRole'
        - name: status
          in: query
          description: Filter by user status
          required: false
          schema:
            $ref: '#/components/schemas/UserStatus'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserList'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForbiddenError'

    post:
      tags:
        - Users
      summary: Create user (Admin only)
      description: |
        Create a new user account (admin function).
        Email verification will be sent automatically.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForbiddenError'
        '409':
          description: Email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConflictError'

  /users/{user_id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: |
        Retrieve user information by ID.
        Users can only access their own data unless they are administrators.
      parameters:
        - name: user_id
          in: path
          required: true
          description: User ID (UUID)
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForbiddenError'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundError'

    put:
      tags:
        - Users
      summary: Update user
      description: |
        Update user information.
        Users can only update their own data unless they are administrators.
      parameters:
        - name: user_id
          in: path
          required: true
          description: User ID (UUID)
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForbiddenError'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundError'

    delete:
      tags:
        - Users
      summary: Delete user (Admin only)
      description: |
        Soft delete a user account (sets deleted flag).
        Only accessible by administrators.
        This action cannot be undone.
      parameters:
        - name: user_id
          in: path
          required: true
          description: User ID (UUID)
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: User deleted successfully
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedError'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForbiddenError'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT authentication. Include the token in the Authorization header:
        `Authorization: Bearer <token>`

  schemas:
    # User-related schemas
    UserRole:
      type: string
      enum: [CLIENT, ADVISOR, ADMINISTRATOR]
      description: User role in the system

    UserStatus:
      type: string
      enum: [ACTIVE, INACTIVE, SUSPENDED, PENDING_VERIFICATION]
      description: User account status

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique user identifier
        email:
          type: string
          format: email
          description: User email address
        username:
          type: string
          description: Unique username
        first_name:
          type: string
          description: User first name
        last_name:
          type: string
          description: User last name
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'
        email_verified:
          type: boolean
          description: Whether email has been verified
        email_verified_at:
          type: string
          format: date-time
          nullable: true
          description: When email was verified
        last_login_at:
          type: string
          format: date-time
          nullable: true
          description: Last login timestamp
        created_at:
          type: string
          format: date-time
          description: Account creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
      required:
        - id
        - email
        - username
        - role
        - status
        - email_verified
        - created_at
        - updated_at

    CurrentUser:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            permissions:
              type: array
              items:
                type: string
              description: List of user permissions

    UserList:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        pagination:
          $ref: '#/components/schemas/Pagination'
      required:
        - users
        - pagination

    Pagination:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          description: Current page number
        limit:
          type: integer
          minimum: 1
          description: Items per page
        total:
          type: integer
          minimum: 0
          description: Total number of items
        pages:
          type: integer
          minimum: 0
          description: Total number of pages
      required:
        - page
        - limit
        - total
        - pages

    # Authentication request/response schemas
    UserRegistration:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Valid email address
        password:
          type: string
          minLength: 8
          description: |
            Password must contain:
            - At least 8 characters
            - One uppercase letter
            - One lowercase letter  
            - One number
            - One special character
        first_name:
          type: string
          maxLength: 100
          description: User first name
        last_name:
          type: string
          maxLength: 100
          description: User last name
        role:
          $ref: '#/components/schemas/UserRole'
          default: CLIENT
      required:
        - email
        - password
      example:
        email: "<EMAIL>"
        password: "SecurePass123!"
        first_name: "John"
        last_name: "Doe"
        role: "CLIENT"

    RegistrationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Registration successful. Please check your email to verify your account."
        data:
          type: object
          properties:
            user_id:
              type: string
              format: uuid
            email:
              type: string
              format: email
            email_verified:
              type: boolean
              example: false
      required:
        - success
        - message
        - data

    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User email address
        password:
          type: string
          description: User password
        remember_me:
          type: boolean
          default: false
          description: Extended session duration
      required:
        - email
        - password
      example:
        email: "<EMAIL>"
        password: "SecurePass123!"
        remember_me: false

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Login successful"
        data:
          type: object
          properties:
            access_token:
              type: string
              description: JWT access token (15 min expiry)
            refresh_token:
              type: string
              description: JWT refresh token (7 day expiry)
            token_type:
              type: string
              example: "bearer"
            expires_in:
              type: integer
              example: 900
              description: Token expiry in seconds
            user:
              $ref: '#/components/schemas/CurrentUser'
      required:
        - success
        - message
        - data

    LogoutResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Logout successful"
      required:
        - success
        - message

    RefreshTokenRequest:
      type: object
      properties:
        refresh_token:
          type: string
          description: Valid refresh token
      required:
        - refresh_token
      example:
        refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    RefreshTokenResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Token refreshed successfully"
        data:
          type: object
          properties:
            access_token:
              type: string
              description: New JWT access token
            token_type:
              type: string
              example: "bearer"
            expires_in:
              type: integer
              example: 900
              description: Token expiry in seconds
      required:
        - success
        - message
        - data

    EmailVerificationRequest:
      type: object
      properties:
        token:
          type: string
          description: Email verification token
      required:
        - token
      example:
        token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    EmailVerificationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Email verified successfully"
        data:
          type: object
          properties:
            user_id:
              type: string
              format: uuid
            email_verified:
              type: boolean
              example: true
            verified_at:
              type: string
              format: date-time
      required:
        - success
        - message
        - data

    ResendVerificationRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address to resend verification
      required:
        - email
      example:
        email: "<EMAIL>"

    ResendVerificationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Verification email sent successfully"
      required:
        - success
        - message

    ForgotPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address for password reset
      required:
        - email
      example:
        email: "<EMAIL>"

    ForgotPasswordResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password reset email sent successfully"
      required:
        - success
        - message

    ResetPasswordRequest:
      type: object
      properties:
        token:
          type: string
          description: Password reset token from email
        password:
          type: string
          minLength: 8
          description: New password meeting complexity requirements
      required:
        - token
        - password
      example:
        token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        password: "NewSecurePass123!"

    ResetPasswordResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password reset successfully"
      required:
        - success
        - message

    ChangePasswordRequest:
      type: object
      properties:
        current_password:
          type: string
          description: Current password for verification
        new_password:
          type: string
          minLength: 8
          description: New password meeting complexity requirements
      required:
        - current_password
        - new_password
      example:
        current_password: "OldPassword123!"
        new_password: "NewSecurePass123!"

    ChangePasswordResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password changed successfully. Please log in again."
      required:
        - success
        - message

    CreateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Valid email address
        password:
          type: string
          minLength: 8
          description: Password meeting complexity requirements
        first_name:
          type: string
          maxLength: 100
          description: User first name
        last_name:
          type: string
          maxLength: 100
          description: User last name
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'
          default: PENDING_VERIFICATION
      required:
        - email
        - password
        - role

    UpdateUserRequest:
      type: object
      properties:
        first_name:
          type: string
          maxLength: 100
          description: User first name
        last_name:
          type: string
          maxLength: 100
          description: User last name
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'

    # Error schemas
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          description: Error code
        message:
          type: string
          description: Human-readable error message
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        request_id:
          type: string
          description: Unique request identifier for debugging
      required:
        - success
        - error
        - message
        - timestamp

    ValidationError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "VALIDATION_ERROR"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field that failed validation
                  message:
                    type: string
                    description: Validation error message
                required:
                  - field
                  - message
              example:
                - field: "password"
                  message: "Password must contain at least one uppercase letter"

    AuthError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "INVALID_CREDENTIALS"
            message:
              type: string
              example: "Invalid email or password"

    UnauthorizedError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "UNAUTHORIZED"
            message:
              type: string
              example: "Invalid or expired authentication token"

    ForbiddenError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "FORBIDDEN"
            message:
              type: string
              example: "Insufficient permissions to access this resource"

    NotFoundError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "NOT_FOUND"
            message:
              type: string
              example: "The requested resource was not found"

    ConflictError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "EMAIL_EXISTS"
            message:
              type: string
              example: "An account with this email already exists"

    RateLimitError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "RATE_LIMIT_EXCEEDED"
            message:
              type: string
              example: "Too many requests. Please try again later."

    UnverifiedAccountError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error:
              type: string
              example: "ACCOUNT_NOT_VERIFIED"
            message:
              type: string
              example: "Please verify your email address before logging in"

tags:
  - name: Health
    description: API health and status endpoints
  - name: Authentication
    description: User authentication and authorization endpoints
  - name: Users
    description: User management endpoints (admin functions)