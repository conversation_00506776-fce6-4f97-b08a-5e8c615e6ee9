# FinPro Backend Service

A Python backend service implementing Domain-Driven Design (DDD) principles for comprehensive financial portfolio management.

## Overview

This backend service provides CRUD operations for managing households, users, portfolios, assets, liabilities, income, expenses, and insurance policies. Built with FastAPI and PostgreSQL, it follows strict DDD principles with clear separation between domain logic, application services, and infrastructure concerns.

## Architecture

The project follows Clean Architecture and Domain-Driven Design principles:

- **Domain Layer**: Pure business logic with no external dependencies
- **Application Layer**: Use cases and application services
- **Infrastructure Layer**: Database, external services, and technical implementations  
- **API Layer**: REST endpoints and request/response handling

See [finpro_backend.md](finpro_backend.md) for detailed architecture documentation.

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL 15+ (via Docker)
- Redis (via Docker)

### Local Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd finpro_backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # For development
```

4. Copy environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Start services with Docker:
```bash
docker-compose up -d
```

6. Run database migrations:
```bash
alembic upgrade head
```

7. Start the development server:
```bash
uvicorn src.api.main:app --reload
```

The API will be available at `http://localhost:8000` with documentation at `http://localhost:8000/docs`.

## Project Structure

```
finpro-backend/
├── src/                    # Source code
│   ├── domain/            # Domain layer (entities, value objects, services)
│   ├── application/       # Application layer (use cases, DTOs)
│   ├── infrastructure/    # Infrastructure layer (database, external services)
│   ├── api/              # API layer (endpoints, schemas, middleware)
│   └── core/             # Shared kernel (exceptions, types, constants)
├── tests/                 # Test suite
├── scripts/              # Utility scripts
├── docs/                 # Documentation
└── docker-compose.yml    # Local development environment
```

## Key Features

### Domain Entities
- **Households**: Multi-user family financial management
- **Users**: Authentication, roles, and permissions
- **Portfolios**: Financial data containers with ownership
- **Assets**: Bank accounts, investments, real estate, vehicles, etc.
- **Liabilities**: Mortgages, loans, credit cards, etc.
- **Income**: Employment, business, investment income
- **Expenses**: Categorized expense tracking and budgets
- **Insurance**: Life, disability, health, property policies

### Security Features
- JWT-based authentication with refresh tokens
- Role-Based Access Control (RBAC)
- Field-level encryption for sensitive data
- Comprehensive audit logging
- API rate limiting

### Technical Features
- Async/await throughout with FastAPI
- CQRS pattern for commands and queries
- Domain events for decoupling
- Repository pattern for data access
- Unit of Work for transactions

## Development

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test category
pytest tests/unit
pytest tests/integration
pytest tests/e2e
```

### Code Quality
```bash
# Format code
black src tests

# Sort imports
isort src tests

# Lint code
flake8 src tests

# Type checking
mypy src
```

### Database Migrations
```bash
# Create a new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback one migration
alembic downgrade -1
```

## API Documentation

Once running, the API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
- OpenAPI JSON: `http://localhost:8000/openapi.json`

## Implementation Status

See [tasks.md](tasks.md) for the complete implementation task list and progress tracking.

## Contributing

1. Follow the established architecture patterns
2. Write tests for all new code
3. Ensure all tests pass and coverage remains high
4. Follow the code style guidelines
5. Update documentation as needed

## License

[License details to be added]