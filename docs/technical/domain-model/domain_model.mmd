erDiagram
    %% Core Tables
    organizations ||--o{ teams : "has"
    teams ||--o{ team_advisors : "has"
    users ||--o{ team_advisors : "advisor"
    users ||--o{ organizations : "contact_advisor"
    households ||--o{ household_advisors : "has"
    team_advisors ||--o{ household_advisors : "assigned_to"
    households ||--o{ household_members : "has"
    households ||--o{ portfolios : "owns"
    users ||--o{ household_members : "belongs to"
    
    %% Financial Entities
    portfolios ||--o{ assets : "contains"
    portfolios ||--o{ liabilities : "contains"
    portfolios ||--o{ incomes : "contains"
    portfolios ||--o{ expense_items : "contains"
    portfolios ||--o{ insurance_policies : "contains"
    
    %% Asset Subtypes
    assets ||--o| asset_bank_accounts : "details"
    assets ||--o| asset_investment_accounts : "details"
    assets ||--o| asset_retirement_accounts : "details"
    assets ||--o| asset_real_estate : "details"
    assets ||--o| asset_vehicles : "details"
    assets ||--o| asset_personal_property : "details"
    assets ||--o| asset_business_interests : "details"
    assets ||--o| asset_other : "details"
    assets ||--o{ asset_valuations : "has"
    
    %% Liability Subtypes
    liabilities ||--o| liability_mortgages : "details"
    liabilities ||--o| liability_credit_cards : "details"
    liabilities ||--o| liability_student_loans : "details"
    liabilities ||--o| liability_vehicle_loans : "details"
    liabilities ||--o| liability_personal_loans : "details"
    liabilities ||--o| liability_business_loans : "details"
    liabilities ||--o| liability_taxes : "details"
    liabilities ||--o| liability_other : "details"
    liabilities ||--o{ liability_payments : "has"
    liabilities ||--o{ liability_rate_changes : "has"
    
    %% Income Subtypes
    incomes ||--o| income_employment : "details"
    incomes ||--o| income_business : "details"
    incomes ||--o| income_investment : "details"
    incomes ||--o| income_rental : "details"
    incomes ||--o| income_retirement : "details"
    incomes ||--o| income_support : "details"
    incomes ||--o| income_other : "details"
    incomes ||--o{ income_paystubs : "has"
    incomes ||--o{ income_tax_forms : "has"
    incomes ||--o{ income_projections : "has"
    income_paystubs ||--o{ income_paystub_deductions : "has"
    
    %% Expense Subtypes
    expense_items ||--o| expense_housing : "details"
    expense_items ||--o| expense_transportation : "details"
    expense_items ||--o| expense_healthcare : "details"
    expense_items ||--o| expense_insurance : "details"
    expense_items ||--o| expense_utilities : "details"
    expense_items ||--o| expense_education : "details"
    expense_items ||--o| expense_childcare : "details"
    expense_items ||--o| expense_debt_payments : "details"
    expense_items ||--o| expense_savings_investments : "details"
    expense_items ||--o| expense_charitable : "details"
    expense_items ||--o| expense_other : "details"
    expense_items ||--o{ expense_transactions : "has"
    expense_items ||--o{ expense_budgets : "has"
    
    %% Insurance Subtypes and Related
    insurance_policies ||--o| insurance_life_policies : "details"
    insurance_policies ||--o| insurance_disability_policies : "details"
    insurance_policies ||--o| insurance_health_policies : "details"
    insurance_policies ||--o| insurance_property_policies : "details"
    insurance_policies ||--o| insurance_auto_policies : "details"
    insurance_policies ||--o{ insurance_policy_additional_insureds : "has"
    insurance_policies ||--o{ insurance_policy_beneficiaries : "has"
    insurance_policies ||--o{ insurance_policy_claims : "has"
    insurance_policies ||--o{ insurance_policy_renewals : "has"
    insurance_auto_policies ||--o{ insurance_auto_policy_drivers : "has"
    
    %% Associations
    assets }o--o{ liabilities : "asset_liability_associations"
    assets }o--o{ expense_items : "asset_expense_associations"
    assets }o--o{ incomes : "asset_income_associations"
    liabilities }o--o{ expense_items : "liability_expense_associations"
    insurance_policies }o--o{ assets : "insurance_coverage_associations"
    insurance_policies }o--o{ liabilities : "insurance_coverage_associations"
    insurance_policies }o--o{ users : "insurance_coverage_associations"
    
    %% Authentication Tables (Simplified)
    users ||--o{ auth_user_passwords : "has"
    users ||--o{ auth_password_reset_tokens : "has"
    users ||--o{ auth_email_verification_tokens : "has"
    
    %% Audit Tables
    users ||--o{ audit_log : "performs actions"
    portfolios ||--o{ portfolio_snapshots : "snapshots"
    users ||--o{ notification_history : "receives"
    users ||--o{ export_history : "exports data"
    
    %% Entity Dependencies
    assets }o--o{ expense_items : "entity_dependencies"
    liabilities }o--o{ expense_items : "entity_dependencies"
    assets }o--o{ incomes : "entity_dependencies"

    %% Table Definitions
    organizations {
        uuid id PK
        varchar name
        varchar description
        organization_status status
        varchar website_url
        uuid contact_advisor_id FK
        jsonb settings
        boolean is_active
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
    }
    
    teams {
        uuid id PK
        uuid organization_id FK
        varchar name
        varchar description
        team_status status
        jsonb settings
        boolean is_active
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
    }
    
    team_advisors {
        uuid id PK
        uuid team_id FK
        uuid advisor_user_id FK
        advisor_role role
        boolean is_lead_advisor
        timestamptz joined_at
        timestamptz left_at
        advisor_status status
        jsonb permissions
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
    }
    
    household_advisors {
        uuid id PK
        uuid household_id FK
        uuid team_advisor_id FK
        boolean is_primary_advisor
        timestamptz assigned_at
        timestamptz unassigned_at
        advisor_assignment_status status
        jsonb permissions
        text notes
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
    }
    
    households {
        uuid id PK
        varchar name
        varchar type
        text description
        boolean is_private
        jsonb settings
        timestamptz created_at
        uuid created_by_id FK
    }
    
    users {
        uuid id PK
        varchar email UK
        varchar first_name
        varchar last_name
        user_role role "CLIENT | ADVISOR | ADMINISTRATOR"
        boolean email_verified
        user_status status
        timestamptz last_login_at
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
    }
    
    portfolios {
        uuid id PK
        uuid household_id FK
        varchar name
        portfolio_ownership_type ownership_type
        decimal total_assets
        decimal total_liabilities
        decimal net_worth
        timestamptz created_at
    }
    
    assets {
        uuid id PK
        uuid portfolio_id FK
        asset_type asset_type
        varchar name
        decimal current_value_usd
        varchar currency_code
        boolean is_active
        timestamptz created_at
    }
    
    liabilities {
        uuid id PK
        uuid portfolio_id FK
        liability_type liability_type
        varchar name
        decimal current_balance_usd
        varchar currency_code
        decimal interest_rate
        timestamptz created_at
    }
    
    incomes {
        uuid id PK
        uuid portfolio_id FK
        income_type income_type
        varchar name
        decimal gross_amount_usd
        varchar currency_code
        income_frequency frequency
        timestamptz created_at
    }
    
    expense_items {
        uuid id PK
        uuid portfolio_id FK
        expense_type expense_type
        varchar name
        decimal amount
        expense_frequency frequency
        timestamptz created_at
    }
    
    insurance_policies {
        uuid id PK
        uuid portfolio_id FK
        insurance_type insurance_type
        varchar policy_number
        decimal coverage_amount
        decimal premium_amount
        timestamptz created_at
    }
    
    household_members {
        uuid id PK
        uuid household_id FK
        uuid user_id FK
        household_role role
        household_relationship relationship
        household_member_status status
        boolean can_view
        boolean can_edit
        boolean can_delete
        boolean can_manage_members
        boolean can_manage_advisors
        jsonb custom_permissions
        boolean is_primary
        timestamptz joined_at
        uuid invited_by_id FK
        timestamptz invitation_sent_at
        timestamptz invitation_accepted_at
        text notes
        timestamptz created_at
        uuid created_by_id FK
        timestamptz updated_at
        uuid updated_by_id FK
        boolean is_deleted
        timestamptz deleted_at
        uuid deleted_by_id FK
    }
    
    asset_bank_accounts {
        uuid asset_id PK
        bank_account_type account_type
        account_status account_status
        varchar routing_number
        boolean is_checking
        boolean is_savings
        decimal interest_rate
        payment_frequency interest_frequency
        decimal minimum_balance
        decimal monthly_fee
        boolean overdraft_protection
        boolean has_debit_card
        boolean has_checks
        boolean online_banking_enabled
        timestamptz created_at
        timestamptz updated_at
    }
    
    asset_investment_accounts {
        uuid asset_id PK
        investment_account_type account_type
        account_status account_status
        varchar brokerage_name
        varchar account_number
        integer total_holdings
        decimal cash_balance
        decimal margin_balance
        decimal cost_basis
        decimal unrealized_gain_loss
        decimal ytd_return
        boolean margin_enabled
        boolean options_enabled
        varchar risk_tolerance
        varchar investment_objective
        timestamptz created_at
        timestamptz updated_at
    }
    
    asset_retirement_accounts {
        uuid asset_id PK
        retirement_account_type account_type
        account_status account_status
        varchar employer_name
        varchar plan_name
        decimal vested_amount
        decimal employee_contribution_rate
        decimal employer_match_rate
        decimal annual_contribution_limit
        boolean has_loan
        decimal loan_balance
        boolean accepts_rollovers
        timestamptz vesting_schedule
        timestamptz created_at
        timestamptz updated_at
    }
    
    asset_real_estate {
        uuid asset_id PK
        real_estate_type property_type
        property_status property_status
        varchar property_address
        varchar city
        varchar state
        varchar zip_code
        integer square_footage
        integer bedrooms
        integer bathrooms
        integer year_built
        decimal lot_size
        decimal purchase_price
        timestamptz purchase_date
        decimal estimated_value
        timestamptz last_appraisal_date
        decimal rental_income
        boolean is_primary_residence
        boolean is_rental_property
    }
    
    asset_vehicles {
        uuid asset_id PK
        vehicle_type vehicle_type
        varchar make
        varchar model
        integer year
        varchar vin
        varchar license_plate
        integer mileage
        vehicle_condition condition
        decimal purchase_price
        timestamptz purchase_date
        decimal estimated_value
        boolean is_financed
        uuid loan_liability_id FK
        boolean has_insurance
        uuid insurance_policy_id FK
    }
    
    asset_valuations {
        uuid id PK
        uuid asset_id FK
        decimal valuation_amount
        valuation_type valuation_type
        valuation_source source
        timestamptz valuation_date
        text notes
        jsonb supporting_documents
        uuid created_by_id FK
        timestamptz created_at
    }
    
    liability_mortgages {
        uuid liability_id PK
        mortgage_type mortgage_type
        varchar lender_name
        varchar loan_number
        decimal loan_to_value_ratio
        decimal interest_rate
        rate_type rate_type
        integer term_years
        decimal monthly_payment
        decimal principal_payment
        decimal interest_payment
        decimal escrow_payment
        timestamptz origination_date
        timestamptz maturity_date
        uuid property_asset_id FK
        boolean has_pmi
        decimal pmi_amount
        timestamptz created_at
        timestamptz updated_at
    }
    
    liability_credit_cards {
        uuid liability_id PK
        varchar issuer_name
        varchar account_number
        decimal credit_limit
        decimal available_credit
        decimal interest_rate
        decimal minimum_payment
        timestamptz statement_date
        timestamptz due_date
        integer days_past_due
        credit_card_status status
        boolean is_business_card
        decimal annual_fee
        varchar rewards_program
        timestamptz created_at
        timestamptz updated_at
    }
    
    audit_log {
        uuid id PK
        uuid user_id FK
        varchar affected_table_name
        varchar operation_type
        uuid record_id
        jsonb old_values
        jsonb new_values
        varchar ip_address
        varchar user_agent
        timestamptz created_at
        text notes
    }
    
    portfolio_snapshots {
        uuid id PK
        uuid portfolio_id FK
        decimal total_assets
        decimal total_liabilities
        decimal net_worth
        decimal liquid_assets
        decimal retirement_assets
        decimal real_estate_value
        decimal investment_value
        decimal monthly_income
        decimal monthly_expenses
        decimal cash_flow
        timestamptz snapshot_date
        snapshot_type snapshot_type
        uuid created_by_id FK
        timestamptz created_at
    }
    
    notification_history {
        uuid id PK
        uuid user_id FK
        notification_type type
        varchar title
        text message
        notification_priority priority
        notification_status status
        jsonb data
        varchar channel
        timestamptz sent_at
        timestamptz read_at
        timestamptz expires_at
        uuid created_by_id FK
        timestamptz created_at
    }
    
    export_history {
        uuid id PK
        uuid user_id FK
        export_type export_type
        export_format format
        varchar file_name
        integer file_size
        export_status status
        jsonb parameters
        timestamptz requested_at
        timestamptz completed_at
        timestamptz expires_at
        text error_message
        varchar download_url
        uuid created_by_id FK
        timestamptz created_at
    }
    
    income_employment {
        uuid income_id PK
        varchar employer_name
        varchar job_title
        varchar employee_id
        employment_type employment_type
        employment_status status
        decimal base_salary
        decimal hourly_rate
        decimal hours_per_week
        boolean has_overtime
        decimal overtime_rate
        boolean has_commission
        decimal commission_rate
        boolean has_bonus
        decimal annual_bonus
        timestamptz hire_date
        timestamptz end_date
        varchar department
        varchar manager_name
        boolean is_remote
        varchar work_location
    }
    
    income_paystubs {
        uuid id PK
        uuid income_id FK
        timestamptz pay_period_start
        timestamptz pay_period_end
        timestamptz pay_date
        decimal gross_pay
        decimal net_pay
        decimal regular_hours
        decimal overtime_hours
        decimal regular_pay
        decimal overtime_pay
        decimal commission
        decimal bonus
        decimal total_deductions
        decimal total_taxes
        jsonb deduction_details
        varchar paystub_number
        uuid created_by_id FK
        timestamptz created_at
    }
    
    income_paystub_deductions {
        uuid id PK
        uuid paystub_id FK
        deduction_type deduction_type
        varchar description
        decimal amount
        boolean is_pre_tax
        boolean is_employer_match
        decimal employer_contribution
        varchar account_number
        timestamptz created_at
    }
    
    insurance_life_policies {
        uuid insurance_policy_id PK
        life_insurance_type policy_type
        decimal death_benefit
        decimal cash_value
        boolean has_riders
        jsonb rider_details
        varchar medical_exam_required
        health_class health_classification
        varchar smoking_status
        timestamptz medical_exam_date
        boolean is_convertible
        timestamptz conversion_deadline
        timestamptz created_at
        timestamptz updated_at
    }
    
    insurance_policy_beneficiaries {
        uuid id PK
        uuid insurance_policy_id FK
        varchar first_name
        varchar last_name
        varchar relationship
        decimal beneficiary_percentage
        beneficiary_type beneficiary_type
        varchar address
        varchar phone
        varchar email
        varchar social_security_number
        timestamptz date_of_birth
        boolean is_contingent
        text notes
        timestamptz created_at
        uuid created_by_id FK
    }
    
    insurance_auto_policies {
        uuid insurance_policy_id PK
        varchar vin
        varchar make
        varchar model
        integer year
        varchar license_plate
        decimal vehicle_value
        coverage_type liability_coverage
        decimal liability_limit
        coverage_type collision_coverage
        decimal collision_deductible
        coverage_type comprehensive_coverage
        decimal comprehensive_deductible
        boolean has_rental_coverage
        boolean has_roadside_assistance
        decimal annual_mileage
        varchar primary_use
        varchar garage_location
        timestamptz created_at
        timestamptz updated_at
    }
    
    insurance_auto_policy_drivers {
        uuid id PK
        uuid auto_policy_id FK
        varchar first_name
        varchar last_name
        timestamptz date_of_birth
        varchar license_number
        varchar license_state
        driver_status status
        integer years_licensed
        boolean has_violations
        jsonb violation_details
        decimal annual_mileage
        varchar primary_vehicle_vin
        timestamptz created_at
    }
    
    auth_user_passwords {
        uuid id PK
        uuid user_id FK
        varchar hashed_password
        password_hash_algorithm algorithm "bcrypt"
        integer algorithm_version
        jsonb password_history "Last 5 passwords"
        timestamptz created_at
        timestamptz updated_at
    }
    
    auth_password_reset_tokens {
        uuid id PK
        uuid user_id FK
        varchar token_hash
        timestamptz issued_at
        timestamptz expires_at "1 hour expiry"
        boolean is_used
        timestamptz used_at
        varchar ip_address
        varchar user_agent
        reset_request_reason reason
        timestamptz created_at
    }
    
    auth_email_verification_tokens {
        uuid id PK
        uuid user_id FK
        varchar email
        varchar token_hash
        verification_type verification_type
        timestamptz issued_at
        timestamptz expires_at
        boolean is_verified
        timestamptz verified_at
        varchar ip_address
        varchar user_agent
        integer attempt_count
        timestamptz created_at
    }