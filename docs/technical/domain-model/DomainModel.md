# FinPro Domain Model Documentation

## Overview

This document provides a comprehensive overview of the FinPro database schema, describing each table's purpose, structure, and relationships. The domain model supports multi-user household financial management with complete audit trails and flexible entity associations.

## Table of Contents

1. [Core Tables](#core-tables)
   - [Households](#households)
   - [Users](#users)
   - [Portfolios](#portfolios)
   - [Household Members](#household-members)
   - [Portfolio Owners](#portfolio-owners)
   - [User Roles](#user-roles)

2. [Financial Entity Tables](#financial-entity-tables)
   - [Assets](#assets)
     - [Asset Bank Accounts](#asset-bank-accounts)
     - [Asset Investment Accounts](#asset-investment-accounts)
     - [Asset Retirement Accounts](#asset-retirement-accounts)
     - [Asset Real Estate](#asset-real-estate)
     - [Asset Vehicles](#asset-vehicles)
     - [Asset Personal Property](#asset-personal-property)
     - [Asset Business Interests](#asset-business-interests)
     - [Asset Other](#asset-other)
     - [Asset Valuations](#asset-valuations)
   - [Liabilities](#liabilities)
     - [Liability Mortgages](#liability-mortgages)
     - [Liability Credit Cards](#liability-credit-cards)
     - [Liability Student Loans](#liability-student-loans)
     - [Liability Vehicle Loans](#liability-vehicle-loans)
     - [Liability Personal Loans](#liability-personal-loans)
     - [Liability Business Loans](#liability-business-loans)
     - [Liability Taxes](#liability-taxes)
     - [Liability Other](#liability-other)
     - [Liability Payments](#liability-payments)
     - [Liability Rate Changes](#liability-rate-changes)
   - [Income](#income)
     - [Income Employment](#income-employment)
     - [Income Business](#income-business)
     - [Income Investment](#income-investment)
     - [Income Rental](#income-rental)
     - [Income Retirement](#income-retirement)
     - [Income Support](#income-support)
     - [Income Other](#income-other)
     - [Income Paystubs](#income-paystubs)
     - [Income Paystub Deductions](#income-paystub-deductions)
     - [Income Tax Forms](#income-tax-forms)
     - [Income Projections](#income-projections)
   - [Expenses](#expenses)
     - [Expense Housing](#expense-housing)
     - [Expense Transportation](#expense-transportation)
     - [Expense Healthcare](#expense-healthcare)
     - [Expense Insurance](#expense-insurance)
     - [Expense Utilities](#expense-utilities)
     - [Expense Education](#expense-education)
     - [Expense Childcare](#expense-childcare)
     - [Expense Debt Payments](#expense-debt-payments)
     - [Expense Savings Investments](#expense-savings-investments)
     - [Expense Charitable](#expense-charitable)
     - [Expense Other](#expense-other)
     - [Expense Transactions](#expense-transactions)
     - [Expense Budgets](#expense-budgets)
   - [Insurance Policies](#insurance-policies)
     - [Insurance Life](#insurance-life)
     - [Insurance Disability](#insurance-disability)
     - [Insurance Additional Insureds](#insurance-additional-insureds)
     - [Insurance Beneficiaries](#insurance-beneficiaries)
     - [Insurance Health](#insurance-health)
     - [Insurance Property](#insurance-property)
     - [Insurance Auto](#insurance-auto)
     - [Insurance Auto Drivers](#insurance-auto-drivers)
     - [Insurance Claims](#insurance-claims)
     - [Insurance Renewals](#insurance-renewals)

3. [Audit and History Tables](#audit-and-history-tables)
   - [Audit Log](#audit-log)
   - [Entity History](#entity-history)
   - [Portfolio Snapshots](#portfolio-snapshots)
   - [Notification History](#notification-history)
   - [Session History](#session-history)
   - [Export History](#export-history)

4. [Association Tables](#association-tables)
   - [Entity Associations](#entity-associations)
   - [Asset Liability Associations](#asset-liability-associations)
   - [Asset Expense Associations](#asset-expense-associations)
   - [Asset Income Associations](#asset-income-associations)
   - [Liability Expense Associations](#liability-expense-associations)
   - [Insurance Coverage Associations](#insurance-coverage-associations)
   - [Entity Dependencies](#entity-dependencies)
   - [Association Templates](#association-templates)

5. [Entity Relationship Diagram](#entity-relationship-diagram)

---

## Core Tables

### Households

**Purpose**: Represents a family unit or group that shares financial planning. Households serve as the top-level container for organizing users and their portfolios.

**Usage**: Used to group related users (family members, trustees, etc.) and manage shared financial data with privacy controls.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| name | Household display name | VARCHAR(255) | `"Smith Family"` |
| type | Type of household | VARCHAR(50) | `"family"` |
| description | Optional description | TEXT | `"Primary family household"` |
| is_private | Privacy flag | BOOLEAN | `false` |
| allow_advisor_access | Allow advisor access | BOOLEAN | `true` |
| data_sharing_enabled | Enable data sharing | BOOLEAN | `false` |
| settings | JSON configuration | JSONB | `{"theme": "blue", "notifications": true}` |
| preferences | User preferences | JSONB | `{"currency": "USD", "locale": "en-US"}` |
| tags | Searchable tags | TEXT[] | `["high-net-worth", "retirement-planning"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Users

**Purpose**: Stores user account information including authentication, profile data, and system preferences. Central to all user interactions with the system.

**Usage**: Used for authentication, authorization, user profile management, and tracking user activities across the system.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| auth_provider | Authentication provider | VARCHAR(50) | `"auth0"` |
| auth_provider_id | Provider's user ID | VARCHAR(255) | `"auth0|507f1f77bcf86cd799439011"` |
| username | Unique username | VARCHAR(100) | `"jsmith"` |
| email | Email address | VARCHAR(255) | `"<EMAIL>"` |
| email_verified | Email verification status | BOOLEAN | `true` |
| email_verified_at | Email verification timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| password_hash | Hashed password | VARCHAR(255) | `"$2b$10$..."` |
| password_changed_at | Password change timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| mfa_enabled | Multi-factor auth status | BOOLEAN | `true` |
| mfa_secret | MFA secret key | VARCHAR(255) | `"encrypted_secret"` |
| mfa_backup_codes | Backup codes | TEXT[] | `["code1", "code2", "code3"]` |
| mfa_methods | Enabled MFA methods | mfa_type[] | `["totp", "sms"]` |
| first_name | First name | VARCHAR(100) | `"John"` |
| middle_name | Middle name | VARCHAR(100) | `"Michael"` |
| last_name | Last name | VARCHAR(100) | `"Smith"` |
| display_name | Display name | VARCHAR(255) | `"John Smith"` |
| date_of_birth | Birth date | DATE | `1980-05-15` |
| phone | Phone number | VARCHAR(50) | `"+1-************"` |
| phone_verified | Phone verification status | BOOLEAN | `true` |
| address_line1 | Street address | VARCHAR(255) | `"123 Main Street"` |
| address_line2 | Address line 2 | VARCHAR(255) | `"Apt 4B"` |
| city | City | VARCHAR(100) | `"New York"` |
| state_province | State/Province | VARCHAR(100) | `"NY"` |
| postal_code | Postal code | VARCHAR(20) | `"10001"` |
| country | Country code | VARCHAR(2) | `"US"` |
| role | System role | user_role | `"client"` |
| status | Account status | user_status | `"active"` |
| permissions | Custom permissions | JSONB | `{"can_export": true}` |
| preferences | User preferences | JSONB | `{"notifications": {"email": true}}` |
| settings | User settings | JSONB | `{"theme": "dark"}` |
| last_login_at | Last login timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| last_login_ip | Last login IP | INET | `*************` |
| last_activity_at | Last activity timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| login_count | Total login count | INTEGER | `42` |
| failed_login_count | Failed login attempts | INTEGER | `0` |
| locked_until | Account lock expiry | TIMESTAMPTZ | `null` |
| terms_accepted_at | Terms acceptance timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| terms_version | Accepted terms version | VARCHAR(20) | `"2.0"` |
| privacy_accepted_at | Privacy policy acceptance | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| privacy_version | Accepted privacy version | VARCHAR(20) | `"1.5"` |
| marketing_consent | Marketing consent flag | BOOLEAN | `false` |
| marketing_consent_at | Marketing consent timestamp | TIMESTAMPTZ | `null` |
| avatar_url | Profile picture URL | VARCHAR(500) | `"https://example.com/avatars/jsmith.jpg"` |
| timezone | User timezone | VARCHAR(50) | `"America/New_York"` |
| locale | User locale | VARCHAR(10) | `"en-US"` |
| tags | User tags | TEXT[] | `["vip", "advisor-managed"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Portfolios

**Purpose**: Represents a collection of financial assets, liabilities, income, and expenses. Each household can have multiple portfolios for different purposes.

**Usage**: Used to organize and track financial data, calculate net worth, generate reports, and manage different financial scenarios.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| household_id | Parent household | UUID | `123e4567-e89b-12d3-a456-************` |
| name | Portfolio name | VARCHAR(255) | `"Retirement Portfolio"` |
| description | Portfolio description | TEXT | `"401k and IRA accounts"` |
| ownership_type | Type of ownership | portfolio_ownership_type | `"joint"` |
| is_primary | Primary portfolio flag | BOOLEAN | `true` |
| data_entry_mode | Data complexity mode | data_entry_mode | `"standard"` |
| planning_horizon_years | Planning timeframe | INTEGER | `30` |
| visibility | Access visibility | portfolio_visibility | `"household"` |
| is_archived | Archive status | BOOLEAN | `false` |
| archived_at | Archive timestamp | TIMESTAMPTZ | `null` |
| archived_by | Archiving user | UUID | `null` |
| total_assets | Calculated asset total | DECIMAL(15,2) | `500000.00` |
| total_liabilities | Calculated liability total | DECIMAL(15,2) | `200000.00` |
| net_worth | Calculated net worth | DECIMAL(15,2) | `300000.00` |
| last_calculated_at | Last calculation time | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| settings | Portfolio settings | JSONB | `{"risk_tolerance": "moderate"}` |
| goals | Financial goals | JSONB | `[{"name": "Retirement", "target": 1000000}]` |
| assumptions | Planning assumptions | JSONB | `{"inflation_rate": 0.03, "return_rate": 0.07}` |
| tags | Portfolio tags | TEXT[] | `["retirement", "long-term"]` |
| external_id | External reference | VARCHAR(100) | `"EXT-12345"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Household Members

**Purpose**: Junction table that links users to households and defines their roles and permissions within the household.

**Usage**: Used to manage household membership, assign roles, control permissions, and track member relationships.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| household_id | Household reference | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| role | Member role | household_role | `"head"` |
| relationship | Relationship type | household_relationship | `"spouse"` |
| status | Membership status | household_member_status | `"active"` |
| can_view | View permission | BOOLEAN | `true` |
| can_edit | Edit permission | BOOLEAN | `true` |
| can_delete | Delete permission | BOOLEAN | `false` |
| can_manage_members | Member management permission | BOOLEAN | `true` |
| can_manage_advisors | Advisor management permission | BOOLEAN | `false` |
| custom_permissions | Additional permissions | JSONB | `{"can_export": true}` |
| is_primary | Primary member flag | BOOLEAN | `true` |
| joined_at | Join timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| invited_by | Inviting user | UUID | `123e4567-e89b-12d3-a456-************` |
| invitation_sent_at | Invitation timestamp | TIMESTAMPTZ | `2024-01-14 10:30:00+00` |
| invitation_accepted_at | Acceptance timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| notes | Member notes | TEXT | `"Primary account holder"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |

### Portfolio Owners

**Purpose**: Junction table that defines ownership relationships between users and portfolios, including ownership percentages and permissions.

**Usage**: Used to manage portfolio ownership, track ownership percentages, control access permissions, and support joint ownership scenarios.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| ownership_percentage | Ownership percentage | DECIMAL(5,2) | `50.00` |
| is_primary_owner | Primary owner flag | BOOLEAN | `true` |
| can_view | View permission | BOOLEAN | `true` |
| can_edit | Edit permission | BOOLEAN | `true` |
| can_delete | Delete permission | BOOLEAN | `false` |
| can_share | Share permission | BOOLEAN | `false` |
| can_export | Export permission | BOOLEAN | `true` |
| custom_permissions | Additional permissions | JSONB | `{"can_transfer": true}` |
| added_at | Addition timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| added_by | Adding user | UUID | `123e4567-e89b-12d3-a456-************` |
| relationship_type | Owner relationship | VARCHAR(50) | `"joint_owner"` |
| notes | Ownership notes | TEXT | `"Inherited from parent"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |

### User Roles

**Purpose**: Assigns additional roles to users with specific scopes (system-wide, household, or portfolio level) and time bounds.

**Usage**: Used for granular permission management, temporary role assignments, and role-based access control beyond the base user role.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| role | Assigned role | user_role | `"advisor"` |
| household_id | Household scope | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio scope | UUID | `null` |
| valid_from | Role start date | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| valid_until | Role end date | TIMESTAMPTZ | `2024-12-31 23:59:59+00` |
| permissions | Role permissions | JSONB | `{"can_view_all": true}` |
| restrictions | Role restrictions | JSONB | `{"max_portfolios": 10}` |
| assigned_by | Assigning user | UUID | `123e4567-e89b-12d3-a456-************` |
| assigned_at | Assignment timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| reason | Assignment reason | TEXT | `"Temporary advisor access"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |

---

# Financial Entity Tables

## Assets

**Purpose**: Base table for all asset types. Stores common asset information with specific details in child tables based on asset type.

**Usage**: Central repository for all assets including bank accounts, investments, real estate, vehicles, and other valuable items.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_type | Type of asset | asset_type | `"bank_account"` |
| name | Asset name | VARCHAR(255) | `"Chase Checking"` |
| description | Asset description | TEXT | `"Primary checking account"` |
| ownership_type | Ownership structure | asset_ownership_type | `"joint_tenancy"` |
| ownership_percentage | Ownership percentage | DECIMAL(5,2) | `100.00` |
| current_value | Current market value | DECIMAL(15,2) | `25000.00` |
| purchase_value | Original purchase value | DECIMAL(15,2) | `25000.00` |
| purchase_date | Purchase date | DATE | `2020-01-15` |
| is_active | Active status | BOOLEAN | `true` |
| institution_name | Financial institution | VARCHAR(255) | `"Chase Bank"` |
| account_number | Account number | VARCHAR(100) | `"****1234"` |
| contact_info | Contact information | JSONB | `{"phone": "1-800-CHASE"}` |
| metadata | Additional data | JSONB | `{"branch": "NYC Main"}` |
| tags | Asset tags | TEXT[] | `["liquid", "primary"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Asset Bank Accounts

**Purpose**: Stores specific details for bank account assets including account types, interest rates, and banking features.

**Usage**: Extends the base asset table with bank-specific information for checking, savings, and other bank accounts.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| account_type | Type of bank account | bank_account_type | `"checking"` |
| account_status | Account status | account_status | `"active"` |
| routing_number | Bank routing number | VARCHAR(20) | `"*********"` |
| is_checking | Checking account flag | BOOLEAN | `true` |
| is_savings | Savings account flag | BOOLEAN | `false` |
| interest_rate | Annual interest rate | DECIMAL(5,4) | `0.0100` |
| interest_frequency | Interest payment frequency | payment_frequency | `"monthly"` |
| minimum_balance | Minimum balance requirement | DECIMAL(15,2) | `1500.00` |
| monthly_fee | Monthly service fee | DECIMAL(10,2) | `12.00` |
| overdraft_protection | Overdraft protection enabled | BOOLEAN | `true` |
| has_debit_card | Debit card issued | BOOLEAN | `true` |
| has_checks | Check writing enabled | BOOLEAN | `true` |
| online_banking_enabled | Online banking active | BOOLEAN | `true` |

### Asset Investment Accounts

**Purpose**: Stores details for investment accounts including brokerage accounts, mutual funds, and other investment vehicles.

**Usage**: Tracks investment-specific information including holdings, performance metrics, and account features.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| account_type | Type of investment account | investment_account_type | `"brokerage"` |
| account_status | Account status | account_status | `"active"` |
| brokerage_name | Brokerage firm name | VARCHAR(255) | `"Fidelity"` |
| account_number | Account number | VARCHAR(100) | `"X12345678"` |
| total_holdings | Number of holdings | INTEGER | `15` |
| cash_balance | Cash balance | DECIMAL(15,2) | `5000.00` |
| margin_balance | Margin balance | DECIMAL(15,2) | `0.00` |
| cost_basis | Total cost basis | DECIMAL(15,2) | `75000.00` |
| unrealized_gain_loss | Unrealized P&L | DECIMAL(15,2) | `25000.00` |
| ytd_return | Year-to-date return % | DECIMAL(5,2) | `12.50` |
| margin_enabled | Margin trading enabled | BOOLEAN | `false` |
| options_enabled | Options trading enabled | BOOLEAN | `false` |
| risk_tolerance | Risk profile | VARCHAR(50) | `"moderate"` |
| investment_objective | Investment goal | VARCHAR(255) | `"long-term growth"` |

### Asset Retirement Accounts

**Purpose**: Stores retirement account specific information including 401(k), IRA, and pension details.

**Usage**: Tracks retirement account features, contributions, vesting schedules, and distribution information.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| account_type | Type of retirement account | retirement_account_type | `"401k"` |
| account_status | Account status | account_status | `"active"` |
| plan_name | Retirement plan name | VARCHAR(255) | `"Company 401(k) Plan"` |
| employer_name | Employer name | VARCHAR(255) | `"Acme Corporation"` |
| participant_id | Participant ID | VARCHAR(100) | `"EMP12345"` |
| employee_contribution_pct | Employee contribution % | DECIMAL(5,2) | `6.00` |
| employee_contribution_amt | Employee contribution $ | DECIMAL(10,2) | `500.00` |
| employer_match_pct | Employer match % | DECIMAL(5,2) | `3.00` |
| employer_match_amt | Employer match $ | DECIMAL(10,2) | `250.00` |
| vesting_schedule | Vesting schedule description | TEXT | `"20% per year"` |
| vested_percentage | Current vested % | DECIMAL(5,2) | `60.00` |
| vested_balance | Vested balance | DECIMAL(15,2) | `60000.00` |
| has_loan | Active loan flag | BOOLEAN | `false` |
| loan_balance | Outstanding loan balance | DECIMAL(15,2) | `0.00` |
| loan_interest_rate | Loan interest rate | DECIMAL(5,4) | `0.0000` |
| eligible_for_distribution | Distribution eligibility | BOOLEAN | `false` |
| required_minimum_distribution | RMD amount | DECIMAL(10,2) | `0.00` |
| last_distribution_date | Last distribution date | DATE | `null` |

### Asset Real Estate

**Purpose**: Stores real estate property information including residential, commercial, and land holdings.

**Usage**: Tracks property details, valuations, rental income, and property-related expenses.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| property_type | Type of property | property_type | `"single_family"` |
| property_use | Property usage | property_use | `"primary_residence"` |
| address_line1 | Street address | VARCHAR(255) | `"123 Main Street"` |
| address_line2 | Address line 2 | VARCHAR(255) | `null` |
| city | City | VARCHAR(100) | `"New York"` |
| state_province | State/Province | VARCHAR(100) | `"NY"` |
| postal_code | Postal code | VARCHAR(20) | `"10001"` |
| country | Country code | VARCHAR(2) | `"US"` |
| year_built | Year constructed | INTEGER | `2005` |
| square_footage | Total square feet | INTEGER | `2500` |
| lot_size_acres | Lot size in acres | DECIMAL(10,4) | `0.2500` |
| bedrooms | Number of bedrooms | INTEGER | `3` |
| bathrooms | Number of bathrooms | DECIMAL(3,1) | `2.5` |
| assessed_value | Tax assessed value | DECIMAL(15,2) | `450000.00` |
| assessment_date | Assessment date | DATE | `2023-07-01` |
| market_value | Market value | DECIMAL(15,2) | `500000.00` |
| appraisal_date | Appraisal date | DATE | `2023-06-15` |
| is_rental | Rental property flag | BOOLEAN | `false` |
| monthly_rental_income | Monthly rental income | DECIMAL(10,2) | `0.00` |
| occupancy_rate | Occupancy rate % | DECIMAL(5,2) | `0.00` |
| property_tax_annual | Annual property tax | DECIMAL(10,2) | `6000.00` |
| insurance_annual | Annual insurance cost | DECIMAL(10,2) | `1200.00` |
| hoa_monthly | Monthly HOA fee | DECIMAL(10,2) | `200.00` |
| maintenance_annual | Annual maintenance cost | DECIMAL(10,2) | `3000.00` |

### Asset Vehicles

**Purpose**: Stores vehicle information including cars, boats, RVs, and other transportation assets.

**Usage**: Tracks vehicle details, values, loans, and insurance information.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| vehicle_type | Type of vehicle | vehicle_type | `"car"` |
| vehicle_use | Vehicle usage | vehicle_use | `"personal"` |
| year | Model year | INTEGER | `2022` |
| make | Vehicle make | VARCHAR(100) | `"Toyota"` |
| model | Vehicle model | VARCHAR(100) | `"Camry"` |
| trim | Vehicle trim level | VARCHAR(100) | `"XLE"` |
| vin | Vehicle ID number | VARCHAR(17) | `"1HGBH41JXMN109186"` |
| license_plate | License plate number | VARCHAR(20) | `"ABC-1234"` |
| mileage | Current mileage | INTEGER | `25000` |
| condition | Vehicle condition | VARCHAR(50) | `"excellent"` |
| has_loan | Active loan flag | BOOLEAN | `true` |
| loan_balance | Outstanding loan balance | DECIMAL(15,2) | `18000.00` |
| monthly_payment | Monthly loan payment | DECIMAL(10,2) | `450.00` |
| insurance_carrier | Insurance company | VARCHAR(255) | `"State Farm"` |
| insurance_policy_number | Policy number | VARCHAR(100) | `"POL-123456"` |
| insurance_annual_premium | Annual insurance cost | DECIMAL(10,2) | `1800.00` |

### Asset Personal Property

**Purpose**: Stores information about valuable personal property including jewelry, art, collectibles, and other high-value items.

**Usage**: Tracks valuable personal items for insurance, estate planning, and net worth calculations.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| property_type | Type of property | VARCHAR(50) | `"jewelry"` |
| item_description | Item description | TEXT | `"3-carat diamond engagement ring"` |
| manufacturer | Manufacturer/creator | VARCHAR(255) | `"Tiffany & Co."` |
| model_number | Model/serial number | VARCHAR(100) | `"T123456"` |
| serial_number | Serial number | VARCHAR(100) | `"SN789012"` |
| purchase_price | Purchase price | DECIMAL(15,2) | `15000.00` |
| purchase_date | Purchase date | DATE | `2020-06-15` |
| appraised_value | Appraised value | DECIMAL(15,2) | `18000.00` |
| appraisal_date | Appraisal date | DATE | `2023-01-10` |
| appraiser_name | Appraiser name | VARCHAR(255) | `"Johnson Appraisals"` |
| condition | Item condition | VARCHAR(20) | `"excellent"` |
| storage_location | Storage location | VARCHAR(255) | `"Home safe"` |
| is_scheduled_on_insurance | Scheduled on insurance | BOOLEAN | `true` |
| insurance_declared_value | Insurance value | DECIMAL(15,2) | `18000.00` |

### Asset Business Interests

**Purpose**: Stores information about business ownership interests including partnerships, corporations, and sole proprietorships.

**Usage**: Tracks business equity, valuations, and income for business owners and investors.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| business_name | Business name | VARCHAR(255) | `"ABC Technologies LLC"` |
| business_type | Business structure | VARCHAR(50) | `"llc"` |
| tax_id | Business tax ID | VARCHAR(20) | `"12-3456789"` |
| ownership_percentage | Ownership percentage | DECIMAL(5,2) | `25.00` |
| number_of_shares | Number of shares owned | INTEGER | `250000` |
| share_class | Class of shares | VARCHAR(50) | `"Class A Common"` |
| book_value | Book value | DECIMAL(15,2) | `500000.00` |
| market_value | Market value | DECIMAL(15,2) | `750000.00` |
| valuation_date | Valuation date | DATE | `2023-12-31` |
| valuation_method | Valuation method | VARCHAR(100) | `"Discounted Cash Flow"` |
| annual_revenue | Annual revenue | DECIMAL(15,2) | `2000000.00` |
| annual_profit | Annual profit | DECIMAL(15,2) | `400000.00` |
| annual_distributions | Annual distributions | DECIMAL(15,2) | `100000.00` |
| industry | Industry sector | VARCHAR(100) | `"Software Development"` |
| number_of_employees | Employee count | INTEGER | `45` |
| year_established | Year established | INTEGER | `2015` |

### Asset Other

**Purpose**: Flexible table for miscellaneous assets that don't fit into other categories such as intellectual property, royalties, or unique assets.

**Usage**: Captures any asset type not covered by specific asset tables with flexible JSON storage for custom attributes.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| asset_id | Asset reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_type | Type of asset | VARCHAR(100) | `"intellectual_property"` |
| details | Asset details (JSON) | JSONB | `{"patent_number": "US1234567", "field": "Software"}` |
| institution_name | Institution name | VARCHAR(255) | `"US Patent Office"` |
| account_identifier | Account/ID number | VARCHAR(100) | `"PAT-2023-001"` |
| maturity_date | Maturity date | DATE | `2040-01-15` |
| interest_rate | Interest rate % | DECIMAL(7,4) | `null` |
| annual_income | Annual income | DECIMAL(15,2) | `50000.00` |
| income_type | Type of income | VARCHAR(50) | `"royalties"` |

### Asset Valuations

**Purpose**: Tracks historical valuations for all asset types to maintain valuation history and support performance tracking.

**Usage**: Records periodic asset valuations from various sources for trend analysis and reporting.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| valuation_date | Valuation date | DATE | `2024-01-15` |
| value | Valuation amount | DECIMAL(15,2) | `525000.00` |
| valuation_method | Valuation method | VARCHAR(50) | `"market_comparison"` |
| source | Valuation source | VARCHAR(255) | `"Zillow Estimate"` |
| notes | Valuation notes | TEXT | `"Annual property assessment"` |
| document_url | Supporting document URL | TEXT | `"https://docs.example.com/val123.pdf"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

## Liabilities

**Purpose**: Base table for all liability types. Stores common debt information with specific details in child tables.

**Usage**: Central repository for all debts including mortgages, loans, credit cards, and other financial obligations.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_type | Type of liability | liability_type | `"mortgage"` |
| name | Liability name | VARCHAR(255) | `"Home Mortgage"` |
| description | Liability description | TEXT | `"Primary residence mortgage"` |
| creditor_name | Creditor/Lender name | VARCHAR(255) | `"Wells Fargo"` |
| account_number | Account number | VARCHAR(100) | `"****5678"` |
| original_amount | Original loan amount | DECIMAL(15,2) | `400000.00` |
| current_balance | Current balance | DECIMAL(15,2) | `350000.00` |
| minimum_payment | Minimum payment | DECIMAL(10,2) | `2500.00` |
| interest_rate | Interest rate % | DECIMAL(6,4) | `3.7500` |
| interest_rate_type | Rate type | interest_rate_type | `"fixed"` |
| payment_frequency | Payment frequency | payment_frequency | `"monthly"` |
| origination_date | Loan origination date | DATE | `2020-03-15` |
| first_payment_date | First payment date | DATE | `2020-05-01` |
| maturity_date | Loan maturity date | DATE | `2050-03-15` |
| last_payment_date | Last payment date | DATE | `2024-01-01` |
| status | Liability status | liability_status | `"current"` |
| is_active | Active status | BOOLEAN | `true` |
| metadata | Additional data | JSONB | `{"loan_officer": "John Doe"}` |
| tags | Liability tags | TEXT[] | `["secured", "real-estate"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Liability Mortgages

**Purpose**: Stores mortgage-specific information including property references, PMI, escrow, and loan features.

**Usage**: Extends liability table with mortgage-specific details for home loans, HELOCs, and other property-secured debt.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| mortgage_type | Type of mortgage | mortgage_type | `"conventional"` |
| property_asset_id | Related property asset | UUID | `123e4567-e89b-12d3-a456-************` |
| loan_to_value | Current LTV ratio | DECIMAL(5,2) | `70.00` |
| is_primary_residence | Primary residence flag | BOOLEAN | `true` |
| has_pmi | PMI required flag | BOOLEAN | `false` |
| pmi_amount | Monthly PMI amount | DECIMAL(10,2) | `0.00` |
| pmi_removal_ltv | LTV for PMI removal | DECIMAL(5,2) | `80.00` |
| has_escrow | Escrow account flag | BOOLEAN | `true` |
| escrow_balance | Current escrow balance | DECIMAL(10,2) | `3000.00` |
| property_tax_monthly | Monthly property tax | DECIMAL(10,2) | `500.00` |
| insurance_monthly | Monthly insurance | DECIMAL(10,2) | `100.00` |
| is_assumable | Assumable loan flag | BOOLEAN | `false` |
| has_prepayment_penalty | Prepayment penalty flag | BOOLEAN | `false` |
| prepayment_penalty_end_date | Penalty end date | DATE | `null` |

### Liability Credit Cards

**Purpose**: Stores credit card account information including limits, rates, fees, and rewards programs.

**Usage**: Tracks credit card debt, available credit, interest rates, and card features.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| card_type | Card network/type | VARCHAR(50) | `"Visa"` |
| last_four_digits | Last 4 digits | VARCHAR(4) | `"1234"` |
| credit_limit | Credit limit | DECIMAL(15,2) | `10000.00` |
| available_credit | Available credit | DECIMAL(15,2) | `7500.00` |
| cash_advance_limit | Cash advance limit | DECIMAL(10,2) | `3000.00` |
| purchase_apr | Purchase APR % | DECIMAL(6,4) | `18.9900` |
| cash_advance_apr | Cash advance APR % | DECIMAL(6,4) | `24.9900` |
| balance_transfer_apr | Balance transfer APR % | DECIMAL(6,4) | `0.0000` |
| penalty_apr | Penalty APR % | DECIMAL(6,4) | `29.9900` |
| annual_fee | Annual fee | DECIMAL(10,2) | `95.00` |
| late_payment_fee | Late payment fee | DECIMAL(10,2) | `39.00` |
| over_limit_fee | Over limit fee | DECIMAL(10,2) | `39.00` |
| has_rewards | Rewards program flag | BOOLEAN | `true` |
| rewards_program | Rewards program name | VARCHAR(255) | `"Cash Back"` |
| rewards_balance | Current rewards balance | INTEGER | `15000` |

### Liability Student Loans

**Purpose**: Stores student loan information including federal/private loans, repayment plans, and forgiveness programs.

**Usage**: Tracks education debt, repayment status, and eligibility for various loan programs.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| loan_type | Type of student loan | student_loan_type | `"federal_subsidized"` |
| school_name | School name | VARCHAR(255) | `"State University"` |
| degree_program | Degree program | VARCHAR(255) | `"Bachelor of Science"` |
| enrollment_status | Current enrollment | VARCHAR(50) | `"graduated"` |
| is_federal | Federal loan flag | BOOLEAN | `true` |
| servicer_name | Loan servicer | VARCHAR(255) | `"Nelnet"` |
| repayment_plan | Current repayment plan | VARCHAR(100) | `"income_based"` |
| is_in_repayment | In repayment flag | BOOLEAN | `true` |
| deferment_end_date | Deferment end date | DATE | `null` |
| forbearance_end_date | Forbearance end date | DATE | `null` |
| eligible_for_forgiveness | Forgiveness eligible | BOOLEAN | `true` |
| forgiveness_program | Forgiveness program | VARCHAR(255) | `"PSLF"` |
| qualifying_payments_made | Qualifying payments made | INTEGER | `36` |
| qualifying_payments_required | Total required payments | INTEGER | `120` |

### Liability Vehicle Loans

**Purpose**: Stores vehicle loan and lease information including terms, insurance, and vehicle references.

**Usage**: Tracks auto loans, leases, and related terms for vehicles owned or leased.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| lender_name | Lender name | VARCHAR(255) | `"Toyota Financial Services"` |
| loan_number | Loan account number | VARCHAR(100) | `"TFS-123456"` |
| vehicle_asset_id | Vehicle asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| vehicle_description | Vehicle description | VARCHAR(255) | `"2022 Toyota Camry XLE"` |
| is_lease | Lease flag | BOOLEAN | `false` |
| down_payment | Down payment amount | DECIMAL(15,2) | `5000.00` |
| trade_in_value | Trade-in value | DECIMAL(15,2) | `15000.00` |
| lease_term_months | Lease term (months) | INTEGER | `null` |
| mileage_limit_annual | Annual mileage limit | INTEGER | `null` |
| excess_mileage_charge | Excess mileage charge/mile | DECIMAL(5,2) | `null` |
| residual_value | Lease residual value | DECIMAL(15,2) | `null` |
| purchase_option_available | Purchase option flag | BOOLEAN | `false` |
| has_gap_insurance | GAP insurance flag | BOOLEAN | `true` |
| gap_insurance_cost | GAP insurance cost | DECIMAL(15,2) | `500.00` |

### Liability Personal Loans

**Purpose**: Stores personal loan information from banks, credit unions, online lenders, and peer-to-peer platforms.

**Usage**: Tracks personal loans, their purposes, collateral, and cosigner information.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| lender_name | Lender name | VARCHAR(255) | `"LendingClub"` |
| loan_number | Loan number | VARCHAR(100) | `"LC-789012"` |
| lender_type | Type of lender | VARCHAR(50) | `"peer_to_peer"` |
| loan_purpose | Purpose of loan | VARCHAR(100) | `"Debt consolidation"` |
| is_secured | Secured loan flag | BOOLEAN | `false` |
| collateral_description | Collateral description | TEXT | `null` |
| collateral_value | Collateral value | DECIMAL(15,2) | `null` |
| has_cosigner | Cosigner flag | BOOLEAN | `false` |
| cosigner_name | Cosigner name | VARCHAR(255) | `null` |
| cosigner_relationship | Cosigner relationship | VARCHAR(50) | `null` |
| has_prepayment_penalty | Prepayment penalty flag | BOOLEAN | `false` |
| prepayment_penalty_amount | Prepayment penalty | DECIMAL(15,2) | `null` |
| prepayment_penalty_end_date | Penalty end date | DATE | `null` |

### Liability Business Loans

**Purpose**: Stores business loan information including SBA loans, lines of credit, and commercial loans.

**Usage**: Tracks business debt, collateral, personal guarantees, and credit facilities.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| lender_name | Lender name | VARCHAR(255) | `"Bank of America"` |
| loan_number | Loan number | VARCHAR(100) | `"BOA-BUS-456789"` |
| loan_program | Loan program type | VARCHAR(100) | `"SBA 7(a)"` |
| business_name | Business name | VARCHAR(255) | `"ABC Technologies LLC"` |
| business_asset_id | Business asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| ein | Business EIN | VARCHAR(20) | `"12-3456789"` |
| is_line_of_credit | Line of credit flag | BOOLEAN | `false` |
| credit_limit | Credit limit (if LOC) | DECIMAL(15,2) | `null` |
| draw_period_end_date | Draw period end date | DATE | `null` |
| is_secured | Secured loan flag | BOOLEAN | `true` |
| collateral_type | Type of collateral | VARCHAR(100) | `"Business equipment"` |
| collateral_value | Collateral value | DECIMAL(15,2) | `250000.00` |
| has_personal_guarantee | Personal guarantee flag | BOOLEAN | `true` |
| guarantor_names | Guarantor names | TEXT | `"John Smith, Jane Smith"` |
| is_sba_loan | SBA loan flag | BOOLEAN | `true` |
| sba_loan_number | SBA loan number | VARCHAR(50) | `"SBA-2023-001"` |
| sba_guarantee_percentage | SBA guarantee % | DECIMAL(5,2) | `75.00` |

### Liability Taxes

**Purpose**: Stores tax liabilities including federal, state, and local tax debts with payment plans.

**Usage**: Tracks tax obligations, payment arrangements, liens, and interest/penalties.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| tax_authority | Tax authority | VARCHAR(100) | `"IRS"` |
| tax_type | Type of tax | VARCHAR(50) | `"income"` |
| tax_year | Tax year | INTEGER | `2022` |
| assessment_date | Assessment date | DATE | `2023-04-15` |
| assessment_number | Assessment number | VARCHAR(50) | `"2023-IRS-123456"` |
| has_payment_plan | Payment plan flag | BOOLEAN | `true` |
| payment_plan_type | Payment plan type | VARCHAR(50) | `"installment_agreement"` |
| number_of_payments | Number of payments | INTEGER | `24` |
| original_tax_amount | Original tax amount | DECIMAL(15,2) | `15000.00` |
| penalties_amount | Penalties amount | DECIMAL(15,2) | `1500.00` |
| interest_amount | Interest amount | DECIMAL(15,2) | `750.00` |
| has_lien | Tax lien flag | BOOLEAN | `false` |
| lien_date | Lien filing date | DATE | `null` |
| lien_release_date | Lien release date | DATE | `null` |

### Liability Other

**Purpose**: Flexible table for miscellaneous liabilities that don't fit standard categories.

**Usage**: Captures unique debt types with custom attributes stored in JSON format.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| liability_id | Liability reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_type | Type of liability | VARCHAR(100) | `"legal_settlement"` |
| creditor_name | Creditor name | VARCHAR(255) | `"ABC Law Firm"` |
| account_number | Account number | VARCHAR(100) | `"CASE-2023-789"` |
| is_secured | Secured debt flag | BOOLEAN | `false` |
| collateral_description | Collateral description | TEXT | `null` |
| details | Additional details (JSON) | JSONB | `{"case_number": "CV-2023-001", "court": "District Court"}` |

### Liability Payments

**Purpose**: Tracks payment history for all liabilities including principal, interest, and fee breakdowns.

**Usage**: Records all payments made, tracks late payments, and maintains payment confirmation details.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| payment_date | Payment date | DATE | `2024-01-15` |
| scheduled_payment | Scheduled payment amount | DECIMAL(15,2) | `1500.00` |
| actual_payment | Actual payment amount | DECIMAL(15,2) | `1500.00` |
| principal_payment | Principal portion | DECIMAL(15,2) | `1200.00` |
| interest_payment | Interest portion | DECIMAL(15,2) | `280.00` |
| fees_payment | Fees portion | DECIMAL(15,2) | `20.00` |
| escrow_payment | Escrow portion | DECIMAL(15,2) | `0.00` |
| balance_before | Balance before payment | DECIMAL(15,2) | `180000.00` |
| balance_after | Balance after payment | DECIMAL(15,2) | `178800.00` |
| payment_method | Payment method | VARCHAR(50) | `"auto_debit"` |
| confirmation_number | Confirmation number | VARCHAR(100) | `"PAY-2024-0115-001"` |
| is_late | Late payment flag | BOOLEAN | `false` |
| days_late | Days late | INTEGER | `0` |
| late_fee_charged | Late fee amount | DECIMAL(15,2) | `0.00` |
| notes | Payment notes | TEXT | `null` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:00:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Liability Rate Changes

**Purpose**: Tracks interest rate changes over time for variable rate loans and credit cards.

**Usage**: Maintains history of rate adjustments for ARMs, variable loans, and promotional rates.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| effective_date | Effective date | DATE | `2024-02-01` |
| old_rate | Previous rate % | DECIMAL(7,4) | `6.5000` |
| new_rate | New rate % | DECIMAL(7,4) | `7.2500` |
| rate_type | Type of rate | VARCHAR(50) | `"purchase"` |
| change_reason | Reason for change | VARCHAR(100) | `"Index adjustment"` |
| is_promotional | Promotional rate flag | BOOLEAN | `false` |
| promotional_end_date | Promo end date | DATE | `null` |
| index_value | Index value (ARMs) | DECIMAL(7,4) | `5.2500` |
| margin_value | Margin value (ARMs) | DECIMAL(7,4) | `2.0000` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-20 09:00:00+00` |
| created_by | Creating user/system | UUID | `********-0000-0000-0000-********0000` |

## Income

**Purpose**: Base table for all income sources. Stores common income information with specific details in child tables.

**Usage**: Central repository for tracking all income including employment, business, investment, and other income sources.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| income_type | Type of income | income_type | `"employment"` |
| name | Income source name | VARCHAR(255) | `"Acme Corp Salary"` |
| description | Income description | TEXT | `"Full-time employment income"` |
| gross_amount | Gross income amount | DECIMAL(15,2) | `8333.33` |
| net_amount | Net income amount | DECIMAL(15,2) | `6250.00` |
| frequency | Payment frequency | income_frequency | `"monthly"` |
| is_taxable | Taxable income flag | BOOLEAN | `true` |
| tax_rate | Effective tax rate % | DECIMAL(5,2) | `25.00` |
| start_date | Income start date | DATE | `2020-01-15` |
| end_date | Income end date | DATE | `null` |
| next_payment_date | Next payment date | DATE | `2024-02-01` |
| is_active | Active status | BOOLEAN | `true` |
| is_recurring | Recurring income flag | BOOLEAN | `true` |
| metadata | Additional data | JSONB | `{"direct_deposit": true}` |
| tags | Income tags | TEXT[] | `["primary", "stable"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Income Employment

**Purpose**: Stores employment-specific income details including employer information, compensation structure, and benefits.

**Usage**: Extends income table with employment details for W-2 employees and contractors.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| employer_name | Employer name | VARCHAR(255) | `"Acme Corporation"` |
| employer_address | Employer address | TEXT | `"123 Business Blvd"` |
| employer_phone | Employer phone | VARCHAR(50) | `"************"` |
| job_title | Job title | VARCHAR(255) | `"Senior Software Engineer"` |
| department | Department | VARCHAR(255) | `"Engineering"` |
| employee_id | Employee ID | VARCHAR(100) | `"EMP12345"` |
| employment_type | Employment type | employment_type | `"full_time"` |
| employment_status | Employment status | employment_status | `"active"` |
| base_salary | Annual base salary | DECIMAL(15,2) | `100000.00` |
| hourly_rate | Hourly rate | DECIMAL(10,2) | `null` |
| hours_per_week | Hours per week | DECIMAL(5,2) | `40.00` |
| bonus_annual | Expected annual bonus | DECIMAL(15,2) | `15000.00` |
| commission_rate | Commission rate % | DECIMAL(5,2) | `0.00` |
| overtime_rate | Overtime rate | DECIMAL(10,2) | `null` |
| benefits_annual_value | Annual benefits value | DECIMAL(15,2) | `20000.00` |
| tax_status | Tax classification | tax_status | `"employee"` |
| w4_allowances | W-4 allowances | INTEGER | `2` |
| additional_withholding | Extra withholding | DECIMAL(10,2) | `0.00` |

### Income Business

**Purpose**: Stores business income information for self-employed individuals and business owners.

**Usage**: Tracks business revenue, expenses, and profit for various business structures.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| business_name | Business name | VARCHAR(255) | `"Smith Consulting LLC"` |
| business_type | Type of business | VARCHAR(255) | `"IT Consulting"` |
| business_structure | Legal structure | business_structure | `"llc"` |
| ein | Employer ID number | VARCHAR(20) | `"12-3456789"` |
| gross_revenue | Gross revenue | DECIMAL(15,2) | `150000.00` |
| operating_expenses | Operating expenses | DECIMAL(15,2) | `50000.00` |
| net_profit | Net profit | DECIMAL(15,2) | `100000.00` |
| ownership_percentage | Ownership percentage | DECIMAL(5,2) | `100.00` |
| quarterly_estimated_tax | Quarterly tax payment | DECIMAL(10,2) | `7500.00` |
| business_use_of_home | Home office flag | BOOLEAN | `true` |
| home_office_percentage | Home office % | DECIMAL(5,2) | `15.00` |

### Income Investment

**Purpose**: Stores investment income details including dividends, interest, and capital gains.

**Usage**: Tracks income generated from investment assets and categorizes for tax purposes.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| source_asset_id | Source asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| investment_type | Type of investment | VARCHAR(100) | `"dividend_stocks"` |
| dividend_income | Dividend income | DECIMAL(15,2) | `5000.00` |
| interest_income | Interest income | DECIMAL(15,2) | `1000.00` |
| capital_gains_short | Short-term gains | DECIMAL(15,2) | `2000.00` |
| capital_gains_long | Long-term gains | DECIMAL(15,2) | `10000.00` |
| distribution_frequency | Distribution frequency | payment_frequency | `"quarterly"` |
| reinvested | Reinvestment flag | BOOLEAN | `true` |
| qualified_dividends | Qualified dividends | DECIMAL(15,2) | `4500.00` |
| tax_exempt_interest | Tax-exempt interest | DECIMAL(15,2) | `0.00` |

### Income Rental

**Purpose**: Stores rental property income with associated property and expense tracking.

**Usage**: Tracks rental income, occupancy rates, and property-specific income details.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| property_asset_id | Property asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| property_address | Property address | TEXT | `"123 Rental St, City, ST 12345"` |
| number_of_units | Number of rental units | INTEGER | `1` |
| occupied_units | Currently occupied units | INTEGER | `1` |
| monthly_rent_per_unit | Rent per unit | DECIMAL(10,2) | `2500.00` |
| lease_start_date | Lease start date | DATE | `2023-01-01` |
| lease_end_date | Lease end date | DATE | `2024-12-31` |
| security_deposit_held | Security deposit amount | DECIMAL(10,2) | `2500.00` |
| last_rent_increase_date | Last increase date | DATE | `2023-01-01` |
| next_rent_increase_date | Next increase date | DATE | `2024-01-01` |
| property_management_fee | Management fee | DECIMAL(10,2) | `250.00` |
| property_management_company | Management company | VARCHAR(255) | `"ABC Property Management"` |
| includes_utilities | Utilities included | BOOLEAN | `false` |
| tenant_pays_utilities | Tenant pays utilities | BOOLEAN | `true` |
| maintenance_reserve_monthly | Monthly maintenance reserve | DECIMAL(10,2) | `200.00` |

### Income Retirement

**Purpose**: Stores retirement income including pensions, 401k distributions, and Social Security.

**Usage**: Tracks various retirement income sources with tax withholding and survivor benefit information.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| retirement_type | Type of retirement income | retirement_income_type | `"pension"` |
| account_number | Account number | VARCHAR(100) | `"****5678"` |
| institution_name | Financial institution | VARCHAR(255) | `"Fidelity"` |
| account_number_last4 | Last 4 of account | VARCHAR(4) | `"5678"` |
| is_defined_benefit | Defined benefit flag | BOOLEAN | `true` |
| years_of_service | Years of service | INTEGER | `25` |
| full_retirement_age | Full retirement age | DATE | `2035-05-15` |
| benefit_start_age | Benefit start age | INTEGER | `65` |
| is_survivor_benefit | Survivor benefit flag | BOOLEAN | `false` |
| is_required_distribution | RMD flag | BOOLEAN | `false` |
| has_cola | COLA adjustment flag | BOOLEAN | `true` |
| cola_percentage | COLA percentage | DECIMAL(5,2) | `2.00` |
| has_survivor_benefit | Has survivor benefit | BOOLEAN | `true` |
| survivor_benefit_percentage | Survivor benefit % | DECIMAL(5,2) | `50.00` |
| federal_tax_withheld | Federal tax withheld | DECIMAL(15,2) | `500.00` |
| state_tax_withheld | State tax withheld | DECIMAL(15,2) | `100.00` |
| withholding_allowances | W-4 allowances | INTEGER | `2` |

### Income Support

**Purpose**: Stores alimony and child support income with court order details.

**Usage**: Tracks support payments, enforcement details, and tax treatment.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| support_type | Type of support | VARCHAR(20) | `"child_support"` |
| payer_name | Payer name | VARCHAR(255) | `"John Doe"` |
| payer_ssn_last4 | Payer SSN last 4 | VARCHAR(4) | `"1234"` |
| court_order_number | Court order number | VARCHAR(50) | `"CV-2020-1234"` |
| court_order_date | Court order date | DATE | `2020-06-15` |
| court_name | Court name | VARCHAR(255) | `"Superior Court of County"` |
| monthly_amount | Monthly amount | DECIMAL(15,2) | `1500.00` |
| payment_start_date | Payment start date | DATE | `2020-07-01` |
| payment_end_date | Payment end date | DATE | `2038-05-15` |
| number_of_children | Number of children | INTEGER | `2` |
| children_names | Children names | TEXT | `"Jane Doe, John Doe Jr."` |
| modification_date | Last modification date | DATE | `null` |
| is_garnishment | Garnishment flag | BOOLEAN | `false` |
| garnishment_percentage | Garnishment % | DECIMAL(5,2) | `0.00` |
| arrears_amount | Arrears amount | DECIMAL(15,2) | `0.00` |
| is_taxable_to_recipient | Taxable to recipient | BOOLEAN | `false` |
| is_deductible_to_payer | Deductible to payer | BOOLEAN | `false` |

### Income Other

**Purpose**: Flexible table for miscellaneous income types not covered by specific tables.

**Usage**: Stores various other income sources with customizable fields in JSON.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| income_id | Income reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| income_type | Type of income | VARCHAR(100) | `"royalty"` |
| source_name | Source name | VARCHAR(255) | `"Book Publisher LLC"` |
| source_description | Source description | TEXT | `"Royalties from published book"` |
| is_recurring | Recurring flag | BOOLEAN | `true` |
| payment_method | Payment method | VARCHAR(50) | `"direct_deposit"` |
| requires_1099 | Requires 1099 flag | BOOLEAN | `true` |
| form_type | Tax form type | VARCHAR(20) | `"1099-MISC"` |
| details | Additional details | JSONB | `{"isbn": "978-*********0", "royalty_rate": "10%"}` |

### Income Paystubs

**Purpose**: Stores detailed paystub information for employment income verification.

**Usage**: Tracks individual paystubs with earnings, deductions, and YTD totals.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| income_id | Income reference | UUID | `123e4567-e89b-12d3-a456-************` |
| pay_period_start | Period start date | DATE | `2024-01-01` |
| pay_period_end | Period end date | DATE | `2024-01-15` |
| pay_date | Payment date | DATE | `2024-01-19` |
| regular_hours | Regular hours worked | DECIMAL(5,2) | `80.00` |
| regular_earnings | Regular earnings | DECIMAL(15,2) | `4000.00` |
| overtime_hours | Overtime hours | DECIMAL(5,2) | `5.00` |
| overtime_earnings | Overtime earnings | DECIMAL(15,2) | `375.00` |
| other_earnings | Other earnings | DECIMAL(15,2) | `500.00` |
| gross_pay | Gross pay | DECIMAL(15,2) | `4875.00` |
| ytd_gross | YTD gross pay | DECIMAL(15,2) | `4875.00` |
| ytd_net | YTD net pay | DECIMAL(15,2) | `3656.25` |
| document_url | Document URL | TEXT | `"https://secure.example.com/paystubs/2024-01-19.pdf"` |
| is_verified | Verified flag | BOOLEAN | `true` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-19 10:00:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Income Paystub Deductions

**Purpose**: Itemizes deductions from paystubs including taxes, insurance, and retirement contributions.

**Usage**: Detailed breakdown of all paystub deductions for tax and benefit tracking.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| paystub_id | Paystub reference | UUID | `123e4567-e89b-12d3-a456-************` |
| deduction_type | Type of deduction | VARCHAR(50) | `"federal_tax"` |
| description | Deduction description | VARCHAR(255) | `"Federal Income Tax"` |
| current_amount | Current period amount | DECIMAL(15,2) | `487.50` |
| ytd_amount | YTD amount | DECIMAL(15,2) | `487.50` |
| is_pretax | Pre-tax deduction flag | BOOLEAN | `false` |
| employer_contribution | Employer contribution | DECIMAL(15,2) | `0.00` |

### Income Tax Forms

**Purpose**: Stores tax form data (W-2, 1099, etc.) for income documentation.

**Usage**: Maintains tax documents with flexible JSON storage for various form types.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| income_id | Income reference | UUID | `123e4567-e89b-12d3-a456-************` |
| tax_year | Tax year | INTEGER | `2023` |
| form_type | Form type | VARCHAR(20) | `"W2"` |
| payer_name | Payer name | VARCHAR(255) | `"Acme Corporation"` |
| payer_ein | Payer EIN | VARCHAR(20) | `"12-3456789"` |
| form_data | Form data in JSON | JSONB | `{"box1": 100000, "box2": 22000, "box3": 100000}` |
| document_url | Document URL | TEXT | `"https://secure.example.com/tax-forms/2023-W2.pdf"` |
| is_amended | Amended form flag | BOOLEAN | `false` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-31 10:00:00+00` |
| uploaded_by | Uploading user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Income Projections

**Purpose**: Stores projected future income for planning and forecasting.

**Usage**: Tracks income projections with confidence levels and assumptions.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| income_id | Income reference | UUID | `123e4567-e89b-12d3-a456-************` |
| projection_year | Projection year | INTEGER | `2025` |
| projection_month | Projection month | INTEGER | `null` |
| projected_gross | Projected gross amount | DECIMAL(15,2) | `110000.00` |
| projected_net | Projected net amount | DECIMAL(15,2) | `82500.00` |
| confidence_level | Confidence level % | DECIMAL(5,2) | `85.00` |
| notes | Projection notes | TEXT | `"Assumes 3% annual raise"` |
| inflation_rate | Inflation rate % | DECIMAL(5,2) | `2.50` |
| growth_rate | Growth rate % | DECIMAL(5,2) | `3.00` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:00:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

## Expenses

**Purpose**: Base table for all expense types. Stores common expense information for budgeting and cash flow analysis.

**Usage**: Central repository for tracking all expenses including housing, transportation, food, and other spending.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_type | Type of expense | expense_type | `"housing"` |
| name | Expense name | VARCHAR(255) | `"Monthly Rent"` |
| description | Expense description | TEXT | `"Apartment rent payment"` |
| amount | Expense amount | DECIMAL(15,2) | `2000.00` |
| frequency | Payment frequency | expense_frequency | `"monthly"` |
| category | Expense category | VARCHAR(100) | `"rent"` |
| subcategory | Expense subcategory | VARCHAR(100) | `"primary_residence"` |
| necessity | Necessity level | expense_necessity | `"essential"` |
| tax_deductible | Tax deductibility | tax_deductibility | `"not_deductible"` |
| start_date | Expense start date | DATE | `2023-01-01` |
| end_date | Expense end date | DATE | `null` |
| next_payment_date | Next payment date | DATE | `2024-02-01` |
| payee_name | Payee name | VARCHAR(255) | `"ABC Property Management"` |
| payment_method | Payment method | VARCHAR(50) | `"auto_debit"` |
| auto_pay | Auto-pay enabled | BOOLEAN | `true` |
| is_active | Active status | BOOLEAN | `true` |
| is_recurring | Recurring expense flag | BOOLEAN | `true` |
| metadata | Additional data | JSONB | `{"lease_end": "2024-12-31"}` |
| tags | Expense tags | TEXT[] | `["fixed", "housing"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Expense Housing

**Purpose**: Stores housing-specific expense details including rent, mortgage payments, and property-related costs.

**Usage**: Extends expense table with housing-specific information for rent and property expenses.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| housing_expense_type | Type of housing expense | VARCHAR(50) | `"rent"` |
| property_asset_id | Related property asset | UUID | `null` |
| is_rent | Rent payment flag | BOOLEAN | `true` |
| lease_end_date | Lease end date | DATE | `2024-12-31` |
| security_deposit | Security deposit amount | DECIMAL(10,2) | `2000.00` |
| includes_utilities | Utilities included flag | BOOLEAN | `false` |
| utility_allowance | Utility allowance amount | DECIMAL(10,2) | `0.00` |

### Expense Transportation

**Purpose**: Stores transportation-related expenses including vehicle payments, insurance, and maintenance.

**Usage**: Tracks all transportation costs associated with vehicles or public transit.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| transportation_type | Type of transportation expense | VARCHAR(50) | `"car_payment"` |
| vehicle_asset_id | Vehicle asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| vehicle_description | Vehicle description | VARCHAR(255) | `"2022 Toyota Camry"` |
| vehicle_loan_id | Vehicle loan reference | UUID | `123e4567-e89b-12d3-a456-************` |
| insurance_policy_id | Insurance policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| gas_octane_level | Gas octane level | VARCHAR(20) | `"regular"` |
| average_mpg | Average MPG | DECIMAL(5,2) | `28.50` |
| monthly_mileage | Monthly mileage | INTEGER | `1000` |
| service_provider | Service provider name | VARCHAR(255) | `"Toyota Service Center"` |
| transit_pass_type | Transit pass type | VARCHAR(50) | `null` |
| transit_zones | Transit zones covered | VARCHAR(100) | `null` |

### Expense Healthcare

**Purpose**: Stores healthcare expenses including medical, dental, vision, and prescriptions.

**Usage**: Tracks healthcare costs for tax deductions and HSA/FSA reimbursements.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| healthcare_type | Type of healthcare expense | VARCHAR(50) | `"medical"` |
| provider_name | Healthcare provider | VARCHAR(255) | `"City Medical Center"` |
| provider_npi | Provider NPI number | VARCHAR(20) | `"*********0"` |
| patient_name | Patient name | VARCHAR(255) | `"Jane Smith"` |
| service_date | Date of service | DATE | `2024-01-15` |
| diagnosis_code | Diagnosis code | VARCHAR(20) | `"Z00.00"` |
| procedure_code | Procedure code | VARCHAR(20) | `"99213"` |
| insurance_covered_amount | Insurance covered | DECIMAL(15,2) | `150.00` |
| insurance_policy_id | Insurance policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| is_hsa_eligible | HSA eligible flag | BOOLEAN | `true` |
| is_fsa_eligible | FSA eligible flag | BOOLEAN | `true` |
| prescription_name | Prescription name | VARCHAR(255) | `null` |
| prescription_generic | Generic drug flag | BOOLEAN | `null` |
| refills_remaining | Refills remaining | INTEGER | `null` |

### Expense Insurance

**Purpose**: Stores insurance premium expenses separate from the insurance policies themselves.

**Usage**: Tracks insurance premium payments and links to insurance policies.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| insurance_type | Type of insurance | insurance_type | `"auto"` |
| policy_id | Insurance policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_number | Policy number | VARCHAR(100) | `"AUTO-123456"` |
| insurance_company | Insurance company | VARCHAR(255) | `"State Farm"` |
| coverage_period_start | Coverage start | DATE | `2024-01-01` |
| coverage_period_end | Coverage end | DATE | `2024-06-30` |
| is_employer_paid | Employer paid flag | BOOLEAN | `false` |
| employer_contribution | Employer contribution | DECIMAL(10,2) | `0.00` |
| is_pre_tax | Pre-tax payment flag | BOOLEAN | `false` |

### Expense Utilities

**Purpose**: Stores utility expenses including electricity, gas, water, internet, and phone.

**Usage**: Tracks monthly utility costs for budgeting and property expense allocation.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| utility_type | Type of utility | VARCHAR(50) | `"electricity"` |
| provider_name | Utility provider | VARCHAR(255) | `"City Power Company"` |
| account_number | Account number | VARCHAR(100) | `"****5678"` |
| service_address | Service address | TEXT | `"123 Main St, City, ST 12345"` |
| is_budget_billing | Budget billing flag | BOOLEAN | `true` |
| average_usage | Average monthly usage | DECIMAL(10,2) | `850.00` |
| usage_unit | Usage unit | VARCHAR(20) | `"kWh"` |
| rate_per_unit | Rate per unit | DECIMAL(10,4) | `0.1200` |
| property_asset_id | Property reference | UUID | `123e4567-e89b-12d3-a456-************` |
| is_rental_property | Rental property flag | BOOLEAN | `false` |
| tenant_reimbursed | Tenant reimbursed flag | BOOLEAN | `false` |

### Expense Education

**Purpose**: Stores education expenses including tuition, books, and supplies.

**Usage**: Tracks education costs for tax credits and 529 plan distributions.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| education_type | Type of education expense | VARCHAR(50) | `"tuition"` |
| institution_name | Educational institution | VARCHAR(255) | `"State University"` |
| institution_id | Institution ID | VARCHAR(50) | `"123456"` |
| student_name | Student name | VARCHAR(255) | `"John Smith Jr."` |
| student_id | Student ID | VARCHAR(50) | `"STU123456"` |
| academic_period | Academic period | VARCHAR(50) | `"Fall 2024"` |
| degree_program | Degree program | VARCHAR(255) | `"Bachelor of Science"` |
| is_qualified_expense | Qualified expense flag | BOOLEAN | `true` |
| is_529_eligible | 529 eligible flag | BOOLEAN | `true` |
| covered_by_scholarship | Scholarship amount | DECIMAL(15,2) | `5000.00` |
| covered_by_grants | Grant amount | DECIMAL(15,2) | `2500.00` |
| tax_form_1098t | Has 1098-T flag | BOOLEAN | `true` |

### Expense Childcare

**Purpose**: Stores childcare expenses including daycare, after-school care, and summer camps.

**Usage**: Tracks childcare costs for dependent care FSA and tax credits.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| childcare_type | Type of childcare | VARCHAR(50) | `"daycare"` |
| provider_name | Childcare provider | VARCHAR(255) | `"Happy Kids Daycare"` |
| provider_tin | Provider TIN | VARCHAR(20) | `"12-3456789"` |
| provider_license | Provider license # | VARCHAR(50) | `"LIC123456"` |
| child_name | Child name | VARCHAR(255) | `"Jane Smith"` |
| child_age | Child age | INTEGER | `4` |
| hours_per_week | Hours per week | DECIMAL(5,2) | `40.00` |
| is_before_after_school | Before/after school flag | BOOLEAN | `false` |
| is_summer_program | Summer program flag | BOOLEAN | `false` |
| is_overnight_care | Overnight care flag | BOOLEAN | `false` |
| is_special_needs | Special needs care flag | BOOLEAN | `false` |
| fsa_reimbursable | FSA reimbursable flag | BOOLEAN | `true` |
| tax_credit_eligible | Tax credit eligible flag | BOOLEAN | `true` |

### Expense Debt Payments

**Purpose**: Links debt payments to their corresponding liabilities.

**Usage**: Tracks debt payments separately from the underlying liabilities.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| payment_type | Type of payment | VARCHAR(50) | `"regular"` |
| principal_amount | Principal portion | DECIMAL(15,2) | `1500.00` |
| interest_amount | Interest portion | DECIMAL(15,2) | `500.00` |
| escrow_amount | Escrow portion | DECIMAL(15,2) | `300.00` |
| fees_amount | Fees portion | DECIMAL(15,2) | `0.00` |
| is_extra_payment | Extra payment flag | BOOLEAN | `false` |
| remaining_balance | Balance after payment | DECIMAL(15,2) | `248500.00` |

### Expense Savings Investments

**Purpose**: Stores contributions to savings and investment accounts.

**Usage**: Tracks regular contributions to various savings and investment vehicles.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| investment_type | Type of investment | VARCHAR(50) | `"401k"` |
| account_id | Account reference | UUID | `123e4567-e89b-12d3-a456-************` |
| account_name | Account name | VARCHAR(255) | `"Company 401(k)"` |
| institution_name | Financial institution | VARCHAR(255) | `"Fidelity"` |
| contribution_type | Contribution type | VARCHAR(50) | `"employee"` |
| is_pre_tax | Pre-tax contribution flag | BOOLEAN | `true` |
| employer_match | Employer match amount | DECIMAL(15,2) | `250.00` |
| investment_allocation | Investment allocation | JSONB | `{"stocks": 70, "bonds": 30}` |
| is_automatic | Automatic investment flag | BOOLEAN | `true` |
| vesting_percentage | Current vesting % | DECIMAL(5,2) | `60.00` |

### Expense Charitable

**Purpose**: Stores charitable contributions and donations.

**Usage**: Tracks charitable giving for tax deductions and personal records.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| charity_name | Charity name | VARCHAR(255) | `"Red Cross"` |
| charity_ein | Charity EIN | VARCHAR(20) | `"53-0196605"` |
| donation_type | Type of donation | VARCHAR(50) | `"cash"` |
| is_tax_deductible | Tax deductible flag | BOOLEAN | `true` |
| receipt_received | Receipt received flag | BOOLEAN | `true` |
| receipt_number | Receipt number | VARCHAR(100) | `"RC2024-1234"` |
| in_kind_description | In-kind description | TEXT | `null` |
| in_kind_fair_market_value | FMV of in-kind donation | DECIMAL(15,2) | `null` |
| volunteer_hours | Volunteer hours | DECIMAL(10,2) | `null` |
| volunteer_mileage | Volunteer mileage | DECIMAL(10,2) | `null` |

### Expense Other

**Purpose**: Flexible table for miscellaneous expenses not covered by specific tables.

**Usage**: Stores various other expenses with customizable fields.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| expense_id | Expense reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_subtype | Expense subtype | VARCHAR(100) | `"pet_care"` |
| vendor_name | Vendor name | VARCHAR(255) | `"City Pet Hospital"` |
| item_description | Item description | TEXT | `"Annual pet checkup and vaccinations"` |
| quantity | Quantity | DECIMAL(10,2) | `1.00` |
| unit_price | Unit price | DECIMAL(15,2) | `250.00` |
| is_reimbursable | Reimbursable flag | BOOLEAN | `false` |
| reimbursed_amount | Reimbursed amount | DECIMAL(15,2) | `0.00` |
| details | Additional details | JSONB | `{"pet_name": "Max", "service_type": "veterinary"}` |

### Expense Transactions

**Purpose**: Stores individual expense transactions for detailed tracking.

**Usage**: Records actual payment transactions linked to expense records.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_id | Expense reference | UUID | `123e4567-e89b-12d3-a456-************` |
| transaction_date | Transaction date | DATE | `2024-01-15` |
| amount | Transaction amount | DECIMAL(15,2) | `2000.00` |
| payment_method | Payment method | payment_method | `"credit_card"` |
| payment_reference | Payment reference # | VARCHAR(100) | `"CHK-1234"` |
| merchant_name | Merchant name | VARCHAR(255) | `"ABC Property Management"` |
| merchant_category | Merchant category | VARCHAR(100) | `"Real Estate"` |
| is_recurring_instance | Recurring instance flag | BOOLEAN | `true` |
| notes | Transaction notes | TEXT | `"January rent payment"` |
| receipt_url | Receipt URL | TEXT | `"https://receipts.example.com/2024/01/rent.pdf"` |
| is_disputed | Disputed flag | BOOLEAN | `false` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:00:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Expense Budgets

**Purpose**: Stores budget targets for expense categories.

**Usage**: Tracks budgeted amounts vs actual expenses for financial planning.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| budget_year | Budget year | INTEGER | `2024` |
| budget_month | Budget month | INTEGER | `1` |
| category | Expense category | expense_category | `"housing"` |
| budgeted_amount | Budgeted amount | DECIMAL(15,2) | `3000.00` |
| actual_amount | Actual amount spent | DECIMAL(15,2) | `2800.00` |
| variance_amount | Variance (actual - budget) | DECIMAL(15,2) | `-200.00` |
| variance_percentage | Variance percentage | DECIMAL(5,2) | `-6.67` |
| notes | Budget notes | TEXT | `"Under budget due to energy savings"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-01 00:00:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-31 23:59:59+00` |
| updated_by | Updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

## Insurance Policies

**Purpose**: Base table for all insurance policies. Stores common policy information with specific details in child tables.

**Usage**: Central repository for tracking all insurance policies including life, disability, property, and other coverage.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| insurance_type | Type of insurance | insurance_type | `"life"` |
| policy_number | Policy number | VARCHAR(100) | `"LIF-*********"` |
| name | Policy name | VARCHAR(255) | `"Term Life Insurance"` |
| description | Policy description | TEXT | `"20-year term life policy"` |
| insurer_name | Insurance company | VARCHAR(255) | `"Prudential"` |
| agent_name | Agent name | VARCHAR(255) | `"Jane Smith"` |
| agent_phone | Agent phone | VARCHAR(50) | `"************"` |
| agent_email | Agent email | VARCHAR(255) | `"<EMAIL>"` |
| coverage_amount | Coverage amount | DECIMAL(15,2) | `1000000.00` |
| deductible | Policy deductible | DECIMAL(10,2) | `0.00` |
| premium_amount | Premium amount | DECIMAL(10,2) | `150.00` |
| premium_frequency | Premium frequency | premium_frequency | `"monthly"` |
| effective_date | Policy start date | DATE | `2020-01-01` |
| expiration_date | Policy end date | DATE | `2040-01-01` |
| renewal_date | Next renewal date | DATE | `2025-01-01` |
| policy_status | Policy status | policy_status | `"active"` |
| auto_renew | Auto-renewal flag | BOOLEAN | `true` |
| beneficiaries | Beneficiary information | JSONB | `[{"name": "Jane Smith", "percentage": 100}]` |
| metadata | Additional data | JSONB | `{"underwriting_class": "preferred"}` |
| tags | Policy tags | TEXT[] | `["term", "primary"]` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| is_deleted | Soft delete flag | BOOLEAN | `false` |
| deleted_at | Deletion timestamp | TIMESTAMPTZ | `null` |
| deleted_by | Deleting user ID | UUID | `null` |
| version | Version number | INTEGER | `1` |

### Insurance Life

**Purpose**: Stores life insurance specific details including death benefits, cash values, and policy riders.

**Usage**: Extends insurance policies table with life insurance specific features and benefits.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| policy_id | Policy reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| life_insurance_type | Type of life insurance | life_insurance_type | `"term"` |
| death_benefit | Death benefit amount | DECIMAL(15,2) | `1000000.00` |
| cash_value | Current cash value | DECIMAL(15,2) | `0.00` |
| surrender_value | Surrender value | DECIMAL(15,2) | `0.00` |
| term_years | Term length in years | INTEGER | `20` |
| is_convertible | Convertible policy flag | BOOLEAN | `true` |
| conversion_deadline | Conversion deadline | DATE | `2030-01-01` |
| has_waiver_of_premium | Premium waiver rider | BOOLEAN | `true` |
| has_accelerated_death_benefit | ADB rider | BOOLEAN | `true` |
| has_long_term_care_rider | LTC rider | BOOLEAN | `false` |
| additional_riders | Other riders | JSONB | `[{"name": "Child rider", "cost": 10}]` |
| loan_available | Policy loan available | BOOLEAN | `false` |
| loan_balance | Outstanding loan | DECIMAL(15,2) | `0.00` |
| loan_interest_rate | Loan interest rate | DECIMAL(5,4) | `0.0000` |

### Insurance Disability

**Purpose**: Stores disability insurance details including benefit amounts, elimination periods, and coverage features.

**Usage**: Extends insurance policies table with disability-specific coverage information.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| policy_id | Policy reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| disability_type | Type of disability | disability_type | `"long_term"` |
| monthly_benefit | Monthly benefit amount | DECIMAL(10,2) | `5000.00` |
| benefit_period | Benefit period | VARCHAR(50) | `"to_age_65"` |
| elimination_period_days | Waiting period days | INTEGER | `90` |
| income_replacement_pct | Income replacement % | DECIMAL(5,2) | `60.00` |
| maximum_monthly_benefit | Maximum benefit | DECIMAL(10,2) | `10000.00` |
| own_occupation | Own occupation coverage | BOOLEAN | `true` |
| partial_disability_coverage | Partial disability | BOOLEAN | `true` |
| cost_of_living_adjustment | COLA rider | BOOLEAN | `true` |
| future_increase_option | FIO rider | BOOLEAN | `true` |
| return_to_work_benefit | RTW benefit | BOOLEAN | `true` |
| rehabilitation_benefit | Rehab benefit | BOOLEAN | `true` |

### Insurance Additional Insureds

**Purpose**: Stores additional people covered under insurance policies beyond the primary insured.

**Usage**: Tracks family members, dependents, and other covered individuals on policies.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| external_name | External person name | VARCHAR(255) | `"John Smith Jr."` |
| relationship | Relationship to primary | VARCHAR(50) | `"son"` |
| date_of_birth | Date of birth | DATE | `2010-05-15` |
| gender | Gender | VARCHAR(20) | `"male"` |
| smoker_status | Smoker status | BOOLEAN | `false` |
| coverage_percentage | Coverage percentage | DECIMAL(5,2) | `100.00` |
| coverage_amount | Coverage amount | DECIMAL(15,2) | `null` |
| is_active | Active status | BOOLEAN | `true` |
| added_date | Date added | DATE | `2020-01-01` |
| removed_date | Date removed | DATE | `null` |
| notes | Additional notes | TEXT | `null` |

### Insurance Beneficiaries

**Purpose**: Tracks beneficiaries for life insurance and other policies requiring beneficiary designation.

**Usage**: Manages primary and contingent beneficiaries with percentage allocations.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| beneficiary_type | Primary/contingent | VARCHAR(20) | `"primary"` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| external_name | External beneficiary | VARCHAR(255) | `null` |
| relationship | Relationship | VARCHAR(50) | `"spouse"` |
| percentage | Benefit percentage | DECIMAL(5,2) | `100.00` |
| address | Beneficiary address | TEXT | `"123 Main St, City, ST 12345"` |
| phone | Phone number | VARCHAR(20) | `"************"` |
| email | Email address | VARCHAR(255) | `"<EMAIL>"` |
| ssn_last4 | SSN last 4 digits | VARCHAR(4) | `"1234"` |
| date_of_birth | Date of birth | DATE | `1980-01-15` |
| is_active | Active status | BOOLEAN | `true` |
| added_date | Date added | DATE | `2020-01-01` |
| removed_date | Date removed | DATE | `null` |

### Insurance Health

**Purpose**: Stores health insurance plan details including deductibles, copays, and network information.

**Usage**: Tracks health insurance coverage details for medical expense planning.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| policy_id | Policy reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| health_insurance_type | Type of health plan | health_insurance_type | `"hmo"` |
| plan_name | Plan name | VARCHAR(255) | `"Gold HMO Plan"` |
| group_number | Group number | VARCHAR(100) | `"GRP123456"` |
| individual_deductible | Individual deductible | DECIMAL(15,2) | `1500.00` |
| family_deductible | Family deductible | DECIMAL(15,2) | `3000.00` |
| individual_oop_max | Individual OOP max | DECIMAL(15,2) | `7500.00` |
| family_oop_max | Family OOP max | DECIMAL(15,2) | `15000.00` |
| copay_primary_care | Primary care copay | DECIMAL(15,2) | `25.00` |
| copay_specialist | Specialist copay | DECIMAL(15,2) | `50.00` |
| copay_urgent_care | Urgent care copay | DECIMAL(15,2) | `75.00` |
| copay_emergency | Emergency copay | DECIMAL(15,2) | `250.00` |
| coinsurance_percentage | Coinsurance % | DECIMAL(5,2) | `20.00` |
| has_prescription_coverage | Prescription coverage | BOOLEAN | `true` |
| prescription_deductible | Rx deductible | DECIMAL(15,2) | `250.00` |
| copay_generic | Generic Rx copay | DECIMAL(15,2) | `10.00` |
| copay_preferred_brand | Preferred brand copay | DECIMAL(15,2) | `35.00` |
| copay_non_preferred_brand | Non-preferred copay | DECIMAL(15,2) | `70.00` |
| copay_specialty | Specialty Rx copay | DECIMAL(15,2) | `150.00` |
| network_type | Network type | VARCHAR(50) | `"HMO"` |
| primary_care_physician | PCP name | VARCHAR(255) | `"Dr. Jane Smith"` |
| requires_referral | Referral required | BOOLEAN | `true` |
| is_hdhp | HDHP flag | BOOLEAN | `false` |
| hsa_contribution_limit | HSA contribution limit | DECIMAL(15,2) | `null` |
| employer_hsa_contribution | Employer HSA contrib | DECIMAL(15,2) | `null` |

### Insurance Property

**Purpose**: Stores property/homeowners insurance details including coverage amounts and property information.

**Usage**: Tracks property insurance for real estate assets with detailed coverage breakdowns.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| policy_id | Policy reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| property_asset_id | Property asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| property_address | Property address | TEXT | `"123 Main St, City, ST 12345"` |
| property_type | Type of property | VARCHAR(50) | `"single_family"` |
| dwelling_coverage | Dwelling coverage | DECIMAL(15,2) | `500000.00` |
| other_structures_coverage | Other structures | DECIMAL(15,2) | `50000.00` |
| personal_property_coverage | Personal property | DECIMAL(15,2) | `350000.00` |
| loss_of_use_coverage | Loss of use | DECIMAL(15,2) | `100000.00` |
| liability_coverage | Liability coverage | DECIMAL(15,2) | `300000.00` |
| medical_payments_coverage | Medical payments | DECIMAL(15,2) | `5000.00` |
| deductible_amount | Standard deductible | DECIMAL(15,2) | `2500.00` |
| hurricane_deductible | Hurricane deductible | DECIMAL(15,2) | `10000.00` |
| flood_deductible | Flood deductible | DECIMAL(15,2) | `null` |
| has_flood_coverage | Flood coverage flag | BOOLEAN | `false` |
| has_earthquake_coverage | Earthquake coverage | BOOLEAN | `false` |
| has_sewer_backup | Sewer backup coverage | BOOLEAN | `true` |
| has_identity_theft | Identity theft coverage | BOOLEAN | `true` |
| year_built | Year built | INTEGER | `2005` |
| construction_type | Construction type | VARCHAR(50) | `"frame"` |
| roof_type | Roof type | VARCHAR(50) | `"asphalt_shingle"` |
| roof_age_years | Roof age | INTEGER | `5` |
| has_alarm_system | Alarm system flag | BOOLEAN | `true` |
| has_deadbolts | Deadbolts flag | BOOLEAN | `true` |
| has_fire_sprinklers | Sprinklers flag | BOOLEAN | `false` |

### Insurance Auto

**Purpose**: Stores auto insurance policy details including coverage levels and vehicle information.

**Usage**: Tracks auto insurance coverage for vehicles with liability and comprehensive/collision details.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| policy_id | Policy reference (PK) | UUID | `123e4567-e89b-12d3-a456-************` |
| vehicle_asset_id | Vehicle asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| vehicle_description | Vehicle description | VARCHAR(255) | `"2022 Toyota Camry XLE"` |
| vin | Vehicle VIN | VARCHAR(17) | `"1HGBH41JXMN109186"` |
| liability_bodily_injury_per_person | BI per person | DECIMAL(15,2) | `250000.00` |
| liability_bodily_injury_per_accident | BI per accident | DECIMAL(15,2) | `500000.00` |
| liability_property_damage | Property damage | DECIMAL(15,2) | `100000.00` |
| has_collision | Collision coverage | BOOLEAN | `true` |
| collision_deductible | Collision deductible | DECIMAL(15,2) | `500.00` |
| has_comprehensive | Comprehensive coverage | BOOLEAN | `true` |
| comprehensive_deductible | Comp deductible | DECIMAL(15,2) | `500.00` |
| uninsured_motorist_bodily_injury | UM bodily injury | DECIMAL(15,2) | `250000.00` |
| uninsured_motorist_property_damage | UM property damage | DECIMAL(15,2) | `100000.00` |
| medical_payments_coverage | Medical payments | DECIMAL(15,2) | `5000.00` |
| has_rental_reimbursement | Rental reimbursement | BOOLEAN | `true` |
| rental_daily_limit | Rental daily limit | DECIMAL(15,2) | `50.00` |
| has_roadside_assistance | Roadside assistance | BOOLEAN | `true` |
| has_gap_coverage | GAP coverage | BOOLEAN | `false` |
| annual_mileage | Annual mileage | INTEGER | `12000` |
| primary_use | Primary use | VARCHAR(50) | `"commute"` |
| primary_driver_id | Primary driver | UUID | `123e4567-e89b-12d3-a456-************` |

### Insurance Auto Drivers

**Purpose**: Tracks drivers covered under auto insurance policies with their driving records.

**Usage**: Manages driver information for auto insurance rating and coverage.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| external_name | External driver name | VARCHAR(255) | `null` |
| date_of_birth | Date of birth | DATE | `1985-03-20` |
| license_number | License number | VARCHAR(50) | `"D*********"` |
| license_state | License state | VARCHAR(2) | `"CA"` |
| years_licensed | Years licensed | INTEGER | `15` |
| accidents_last_5_years | Recent accidents | INTEGER | `0` |
| violations_last_5_years | Recent violations | INTEGER | `1` |
| dui_convictions | DUI convictions | INTEGER | `0` |
| is_primary_driver | Primary driver flag | BOOLEAN | `true` |
| is_excluded | Excluded driver flag | BOOLEAN | `false` |

### Insurance Claims

**Purpose**: Tracks insurance claims across all policy types from filing through settlement.

**Usage**: Manages claim lifecycle, payments, and documentation for all insurance claims.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| claim_number | Claim number | VARCHAR(100) | `"CLM-2024-001234"` |
| claim_type | Type of claim | VARCHAR(50) | `"auto_collision"` |
| status | Claim status | claim_status | `"approved"` |
| incident_date | Date of incident | DATE | `2024-01-10` |
| reported_date | Date reported | DATE | `2024-01-11` |
| filed_date | Date filed | DATE | `2024-01-12` |
| approved_date | Date approved | DATE | `2024-01-20` |
| closed_date | Date closed | DATE | `2024-02-01` |
| claim_amount | Claimed amount | DECIMAL(15,2) | `8500.00` |
| deductible_amount | Deductible | DECIMAL(15,2) | `500.00` |
| approved_amount | Approved amount | DECIMAL(15,2) | `8000.00` |
| paid_amount | Amount paid | DECIMAL(15,2) | `8000.00` |
| payment_date | Payment date | DATE | `2024-02-01` |
| payment_method | Payment method | VARCHAR(50) | `"check"` |
| check_number | Check number | VARCHAR(50) | `"CHK123456"` |
| incident_description | Incident description | TEXT | `"Rear-ended at traffic light"` |
| damage_description | Damage description | TEXT | `"Rear bumper and trunk damage"` |
| documents | Claim documents | JSONB | `[{"type": "police_report", "url": "..."}]` |
| adjuster_name | Adjuster name | VARCHAR(255) | `"Mike Johnson"` |
| adjuster_phone | Adjuster phone | VARCHAR(20) | `"************"` |
| adjuster_email | Adjuster email | VARCHAR(255) | `"<EMAIL>"` |
| notes | Claim notes | TEXT | `null` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-11 14:30:00+00` |
| updated_at | Update timestamp | TIMESTAMPTZ | `2024-02-01 10:00:00+00` |
| created_by | Creating user | UUID | `123e4567-e89b-12d3-a456-************` |

### Insurance Renewals

**Purpose**: Tracks insurance policy renewals including premium changes and coverage modifications.

**Usage**: Manages renewal history and premium trends for all insurance policies.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| renewal_date | Renewal date | DATE | `2025-01-01` |
| old_premium | Previous premium | DECIMAL(15,2) | `1800.00` |
| new_premium | New premium | DECIMAL(15,2) | `1950.00` |
| premium_change_percentage | Premium change % | DECIMAL(5,2) | `8.33` |
| coverage_changes | Coverage changes | JSONB | `{"deductible": {"old": 1000, "new": 1500}}` |
| status | Renewal status | VARCHAR(50) | `"accepted"` |
| response_date | Response date | DATE | `2024-12-15` |
| change_reason | Reason for changes | TEXT | `"Annual rate adjustment and claim history"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-12-01 09:00:00+00` |
| processed_by | Processing user | UUID | `123e4567-e89b-12d3-a456-************` |

---

## Audit and History Tables

### Audit Log

**Purpose**: Records all system actions for compliance, debugging, and user activity tracking. Immutable audit trail.

**Usage**: Used for security auditing, troubleshooting, compliance reporting, and tracking user actions.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| entity_type | Type of entity | audit_entity_type | `"portfolio"` |
| entity_id | Entity identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| entity_name | Entity display name | VARCHAR(255) | `"Retirement Portfolio"` |
| action | Action performed | audit_action | `"update"` |
| action_timestamp | Action timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| action_by | Acting user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| action_by_name | Acting user name | VARCHAR(255) | `"John Smith"` |
| old_values | Previous values | JSONB | `{"total_assets": 400000}` |
| new_values | New values | JSONB | `{"total_assets": 425000}` |
| changed_fields | Modified fields | TEXT[] | `["total_assets", "updated_at"]` |
| ip_address | Client IP address | INET | `*************` |
| user_agent | Browser user agent | TEXT | `"Mozilla/5.0..."` |
| session_id | Session identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| request_id | Request identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| metadata | Additional metadata | JSONB | `{"source": "web_app"}` |
| tags | Audit tags | TEXT[] | `["high_value", "automated"]` |
| duration_ms | Operation duration | INTEGER | `125` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |

### Entity History

**Purpose**: Stores complete historical versions of entities for point-in-time recovery and historical analysis.

**Usage**: Enables viewing entity state at any point in time, tracking changes over time, and data recovery.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| entity_type | Type of entity | audit_entity_type | `"asset"` |
| entity_id | Entity identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| version | Version number | INTEGER | `5` |
| valid_from | Version start time | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| valid_to | Version end time | TIMESTAMPTZ | `null` |
| is_current | Current version flag | BOOLEAN | `true` |
| entity_data | Complete entity data | JSONB | `{"id": "...", "name": "...", ...}` |
| changed_by | Changing user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| change_reason | Change description | TEXT | `"Updated account balance"` |
| audit_log_id | Related audit entry | UUID | `123e4567-e89b-12d3-a456-************` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |

### Portfolio Snapshots

**Purpose**: Stores periodic financial snapshots of portfolios for trend analysis and reporting.

**Usage**: Used for historical reporting, performance tracking, and generating financial statements.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| portfolio_id | Portfolio reference | UUID | `123e4567-e89b-12d3-a456-************` |
| snapshot_date | Snapshot date | DATE | `2024-01-15` |
| snapshot_type | Type of snapshot | VARCHAR(50) | `"daily"` |
| total_assets | Total assets value | DECIMAL(15,2) | `500000.00` |
| total_liabilities | Total liabilities | DECIMAL(15,2) | `200000.00` |
| net_worth | Net worth | DECIMAL(15,2) | `300000.00` |
| assets_by_type | Asset breakdown | JSONB | `{"bank_account": 50000, "investment": 450000}` |
| assets_by_account | Account breakdown | JSONB | `{"checking": 25000, "401k": 300000}` |
| liabilities_by_type | Liability breakdown | JSONB | `{"mortgage": 180000, "auto_loan": 20000}` |
| liabilities_by_creditor | Creditor breakdown | JSONB | `{"Wells Fargo": 180000}` |
| monthly_income | Monthly income | DECIMAL(15,2) | `10000.00` |
| monthly_expenses | Monthly expenses | DECIMAL(15,2) | `6000.00` |
| monthly_cash_flow | Monthly cash flow | DECIMAL(15,2) | `4000.00` |
| full_snapshot | Complete snapshot data | JSONB | `{...complete portfolio state...}` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-16 00:00:00+00` |
| created_by | Creating user/system | UUID | `********-0000-0000-0000-********0000` |

### Notification History

**Purpose**: Tracks all notifications sent to users including emails, SMS, and in-app notifications.

**Usage**: Used for notification delivery tracking, troubleshooting, and ensuring regulatory compliance.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | Recipient user | UUID | `123e4567-e89b-12d3-a456-************` |
| notification_type | Type of notification | VARCHAR(100) | `"account_statement"` |
| subject | Notification subject | VARCHAR(500) | `"Your Monthly Statement"` |
| body | Notification body | TEXT | `"Your statement for January..."` |
| channel | Delivery channel | notification_channel | `"email"` |
| status | Delivery status | notification_status | `"delivered"` |
| sent_at | Send timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| delivered_at | Delivery timestamp | TIMESTAMPTZ | `2024-01-15 10:30:05+00` |
| read_at | Read timestamp | TIMESTAMPTZ | `2024-01-15 12:00:00+00` |
| failed_at | Failure timestamp | TIMESTAMPTZ | `null` |
| failure_reason | Failure description | TEXT | `null` |
| related_entity_type | Related entity type | audit_entity_type | `"portfolio"` |
| related_entity_id | Related entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| metadata | Additional metadata | JSONB | `{"template": "monthly_statement_v2"}` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating system/user | UUID | `********-0000-0000-0000-********0000` |

### Session History

**Purpose**: Tracks user sessions for security monitoring, analytics, and troubleshooting.

**Usage**: Used for security analysis, user behavior tracking, and session management.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | User reference | UUID | `123e4567-e89b-12d3-a456-************` |
| session_token | Session token | VARCHAR(500) | `"eyJhbGciOiJIUzI1NiIs..."` |
| started_at | Session start | TIMESTAMPTZ | `2024-01-15 10:00:00+00` |
| ended_at | Session end | TIMESTAMPTZ | `2024-01-15 12:30:00+00` |
| last_activity_at | Last activity | TIMESTAMPTZ | `2024-01-15 12:29:45+00` |
| ip_address | Client IP | INET | `*************` |
| user_agent | Browser info | TEXT | `"Mozilla/5.0..."` |
| device_type | Device type | VARCHAR(50) | `"desktop"` |
| browser | Browser name | VARCHAR(50) | `"Chrome"` |
| os | Operating system | VARCHAR(50) | `"Windows 11"` |
| country | Country code | VARCHAR(2) | `"US"` |
| region | Region/State | VARCHAR(100) | `"New York"` |
| city | City | VARCHAR(100) | `"New York"` |
| pages_viewed | Page view count | INTEGER | `25` |
| actions_performed | Action count | INTEGER | `10` |
| is_suspicious | Suspicious flag | BOOLEAN | `false` |
| termination_reason | End reason | VARCHAR(100) | `"user_logout"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:00:00+00` |

### Export History

**Purpose**: Tracks all data exports for compliance, security, and user service purposes.

**Usage**: Used for monitoring data exports, enforcing export limits, and maintaining compliance records.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| user_id | Exporting user | UUID | `123e4567-e89b-12d3-a456-************` |
| export_type | Type of export | VARCHAR(100) | `"portfolio_summary"` |
| format | Export format | VARCHAR(50) | `"pdf"` |
| entity_type | Entity type | audit_entity_type | `"portfolio"` |
| entity_id | Entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| filters | Applied filters | JSONB | `{"date_range": "2023-01-01:2023-12-31"}` |
| file_name | Generated filename | VARCHAR(500) | `"portfolio_summary_2024_01_15.pdf"` |
| file_size_bytes | File size | BIGINT | `1048576` |
| file_hash | File SHA-256 hash | VARCHAR(64) | `"e3b0c44298fc1c149afb..."` |
| status | Export status | VARCHAR(50) | `"completed"` |
| started_at | Start timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| completed_at | Completion timestamp | TIMESTAMPTZ | `2024-01-15 10:30:05+00` |
| failed_at | Failure timestamp | TIMESTAMPTZ | `null` |
| failure_reason | Failure reason | TEXT | `null` |
| download_url | Download URL | TEXT | `"https://secure.example.com/exports/..."` |
| expires_at | URL expiration | TIMESTAMPTZ | `2024-01-22 10:30:05+00` |
| download_count | Download count | INTEGER | `1` |
| last_downloaded_at | Last download | TIMESTAMPTZ | `2024-01-15 10:35:00+00` |
| metadata | Additional metadata | JSONB | `{"ip_address": "*************"}` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |

---

## Association Tables

### Entity Associations

**Purpose**: Generic association table that can link any two entities with typed relationships.

**Usage**: Used for flexible entity relationships that don't fit into specific association tables.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| source_entity_type | Source entity type | audit_entity_type | `"asset"` |
| source_entity_id | Source entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| target_entity_type | Target entity type | audit_entity_type | `"liability"` |
| target_entity_id | Target entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Type of association | association_type | `"collateral"` |
| association_status | Association status | association_status | `"active"` |
| start_date | Association start | DATE | `2020-03-15` |
| end_date | Association end | DATE | `null` |
| metadata | Association metadata | JSONB | `{"lien_position": 1}` |
| notes | Association notes | TEXT | `"Primary mortgage on property"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Liability Associations

**Purpose**: Links assets to liabilities, primarily for collateral relationships (e.g., mortgage to property).

**Usage**: Tracks which assets secure which liabilities, lien positions, and collateral values.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Association type | VARCHAR(50) | `"collateral"` |
| is_collateral | Collateral flag | BOOLEAN | `true` |
| lien_position | Lien position | INTEGER | `1` |
| lien_amount | Lien amount | DECIMAL(15,2) | `350000.00` |
| start_date | Association start | DATE | `2020-03-15` |
| end_date | Association end | DATE | `null` |
| notes | Association notes | TEXT | `"First mortgage"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Expense Associations

**Purpose**: Links assets to related expenses (e.g., property to maintenance costs).

**Usage**: Tracks ongoing expenses associated with specific assets for total cost of ownership analysis.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_id | Expense reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Association type | VARCHAR(50) | `"maintenance"` |
| allocation_percentage | Expense allocation % | DECIMAL(5,2) | `100.00` |
| start_date | Association start | DATE | `2020-03-15` |
| end_date | Association end | DATE | `null` |
| notes | Association notes | TEXT | `"Property maintenance"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Income Associations

**Purpose**: Links assets to income they generate (e.g., rental property to rental income).

**Usage**: Tracks income attribution to specific assets for ROI and performance analysis.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| income_id | Income reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Association type | VARCHAR(50) | `"rental_income"` |
| attribution_percentage | Income attribution % | DECIMAL(5,2) | `100.00` |
| start_date | Association start | DATE | `2020-03-15` |
| end_date | Association end | DATE | `null` |
| notes | Association notes | TEXT | `"Monthly rental income"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Liability Expense Associations

**Purpose**: Links liabilities to their payment expenses (e.g., mortgage to mortgage payment).

**Usage**: Connects debt obligations to their recurring payment expenses for cash flow tracking.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_id | Expense reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Association type | VARCHAR(50) | `"loan_payment"` |
| is_auto_payment | Auto-pay flag | BOOLEAN | `true` |
| payment_portion | Payment type | VARCHAR(50) | `"both"` |
| start_date | Association start | DATE | `2020-05-01` |
| end_date | Association end | DATE | `null` |
| notes | Association notes | TEXT | `"Monthly mortgage payment"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Insurance Coverage Associations

**Purpose**: Links insurance policies to the entities they cover (assets, liabilities, or people).

**Usage**: Tracks insurance coverage for risk management and ensures adequate protection.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| covered_entity_type | Entity type | audit_entity_type | `"asset"` |
| covered_entity_id | Entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| coverage_percentage | Coverage % | DECIMAL(5,2) | `100.00` |
| coverage_amount | Coverage amount | DECIMAL(15,2) | `500000.00` |
| is_beneficiary | Beneficiary flag | BOOLEAN | `false` |
| beneficiary_percentage | Beneficiary % | DECIMAL(5,2) | `null` |
| beneficiary_type | Beneficiary type | VARCHAR(50) | `null` |
| start_date | Coverage start | DATE | `2020-01-01` |
| end_date | Coverage end | DATE | `null` |
| notes | Coverage notes | TEXT | `"Primary residence coverage"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Entity Associations

**Purpose**: Generic association table that can link any two entities with typed relationships.

**Usage**: Used for flexible entity relationships that don't fit into specific association tables.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| source_entity_type | Source entity type | audit_entity_type | `"asset"` |
| source_entity_id | Source entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| target_entity_type | Target entity type | audit_entity_type | `"liability"` |
| target_entity_id | Target entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Type of association | association_type | `"collateral"` |
| association_status | Association status | association_status | `"active"` |
| association_strength | Association strength | association_strength | `"primary"` |
| start_date | Association start | DATE | `2020-03-15` |
| end_date | Association end | DATE | `null` |
| metadata | Association metadata | JSONB | `{"lien_position": 1}` |
| notes | Association notes | TEXT | `"Primary mortgage on property"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Liability Associations

**Purpose**: Links assets to liabilities, primarily for collateral relationships (e.g., mortgage to property).

**Usage**: Tracks which assets secure which liabilities, lien positions, and collateral values.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Type of association | VARCHAR(50) | `"collateral"` |
| lien_position | Lien priority position | INTEGER | `1` |
| lien_amount | Amount of lien | DECIMAL(15,2) | `350000.00` |
| lien_type | Type of lien | VARCHAR(50) | `"first_mortgage"` |
| is_primary | Primary lien flag | BOOLEAN | `true` |
| subordination_agreement | Has subordination agreement | BOOLEAN | `false` |
| status | Association status | association_status | `"active"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Expense Associations

**Purpose**: Links assets to related expenses (e.g., property to maintenance costs).

**Usage**: Tracks ongoing expenses associated with specific assets for total cost of ownership analysis.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_id | Expense reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Type of expense association | VARCHAR(50) | `"maintenance"` |
| allocation_percentage | Expense allocation percentage | DECIMAL(5,2) | `100.00` |
| is_tax_deductible | Tax deductible expense flag | BOOLEAN | `false` |
| status | Association status | association_status | `"active"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Asset Income Associations

**Purpose**: Links assets to income they generate (e.g., rental property to rental income).

**Usage**: Tracks income attribution to specific assets for ROI and performance analysis.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| asset_id | Asset reference | UUID | `123e4567-e89b-12d3-a456-************` |
| income_id | Income reference | UUID | `123e4567-e89b-12d3-a456-************` |
| association_type | Type of income association | VARCHAR(50) | `"rental"` |
| allocation_percentage | Income allocation percentage | DECIMAL(5,2) | `100.00` |
| status | Association status | association_status | `"active"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Liability Expense Associations

**Purpose**: Links liabilities to their payment expenses (e.g., mortgage to mortgage payment).

**Usage**: Connects debt obligations to their recurring payment expenses for cash flow tracking.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| liability_id | Liability reference | UUID | `123e4567-e89b-12d3-a456-************` |
| expense_id | Expense reference | UUID | `123e4567-e89b-12d3-a456-************` |
| is_auto_payment | Auto-payment enabled | BOOLEAN | `true` |
| includes_principal | Payment includes principal | BOOLEAN | `true` |
| includes_interest | Payment includes interest | BOOLEAN | `true` |
| includes_escrow | Payment includes escrow | BOOLEAN | `false` |
| status | Association status | association_status | `"active"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Insurance Coverage Associations

**Purpose**: Links insurance policies to the entities they cover (assets, liabilities, or people).

**Usage**: Tracks insurance coverage for risk management and ensures adequate protection.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|---------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| insurance_policy_id | Policy reference | UUID | `123e4567-e89b-12d3-a456-************` |
| covered_entity_type | Type of covered entity | audit_entity_type | `"asset"` |
| covered_entity_id | Covered entity ID | UUID | `123e4567-e89b-12d3-a456-************` |
| coverage_type | Type of coverage | VARCHAR(50) | `"replacement_cost"` |
| coverage_amount | Amount of coverage | DECIMAL(15,2) | `500000.00` |
| deductible_amount | Deductible amount | DECIMAL(15,2) | `1000.00` |
| coverage_start_date | Coverage start date | DATE | `2020-01-01` |
| coverage_end_date | Coverage end date | DATE | `null` |
| status | Coverage status | association_status | `"active"` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Entity Dependencies

**Purpose**: Tracks dependencies between entities for impact analysis and cascade operations.

**Usage**: Used to understand ripple effects of changes and maintain referential integrity.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| dependent_entity_type | Dependent type | audit_entity_type | `"expense"` |
| dependent_entity_id | Dependent ID | UUID | `123e4567-e89b-12d3-a456-************` |
| source_entity_type | Source type | audit_entity_type | `"liability"` |
| source_entity_id | Source ID | UUID | `123e4567-e89b-12d3-a456-************` |
| dependency_type | Dependency type | VARCHAR(100) | `"payment_for"` |
| impact_type | Impact type | impact_type | `"financial"` |
| impact_severity | Impact severity | impact_severity | `"high"` |
| impact_description | Impact description | TEXT | `"Payment depends on loan"` |
| is_active | Active dependency | BOOLEAN | `true` |
| metadata | Additional metadata | JSONB | `{"auto_adjust": true}` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

### Association Templates

**Purpose**: Stores reusable templates for common entity associations to standardize relationships.

**Usage**: Provides pre-configured association patterns for common scenarios like mortgage-to-property.

| Column Name | Description | Data Type | Example Data |
|-------------|-------------|-----------|--------------|
| id | Unique identifier | UUID | `123e4567-e89b-12d3-a456-************` |
| name | Template name | VARCHAR(255) | `"Mortgage to Property"` |
| description | Template description | TEXT | `"Links mortgage liability to property asset"` |
| source_entity_type | Source type | audit_entity_type | `"liability"` |
| target_entity_type | Target type | audit_entity_type | `"asset"` |
| association_type | Association type | association_type | `"collateral"` |
| default_metadata | Default metadata | JSONB | `{"lien_position": 1, "is_primary": true}` |
| usage_count | Usage count | INTEGER | `42` |
| last_used_at | Last usage | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| is_active | Active status | BOOLEAN | `true` |
| created_at | Creation timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| created_by | Creating user ID | UUID | `123e4567-e89b-12d3-a456-************` |
| updated_at | Last update timestamp | TIMESTAMPTZ | `2024-01-15 10:30:00+00` |
| updated_by | Last updating user ID | UUID | `123e4567-e89b-12d3-a456-************` |

---

## Entity Relationship Diagram

```mermaid
erDiagram
    %% Core Tables
    households ||--o{ household_members : "has"
    households ||--o{ portfolios : "owns"
    users ||--o{ household_members : "belongs to"
    users ||--o{ portfolio_owners : "owns"
    users ||--o{ user_roles : "has"
    portfolios ||--o{ portfolio_owners : "owned by"
    
    %% Financial Entities
    portfolios ||--o{ assets : "contains"
    portfolios ||--o{ liabilities : "contains"
    portfolios ||--o{ income : "contains"
    portfolios ||--o{ expenses : "contains"
    portfolios ||--o{ insurance_policies : "contains"
    
    %% Asset Subtypes
    assets ||--o| asset_bank_accounts : "details"
    assets ||--o| asset_investment_accounts : "details"
    assets ||--o| asset_retirement_accounts : "details"
    assets ||--o| asset_real_estate : "details"
    assets ||--o| asset_vehicles : "details"
    
    %% Liability Subtypes
    liabilities ||--o| liability_mortgages : "details"
    liabilities ||--o| liability_credit_cards : "details"
    liabilities ||--o| liability_student_loans : "details"
    
    %% Income Subtypes
    income ||--o| income_employment : "details"
    income ||--o| income_business : "details"
    income ||--o| income_investment : "details"
    
    %% Expense Subtypes
    expenses ||--o| expense_housing : "details"
    
    %% Insurance Subtypes
    insurance_policies ||--o| insurance_life : "details"
    insurance_policies ||--o| insurance_disability : "details"
    
    %% Associations
    assets }o--o{ liabilities : "asset_liability_associations"
    assets }o--o{ expenses : "asset_expense_associations"
    assets }o--o{ income : "asset_income_associations"
    liabilities }o--o{ expenses : "liability_expense_associations"
    insurance_policies }o--o{ assets : "insurance_coverage_associations"
    insurance_policies }o--o{ liabilities : "insurance_coverage_associations"
    insurance_policies }o--o{ users : "insurance_coverage_associations"
    
    %% Audit Tables
    users ||--o{ audit_log : "performs actions"
    portfolios ||--o{ portfolio_snapshots : "snapshots"
    users ||--o{ notification_history : "receives"
    users ||--o{ session_history : "has sessions"
    users ||--o{ export_history : "exports data"
    
    %% Entity Dependencies
    assets }o--o{ expenses : "entity_dependencies"
    liabilities }o--o{ expenses : "entity_dependencies"
    assets }o--o{ income : "entity_dependencies"

    %% Table Definitions
    households {
        uuid id PK
        varchar name
        varchar type
        text description
        boolean is_private
        jsonb settings
        timestamp created_at
        uuid created_by FK
    }
    
    users {
        uuid id PK
        varchar email UK
        varchar username UK
        varchar first_name
        varchar last_name
        user_role role
        user_status status
        timestamp last_login_at
        timestamp created_at
    }
    
    portfolios {
        uuid id PK
        uuid household_id FK
        varchar name
        portfolio_ownership_type ownership_type
        decimal total_assets
        decimal total_liabilities
        decimal net_worth
        timestamp created_at
    }
    
    assets {
        uuid id PK
        uuid portfolio_id FK
        asset_type asset_type
        varchar name
        decimal current_value
        boolean is_active
        timestamp created_at
    }
    
    liabilities {
        uuid id PK
        uuid portfolio_id FK
        liability_type liability_type
        varchar name
        decimal current_balance
        decimal interest_rate
        timestamp created_at
    }
    
    income {
        uuid id PK
        uuid portfolio_id FK
        income_type income_type
        varchar name
        decimal gross_amount
        income_frequency frequency
        timestamp created_at
    }
    
    expenses {
        uuid id PK
        uuid portfolio_id FK
        expense_type expense_type
        varchar name
        decimal amount
        expense_frequency frequency
        timestamp created_at
    }
    
    insurance_policies {
        uuid id PK
        uuid portfolio_id FK
        insurance_type insurance_type
        varchar policy_number
        decimal coverage_amount
        decimal premium_amount
        timestamp created_at
    }
```

---

## Additional Notes

### Missing Enum Type Definition

The following enum type is referenced in the association tables but was not explicitly defined in the migration scripts:

**association_strength**
- Used in `entity_associations` and `entity_dependencies` tables
- Likely values: `'primary'`, `'secondary'`, `'tertiary'`, `'optional'`
- Purpose: Indicates the strength or importance of the association/dependency

This enum should be added to the `002_create_enums.sql` migration:

```sql
CREATE TYPE association_strength AS ENUM (
    'primary',    -- Primary/strong association
    'secondary',  -- Secondary/moderate association  
    'tertiary',   -- Tertiary/weak association
    'optional'    -- Optional/informational association
);
```

---

## Summary

The FinPro domain model provides a comprehensive framework for household financial management with:

- **Hierarchical Organization**: Households → Portfolios → Financial Entities
- **Flexible Relationships**: Generic and specific association tables for complex relationships
- **Complete Audit Trail**: Every action is logged with full history tracking
- **Extensible Design**: JSON fields and inheritance patterns allow for future growth
- **Data Integrity**: Extensive constraints, triggers, and validation functions
- **Performance Optimization**: Strategic indexes on all foreign keys and common queries

This model supports both simple and complex financial planning scenarios while maintaining data integrity, security, and compliance requirements.
