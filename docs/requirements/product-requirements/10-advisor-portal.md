# Product Requirements Document: Advisor Portal Module

## 1. Overview

### 1.1 Purpose
The Advisor Portal module provides financial advisors with comprehensive tools to manage multiple client relationships, monitor client financial health, provide guidance, and collaborate effectively. This module enables advisors to scale their practice while maintaining personalized service for each client.

### 1.2 Scope
This module covers client management, team collaboration, and practice analytics for financial advisors managing multiple client accounts both individually and as part of teams.

## 2. Core Capabilities

### 2.1 Client Management
- **Client Roster**: Comprehensive list of all clients
- **Client Profiles**: Detailed view of each client
- **Client Grouping**: Organize by categories
- **Search & Filter**: Find clients quickly

### 2.2 Team Management
- **Team Dashboard**: Overview of team performance
- **Shared Clients**: Access to all team clients
- **Team Analytics**: Collective metrics and insights
- **Team Collaboration**: Work together on client accounts

## 3. Functional Requirements

### 3.1 Client List Management

#### 3.1.1 Client Roster
- **FR-CLIENT-001**: Display all assigned clients
- **FR-CLIENT-002**: Show client membership status (active, pending, inactive)
- **FR-CLIENT-003**: Display last activity date
- **FR-CLIENT-004**: Show data gathering completion percentage
- **FR-CLIENT-005**: Quick access to key metrics 

#### 3.1.2 Search and Filter
- **FR-CLIENT-006**: Search by client name
- **FR-CLIENT-007**: Filter by status
- **FR-CLIENT-008**: Filter by net worth range
- **FR-CLIENT-009**: Filter by last activity

### 3.2 Client Onboarding

#### 3.2.1 Invitation System
- **FR-ONBOARD-001**: Send email invitations
- **FR-ONBOARD-002**: Track invitation status
- **FR-ONBOARD-003**: Resend invitations
- **FR-ONBOARD-004**: Customize invitation message
- **FR-ONBOARD-006**: Revoke invitations

### 3.3 Client Management

#### 3.3.1 Client Access
- **FR-ACCESS-001**: View exactly what client sees on their dashboard
- **FR-ACCESS-002**: Help clients complete or change information
- **FR-ACCESS-003**: Audit trail of advisor changes
- **FR-ACCESS-004**: Quick return to advisor dashboard

### 3.4 Advisor Dashboard

#### 3.4.1 Individual Dashboard Metrics
- **FR-PORTFOLIO-001**: Total assets under management (individual)
- **FR-PORTFOLIO-002**: Average client net worth (individual)
- **FR-PORTFOLIO-003**: Client distribution charts
- **FR-PORTFOLIO-004**: Total number of clients (individual)
- **FR-PORTFOLIO-005**: List of advisor's own clients
- **FR-PORTFOLIO-006**: Toggle between individual and team view

### 3.5 Team Functionality

#### 3.5.1 Team Membership Rules
- **FR-TEAM-001**: Advisor can only be member of one team at a time
- **FR-TEAM-002**: Team membership enforced at organization level
- **FR-TEAM-003**: Display current team assignment clearly
- **FR-TEAM-004**: Show team status (active/inactive)

#### 3.5.2 Team Dashboard
- **FR-TEAM-005**: Display total number of advisors in team
- **FR-TEAM-006**: Show total team assets under management
- **FR-TEAM-007**: Display total team net worth
- **FR-TEAM-008**: Show total households managed by team
- **FR-TEAM-009**: Display team performance metrics
- **FR-TEAM-010**: Compare individual vs team contribution

#### 3.5.3 Team Client Management
- **FR-TEAM-011**: View all clients associated with team
- **FR-TEAM-012**: Search clients across entire team
- **FR-TEAM-013**: Filter team clients by various criteria
- **FR-TEAM-014**: Show advisor for each client
- **FR-TEAM-015**: Act on behalf of any team client
- **FR-TEAM-016**: Maintain audit trail of cross-advisor actions

#### 3.5.4 Team Client Table
- **FR-TEAM-017**: Display searchable list of all team clients
- **FR-TEAM-018**: Show client name and profile
- **FR-TEAM-019**: Display associated advisor name
- **FR-TEAM-020**: Show client net worth
- **FR-TEAM-021**: Display total assets
- **FR-TEAM-022**: Show last activity date
- **FR-TEAM-023**: Enable sorting by all columns
- **FR-TEAM-024**: Implement filtering by advisor
- **FR-TEAM-025**: Quick access to client details


## 4. Non-Functional Requirements

### 4.1 Performance
- **NFR-PERF-001**: Load client list within 2 seconds

### 4.2 Security
- **NFR-SEC-004**: Activity logging

### 4.4 Compliance
- **NFR-COMP-001**: SEC compliance
- **NFR-COMP-002**: FINRA requirements
- **NFR-COMP-004**: Data privacy laws

## 5. User Stories

### 5.1 Client Management
- **US-001**: As an advisor, I want to see all my clients at a glance
- **US-002**: As an advisor, I want to quickly find specific clients
- **US-003**: As an advisor, I want to group similar clients
- **US-004**: As an advisor, I want to to invite clients 

### 5.2 Client Service
- **US-005**: As an advisor, I want to help clients manage financial profile
- **US-006**: As an advisor, I want to monitor client progress

### 5.3 Practice Management
- **US-007**: As an advisor, I want to track my clients total assets net worth and growth
- **US-008**: As an advisor, I want to switch between personal and team views
- **US-009**: As an advisor, I want to see my contribution to team metrics

### 5.4 Team Collaboration
- **US-010**: As a team member, I want to see all team clients in one place
- **US-011**: As a team member, I want to help any client in my team
- **US-012**: As a team member, I want to see which advisor manages each client
- **US-013**: As a team member, I want to track team performance metrics
- **US-014**: As a team member, I want to search across all team clients
- **US-015**: As a team member, I want to see team analytics and insights


## 7. API Endpoints

### 7.1 Individual Advisor
- `GET /api/advisor/clients` - List advisor's own clients
- `GET /api/advisor/clients/:id` - Get client details
- `DELETE /api/advisor/clients/:id` - Remove client
- `POST /api/advisor/invitations` - Send client invitation
- `DELETE /api/advisor/invitations/:id` - Revoke client invitation
- `GET /api/advisor/dashboard` - Get individual dashboard

### 7.2 Team Management
- `GET /api/advisor/team` - Get current team details
- `GET /api/advisor/team/dashboard` - Get team dashboard metrics
- `GET /api/advisor/team/clients` - List all team clients
- `GET /api/advisor/team/clients/:id` - Get team client details
- `GET /api/advisor/team/advisors` - List team members


## 8. Acceptance Criteria

### 8.1 Core Functionality
- [ ] Advisors can view all their individual clients
- [ ] Advisors can manage their client data
- [ ] Advisors can send and manage client invitations
- [ ] Individual dashboard shows personal metrics

### 8.2 Team Functionality
- [ ] Advisors can only belong to one team at a time
- [ ] Team dashboard displays accurate team metrics
- [ ] All team members can view all team clients
- [ ] Team members can act on behalf of any team client
- [ ] Team client table shows all required information
- [ ] Search and filter work across all team clients
- [ ] Audit trail captures cross-advisor actions
- [ ] Advisor is clearly indicated for each client
- [ ] Team performance metrics are accurate
- [ ] Toggle between individual and team view works correctly