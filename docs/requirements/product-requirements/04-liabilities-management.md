# Product Requirements Document: Liabilities Management Module

## 1. Overview

### 1.1 Purpose
The Liabilities Management module enables users to track, manage, and analyze their debts and financial obligations. This module provides comprehensive debt tracking with payment schedules, interest calculations, and payoff strategies to help users manage and reduce their liabilities effectively.

### 1.2 Scope
This module covers all liability types including mortgages, auto loans, student loans, credit cards, personal loans, and other debts, with support for payment tracking, amortization schedules, and debt reduction planning.

## 2. Liability Categories

### 2.1 Secured Debt
- **Mortgages**: Primary residence, investment properties, HELOCs
- **Auto Loans**: Vehicle financing
- **Secured Personal Loans**: Loans backed by collateral
- **Equipment Loans**: Business equipment financing

### 2.2 Unsecured Debt
- **Credit Cards**: Revolving credit accounts
- **Personal Loans**: Unsecured personal lending
- **Student Loans**: Federal and private education loans
- **Medical Debt**: Healthcare-related obligations

### 2.3 Retirement Loans
- **401(k) Loans**: Borrowing from retirement accounts
- **403(b) Loans**: Public sector retirement loans
- **IRA Loans**: Prohibited but tracked if exists

### 2.4 Other Obligations
- **Tax Debt**: Federal, state, and local tax obligations
- **Alimony/Child Support**: Court-ordered payments
- **Business Loans**: Small business financing
- **Margin Loans**: Investment account borrowing

## 3. Functional Requirements

### 3.1 Liability Creation & Management

#### 3.1.1 Liability Entry
- **FR-LIAB-001**: Users shall be able to add new liabilities with required fields
- **FR-LIAB-002**: System shall categorize liabilities by type automatically
- **FR-LIAB-003**: Users shall link liabilities to associated assets when applicable
- **FR-LIAB-004**: System shall validate liability data based on type
- **FR-LIAB-005**: Users shall be able to upload loan documents

#### 3.1.2 Liability Details
- **FR-LIAB-006**: Track lender name and contact information
- **FR-LIAB-007**: Record original loan amount and date
- **FR-LIAB-008**: Monitor current balance with last update date
- **FR-LIAB-009**: Track interest rate (fixed or variable)
- **FR-LIAB-010**: Store loan term and maturity date

### 3.2 Payment Management

#### 3.2.1 Payment Tracking
- **FR-PAY-001**: Record minimum monthly payment amount
- **FR-PAY-002**: Track actual payment history
- **FR-PAY-003**: Monitor payment due dates
- **FR-PAY-004**: Calculate principal vs interest breakdown
- **FR-PAY-005**: Support extra principal payments

#### 3.2.2 Payment Scheduling
- **FR-PAY-006**: Set up automatic payment reminders
- **FR-PAY-007**: Track autopay enrollment status
- **FR-PAY-008**: Monitor late payments and fees
- **FR-PAY-009**: Calculate payoff dates based on payment amounts
- **FR-PAY-010**: Support bi-weekly payment schedules

### 3.3 Mortgage Features

#### 3.3.1 Mortgage Details
- **FR-MORT-001**: Track property address and value
- **FR-MORT-002**: Monitor loan-to-value ratio (LTV)
- **FR-MORT-003**: Track PMI requirements and removal date
- **FR-MORT-004**: Support adjustable-rate mortgage (ARM) tracking
- **FR-MORT-005**: Calculate home equity automatically

#### 3.3.2 Refinancing Analysis
- **FR-MORT-006**: Compare current vs potential refinance terms
- **FR-MORT-007**: Calculate break-even point for refinancing
- **FR-MORT-008**: Track closing costs and fees
- **FR-MORT-009**: Monitor rate lock expiration dates
- **FR-MORT-010**: Generate refinancing recommendation reports

### 3.4 Credit Card Features

#### 3.4.1 Credit Management
- **FR-CC-001**: Track credit limit and utilization percentage
- **FR-CC-002**: Monitor APR for purchases and cash advances
- **FR-CC-003**: Calculate interest charges based on average daily balance
- **FR-CC-004**: Track promotional rates and expiration dates
- **FR-CC-005**: Monitor rewards and cashback earnings

#### 3.4.2 Balance Transfer Tracking
- **FR-CC-006**: Track balance transfer amounts and fees
- **FR-CC-007**: Monitor promotional APR periods
- **FR-CC-008**: Calculate savings from balance transfers
- **FR-CC-009**: Alert before promotional rate expiration
- **FR-CC-010**: Recommend optimal transfer strategies

### 3.5 Student Loan Features

#### 3.5.1 Loan Management
- **FR-STU-001**: Differentiate federal vs private loans
- **FR-STU-002**: Track loan servicer information
- **FR-STU-003**: Monitor grace period and deferment status
- **FR-STU-004**: Calculate income-driven repayment amounts
- **FR-STU-005**: Track Public Service Loan Forgiveness progress

#### 3.5.2 Repayment Options
- **FR-STU-006**: Compare standard vs income-driven plans
- **FR-STU-007**: Calculate total interest under different plans
- **FR-STU-008**: Track qualifying payments for forgiveness
- **FR-STU-009**: Monitor consolidation opportunities
- **FR-STU-010**: Alert for recertification deadlines

### 3.6 Debt Analysis & Planning

#### 3.6.1 Debt Metrics
- **FR-ANALYSIS-001**: Calculate total debt-to-income ratio
- **FR-ANALYSIS-002**: Monitor credit utilization across all cards
- **FR-ANALYSIS-003**: Track weighted average interest rate
- **FR-ANALYSIS-004**: Calculate total monthly debt payments
- **FR-ANALYSIS-005**: Project total interest to be paid

#### 3.6.2 Payoff Strategies
- **FR-ANALYSIS-006**: Generate debt snowball plan (smallest balance first)
- **FR-ANALYSIS-007**: Generate debt avalanche plan (highest rate first)
- **FR-ANALYSIS-008**: Calculate time and interest savings for each strategy
- **FR-ANALYSIS-009**: Create custom payoff plans with extra payments
- **FR-ANALYSIS-010**: Track progress against payoff goals

### 3.7 Amortization & Calculations

#### 3.7.1 Amortization Schedules
- **FR-AMORT-001**: Generate full amortization schedules
- **FR-AMORT-002**: Show principal and interest breakdown by payment
- **FR-AMORT-003**: Calculate impact of extra payments
- **FR-AMORT-004**: Display remaining balance after each payment
- **FR-AMORT-005**: Export schedules to spreadsheet format

#### 3.7.2 Interest Calculations
- **FR-AMORT-006**: Support simple interest calculation
- **FR-AMORT-007**: Support compound interest calculation
- **FR-AMORT-008**: Calculate daily interest accrual
- **FR-AMORT-009**: Handle variable rate changes
- **FR-AMORT-010**: Track capitalized interest

## 4. Non-Functional Requirements

### 4.1 Accuracy
- **NFR-ACC-001**: Interest calculations accurate to $0.01
- **NFR-ACC-002**: Amortization schedules match lender statements
- **NFR-ACC-003**: Payoff amounts include per diem interest
- **NFR-ACC-004**: Support leap year calculations

### 4.2 Performance
- **NFR-PERF-001**: Generate amortization schedules within 1 second
- **NFR-PERF-002**: Calculate payoff strategies within 2 seconds
- **NFR-PERF-003**: Support 100+ liabilities per user
- **NFR-PERF-004**: Real-time balance updates

### 4.3 Security
- **NFR-SEC-001**: Encrypt sensitive loan information
- **NFR-SEC-002**: Mask account numbers in UI
- **NFR-SEC-003**: Secure document storage
- **NFR-SEC-004**: Audit trail for all changes

### 4.4 Compliance
- **NFR-COMP-001**: Comply with Truth in Lending Act
- **NFR-COMP-002**: Follow FCRA requirements
- **NFR-COMP-003**: Respect debt collection regulations
- **NFR-COMP-004**: Maintain data retention policies

## 5. User Stories

### 5.1 Basic Debt Management
- **US-001**: As a user, I want to add my mortgage so I can track my largest debt
- **US-002**: As a user, I want to see all my debts in one place
- **US-003**: As a user, I want to update balances as I make payments
- **US-004**: As a user, I want to see when each debt will be paid off

### 5.2 Payment Planning
- **US-005**: As a user, I want to see how extra payments reduce interest
- **US-006**: As a user, I want reminders for payment due dates
- **US-007**: As a user, I want to compare payoff strategies
- **US-008**: As a user, I want to track my debt reduction progress

### 5.3 Financial Planning
- **US-009**: As a homeowner, I want to see if refinancing saves money
- **US-010**: As a student, I want to compare repayment plans
- **US-011**: As a credit card user, I want to minimize interest charges
- **US-012**: As a borrower, I want to become debt-free faster

## 6. Data Models

```typescript
interface Liability {
  id: string;
  userId: string;
  category: LiabilityCategory;
  type: LiabilityType;
  name: string;
  lender: LenderInfo;
  originalAmount: number;
  currentBalance: number;
  interestRate: number;
  rateType: 'fixed' | 'variable';
  term: number; // months
  originationDate: Date;
  maturityDate: Date;
  paymentAmount: number;
  paymentFrequency: PaymentFrequency;
  nextPaymentDate: Date;
  linkedAssetId?: string;
  status: 'current' | 'delinquent' | 'paid_off';
}

interface Mortgage extends Liability {
  propertyAddress: Address;
  propertyValue: number;
  loanToValue: number;
  pmiRequired: boolean;
  pmiRemovalDate?: Date;
  escrowAmount?: number;
  armDetails?: ARMDetails;
}

interface CreditCard extends Liability {
  creditLimit: number;
  utilization: number;
  purchaseAPR: number;
  cashAdvanceAPR: number;
  balanceTransferAPR?: number;
  promotionalRates: PromotionalRate[];
  rewardsProgram?: RewardsInfo;
  annualFee?: number;
}

interface StudentLoan extends Liability {
  loanType: 'federal' | 'private';
  servicer: string;
  repaymentPlan: RepaymentPlanType;
  graceEndDate?: Date;
  defermentEndDate?: Date;
  subsidized: boolean;
  pslf: {
    enrolled: boolean;
    qualifyingPayments: number;
    employmentCertified: boolean;
  };
}

interface PaymentHistory {
  liabilityId: string;
  paymentDate: Date;
  scheduledAmount: number;
  actualAmount: number;
  principal: number;
  interest: number;
  fees?: number;
  remainingBalance: number;
  status: 'on_time' | 'late' | 'missed';
}

interface DebtPayoffPlan {
  strategy: 'snowball' | 'avalanche' | 'custom';
  monthlyExtraPayment: number;
  payoffOrder: string[]; // liability IDs in order
  projectedPayoffDate: Date;
  totalInterestSaved: number;
  monthsSaved: number;
}
```

## 7. API Endpoints

- `GET /api/liabilities` - List all user liabilities
- `GET /api/liabilities/:id` - Get specific liability details
- `POST /api/liabilities` - Create new liability
- `PUT /api/liabilities/:id` - Update liability
- `DELETE /api/liabilities/:id` - Delete liability
- `POST /api/liabilities/:id/payments` - Record payment
- `GET /api/liabilities/:id/amortization` - Get amortization schedule
- `GET /api/liabilities/summary` - Get debt summary metrics
- `POST /api/liabilities/payoff-plan` - Generate payoff strategy
- `GET /api/liabilities/:id/payoff-quote` - Get payoff amount

## 8. Acceptance Criteria

### 8.1 Liability Management
- [ ] Users can add all supported liability types
- [ ] Balances update correctly with payments
- [ ] Interest accrues accurately
- [ ] Payment schedules calculate correctly

### 8.2 Analysis Features
- [ ] Debt ratios calculate accurately
- [ ] Payoff strategies generate correctly
- [ ] Amortization schedules match lender statements
- [ ] Extra payment impacts show accurately

### 8.3 User Experience
- [ ] Payment reminders send on time
- [ ] Liability dashboard loads quickly
- [ ] Mobile interface works properly
- [ ] Help documentation is comprehensive

## 9. Integration Points
- Credit report APIs for balance updates
- Banking APIs for payment verification
- Notification service for reminders
- Document storage for loan documents

## 10. Future Enhancements
- Automated balance updates via API
- Credit score impact analysis
- Debt consolidation recommendations
- Bankruptcy analysis tools
- International loan support
- Cryptocurrency-backed loans