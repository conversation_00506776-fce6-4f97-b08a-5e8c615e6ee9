# Product Requirements Document: Assets Management Module

## 1. Overview

### 1.1 Purpose
The Assets Management module enables users to track, manage, and analyze their financial assets including banking accounts, investments, real estate, and personal property. This module provides comprehensive asset tracking with real-time valuations and performance monitoring.

### 1.2 Scope
This module covers all asset types including liquid assets (banking, investments), illiquid assets (real estate, personal property), and retirement accounts, with support for ownership types, beneficiaries, and asset allocation analysis.

## 2. Asset Categories

### 2.1 Banking Assets
- **Checking Accounts**: Daily transaction accounts
- **Savings Accounts**: Interest-bearing savings
- **Money Market Accounts**: Higher-yield savings
- **Certificates of Deposit**: Fixed-term deposits

### 2.2 Investment Assets
- **Brokerage Accounts**: Taxable investment accounts
- **Retirement Accounts**: 401(k), IRA, Roth IRA, SEP-IRA
- **Education Savings**: 529 plans, Coverdell ESA
- **Health Savings Accounts**: HSA investments

### 2.3 Real Estate
- **Primary Residence**: Owner-occupied home
- **Investment Properties**: Rental properties
- **Vacation Homes**: Secondary residences
- **Commercial Properties**: Business real estate

### 2.4 Personal Property
- **Vehicles**: Cars, boats, RVs
- **Valuable Items**: Jewelry, art, collectibles
- **Business Assets**: Equipment, inventory
- **Life Insurance Cash Value**: Permanent life policies

## 3. Functional Requirements

### 3.1 Asset Creation & Management

#### 3.1.1 Asset Entry
- **FR-ASSET-001**: Users shall be able to add new assets with required fields
- **FR-ASSET-002**: System shall support manual and automated asset entry
- **FR-ASSET-003**: Users shall be able to categorize assets by type
- **FR-ASSET-004**: System shall validate asset data based on type
- **FR-ASSET-005**: Users shall be able to upload supporting documents

#### 3.1.2 Asset Details
- **FR-ASSET-006**: Each asset shall have customizable name and description
- **FR-ASSET-007**: System shall track acquisition date and cost basis
- **FR-ASSET-008**: Users shall specify ownership type (individual, joint, trust)
- **FR-ASSET-009**: System shall support beneficiary designation where applicable
- **FR-ASSET-010**: Users shall be able to add notes and tags to assets

### 3.2 Banking Assets Features

#### 3.2.1 Account Management
- **FR-BANK-001**: Track account number (masked for security)
- **FR-BANK-002**: Record financial institution details
- **FR-BANK-003**: Monitor current balance with last update date
- **FR-BANK-004**: Track interest rate and APY for savings accounts
- **FR-BANK-005**: Calculate monthly interest earnings

#### 3.2.2 Transaction History
- **FR-BANK-006**: Import transactions via bank connection (future)
- **FR-BANK-007**: Categorize transactions automatically
- **FR-BANK-008**: Track average daily balance
- **FR-BANK-009**: Monitor cash flow patterns

### 3.3 Investment Assets Features

#### 3.3.1 Portfolio Management
- **FR-INVEST-001**: Track individual holdings within accounts
- **FR-INVEST-002**: Monitor cost basis for each holding
- **FR-INVEST-003**: Calculate unrealized gains/losses
- **FR-INVEST-004**: Track dividend and interest income
- **FR-INVEST-005**: Support fractional shares

#### 3.3.2 Performance Tracking
- **FR-INVEST-006**: Calculate time-weighted returns
- **FR-INVEST-007**: Compare performance to benchmarks
- **FR-INVEST-008**: Track asset allocation by category
- **FR-INVEST-009**: Monitor portfolio rebalancing needs
- **FR-INVEST-010**: Generate investment income reports

#### 3.3.3 Retirement Accounts
- **FR-RETIRE-001**: Track contribution limits and YTD contributions
- **FR-RETIRE-002**: Monitor employer matching contributions
- **FR-RETIRE-003**: Calculate required minimum distributions (RMDs)
- **FR-RETIRE-004**: Track vesting schedules
- **FR-RETIRE-005**: Project retirement account growth

### 3.4 Real Estate Features

#### 3.4.1 Property Details
- **FR-REALE-001**: Record property address and legal description
- **FR-REALE-002**: Track purchase price and current market value
- **FR-REALE-003**: Monitor property tax assessments
- **FR-REALE-004**: Track home improvements and their costs
- **FR-REALE-005**: Calculate equity based on mortgage balance

#### 3.4.2 Rental Properties
- **FR-REALE-006**: Track rental income by property
- **FR-REALE-007**: Monitor operating expenses
- **FR-REALE-008**: Calculate cash flow and ROI
- **FR-REALE-009**: Track tenant information and leases
- **FR-REALE-010**: Generate Schedule E tax reports

### 3.5 Personal Property Features

#### 3.5.1 Vehicle Management
- **FR-VEHICLE-001**: Track make, model, year, and VIN
- **FR-VEHICLE-002**: Monitor current value with depreciation
- **FR-VEHICLE-003**: Track maintenance history and costs
- **FR-VEHICLE-004**: Store insurance information
- **FR-VEHICLE-005**: Calculate total cost of ownership

#### 3.5.2 Valuable Items
- **FR-VALUE-001**: Catalog items with photos
- **FR-VALUE-002**: Track appraisal values and dates
- **FR-VALUE-003**: Store insurance policy details
- **FR-VALUE-004**: Document provenance and authenticity
- **FR-VALUE-005**: Generate inventory reports for insurance

### 3.6 Asset Valuation & Updates

#### 3.6.1 Valuation Methods
- **FR-VAL-001**: Support manual value updates with date tracking
- **FR-VAL-002**: Integrate with market data for securities (future)
- **FR-VAL-003**: Use Zillow/Redfin APIs for real estate (future)
- **FR-VAL-004**: Apply depreciation schedules for vehicles
- **FR-VAL-005**: Track historical valuations over time

#### 3.6.2 Automated Updates
- **FR-VAL-006**: Schedule periodic value updates
- **FR-VAL-007**: Alert users when updates are needed
- **FR-VAL-008**: Flag significant value changes
- **FR-VAL-009**: Maintain audit trail of all changes

## 4. Non-Functional Requirements

### 4.1 Security
- **NFR-SEC-001**: Encrypt sensitive asset data at rest
- **NFR-SEC-002**: Mask account numbers in UI
- **NFR-SEC-003**: Implement row-level security for asset access
- **NFR-SEC-004**: Log all asset modifications

### 4.2 Performance
- **NFR-PERF-001**: Load asset list within 2 seconds
- **NFR-PERF-002**: Support 10,000+ assets per user
- **NFR-PERF-003**: Calculate totals in real-time
- **NFR-PERF-004**: Bulk operations complete within 5 seconds

### 4.3 Usability
- **NFR-USE-001**: Provide asset templates for common types
- **NFR-USE-002**: Support bulk import via CSV
- **NFR-USE-003**: Enable filtering and sorting options
- **NFR-USE-004**: Offer mobile-responsive interface

### 4.4 Integration
- **NFR-INT-001**: Support Plaid for account aggregation
- **NFR-INT-002**: Integrate with market data providers
- **NFR-INT-003**: Export to accounting software
- **NFR-INT-004**: Sync with tax preparation tools

## 5. User Stories

### 5.1 Basic Asset Management
- **US-001**: As a user, I want to add my checking account so I can track my cash
- **US-002**: As a user, I want to update asset values so they reflect current market
- **US-003**: As a user, I want to see total assets by category
- **US-004**: As a user, I want to delete assets I no longer own

### 5.2 Investment Tracking
- **US-005**: As an investor, I want to track my portfolio performance
- **US-006**: As an investor, I want to see my asset allocation
- **US-007**: As an investor, I want to monitor dividend income
- **US-008**: As an investor, I want to track cost basis for taxes

### 5.3 Property Management
- **US-009**: As a homeowner, I want to track my home's value
- **US-010**: As a landlord, I want to monitor rental income
- **US-011**: As a property owner, I want to track improvements
- **US-012**: As a property owner, I want to calculate equity

## 6. Data Models

```typescript
interface Asset {
  id: string;
  userId: string;
  category: AssetCategory;
  type: AssetType;
  name: string;
  description?: string;
  ownership: 'individual' | 'joint' | 'trust' | 'business';
  owners: Owner[];
  currentValue: number;
  costBasis?: number;
  acquisitionDate?: Date;
  lastUpdated: Date;
  metadata: AssetMetadata;
}

interface BankingAsset extends Asset {
  institutionName: string;
  accountNumber: string; // masked
  accountType: 'checking' | 'savings' | 'moneyMarket' | 'cd';
  interestRate?: number;
  maturityDate?: Date; // for CDs
  minimumBalance?: number;
}

interface InvestmentAsset extends Asset {
  accountType: 'brokerage' | '401k' | 'ira' | 'rothIra' | '529' | 'hsa';
  custodian: string;
  holdings: Holding[];
  cashBalance: number;
  totalValue: number;
  unrealizedGain: number;
  yearToDateReturn: number;
}

interface RealEstateAsset extends Asset {
  propertyType: 'primary' | 'investment' | 'vacation' | 'commercial';
  address: Address;
  purchasePrice: number;
  purchaseDate: Date;
  marketValue: number;
  assessedValue: number;
  mortgageBalance?: number;
  monthlyRentalIncome?: number;
  annualExpenses?: number;
}

interface PersonalPropertyAsset extends Asset {
  itemType: 'vehicle' | 'jewelry' | 'art' | 'collectible' | 'equipment';
  make?: string;
  model?: string;
  year?: number;
  serialNumber?: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  appraisalValue?: number;
  appraisalDate?: Date;
}
```

## 7. API Endpoints

- `GET /api/assets` - List all user assets
- `GET /api/assets/:id` - Get specific asset details
- `POST /api/assets` - Create new asset
- `PUT /api/assets/:id` - Update asset
- `DELETE /api/assets/:id` - Delete asset
- `GET /api/assets/summary` - Get asset summary by category
- `POST /api/assets/:id/valuations` - Add valuation history
- `GET /api/assets/:id/performance` - Get investment performance
- `POST /api/assets/import` - Bulk import assets

## 8. Acceptance Criteria

### 8.1 Asset Management
- [ ] Users can add all supported asset types
- [ ] Asset values update correctly
- [ ] Ownership types are properly tracked
- [ ] Asset categorization works correctly

### 8.2 Calculations
- [ ] Total asset value calculates correctly
- [ ] Investment returns calculate accurately
- [ ] Real estate equity calculates properly
- [ ] Vehicle depreciation applies correctly

### 8.3 Reporting
- [ ] Asset summary shows by category
- [ ] Performance metrics display accurately
- [ ] Historical valuations track properly
- [ ] Export functionality works correctly

## 9. Future Enhancements
- Automated account aggregation via Plaid
- Real-time market data integration
- Cryptocurrency asset support
- International asset support
- Asset-backed loan tracking
- Insurance policy integration