# Product Requirements Document: Client Intake/Onboarding Module

## 1. Overview

### 1.1 Purpose
The Client Intake/Onboarding module provides a comprehensive, user-friendly process for collecting financial information from new clients. This module ensures accurate data collection while minimizing user friction through progressive disclosure and intelligent form design.

### 1.2 Scope
This module covers the multi-step intake process including personal information, financial situation assessment, assets, liabilities, insurance, income, and expenses collection, with support for both Simple and Standard entry modes.

## 2. User Experience Modes

### 2.1 Simple Mode
- **Target Users**: Clients with straightforward financial situations
- **Characteristics**:
  - Fewer form fields
  - Basic asset/liability types
  - Simplified income/expense categories
  - Guided recommendations

### 2.2 Standard Mode
- **Target Users**: Clients with complex financial situations
- **Characteristics**:
  - Comprehensive form fields
  - All asset/liability types available
  - Detailed categorization
  - Advanced options

## 3. Functional Requirements

### 3.1 Intake Flow Management

#### 3.1.1 Progress Tracking
- **FR-INTAKE-001**: System shall display progress indicator showing current step
- **FR-INTAKE-002**: System shall calculate and display completion percentage
- **FR-INTAKE-003**: System shall save progress automatically every 30 seconds
- **FR-INTAKE-004**: Users shall be able to navigate between completed sections
- **FR-INTAKE-005**: System shall prevent navigation to future sections until prerequisites are met

#### 3.1.2 Data Persistence
- **FR-INTAKE-006**: All entered data shall be saved in draft state
- **FR-INTAKE-007**: Users shall be able to resume intake from last position
- **FR-INTAKE-008**: System shall support multiple intake sessions
- **FR-INTAKE-009**: Draft data shall be retained for 30 days

### 3.2 Personal Information Section

#### 3.2.1 Basic Information
- **FR-PERSONAL-001**: Collect first name, last name, email (required)
- **FR-PERSONAL-002**: Collect phone number with country code
- **FR-PERSONAL-003**: Collect date of birth with age calculation
- **FR-PERSONAL-004**: Collect residential address with validation

#### 3.2.2 Household Information
- **FR-PERSONAL-005**: Collect marital status
- **FR-PERSONAL-006**: If married, collect spouse information
- **FR-PERSONAL-007**: Collect dependent information (children, elderly parents)
- **FR-PERSONAL-008**: Support adding multiple dependents

### 3.3 Situation Assessment Section

#### 3.3.1 Living Situation
- **FR-SITUATION-001**: Collect current housing status (own, rent, other)
- **FR-SITUATION-002**: Collect employment status for user and spouse
- **FR-SITUATION-003**: Collect retirement status and target date
- **FR-SITUATION-004**: Assess financial complexity to recommend Simple/Standard mode

#### 3.3.2 Financial Goals
- **FR-SITUATION-005**: Collect short-term financial goals (1-3 years)
- **FR-SITUATION-006**: Collect long-term financial goals (3+ years)
- **FR-SITUATION-007**: Collect risk tolerance assessment
- **FR-SITUATION-008**: Collect investment experience level

### 3.4 Assets Section

#### 3.4.1 Banking Assets
- **FR-ASSETS-001**: Support checking accounts with current balance
- **FR-ASSETS-002**: Support savings accounts with interest rate
- **FR-ASSETS-003**: Support money market accounts
- **FR-ASSETS-004**: Support certificates of deposit with maturity dates

#### 3.4.2 Investment Assets
- **FR-ASSETS-005**: Support brokerage accounts with holdings
- **FR-ASSETS-006**: Support retirement accounts (401k, IRA, Roth IRA)
- **FR-ASSETS-007**: Support education savings (529, ESA)
- **FR-ASSETS-008**: Support HSA accounts

#### 3.4.3 Real Estate Assets
- **FR-ASSETS-009**: Support primary residence with estimated value
- **FR-ASSETS-010**: Support investment properties
- **FR-ASSETS-011**: Link mortgages to properties
- **FR-ASSETS-012**: Collect property details (address, purchase date, improvements)

#### 3.4.4 Personal Property
- **FR-ASSETS-013**: Support vehicle assets with make/model/year
- **FR-ASSETS-014**: Support valuable personal property
- **FR-ASSETS-015**: Support business assets
- **FR-ASSETS-016**: Auto-calculate depreciation for vehicles

### 3.5 Liabilities Section

#### 3.5.1 Mortgage Debt
- **FR-LIAB-001**: Support primary mortgage with rate and term
- **FR-LIAB-002**: Support secondary mortgages/HELOC
- **FR-LIAB-003**: Calculate remaining balance from payment history
- **FR-LIAB-004**: Link to associated property asset

#### 3.5.2 Consumer Debt
- **FR-LIAB-005**: Support auto loans with payment details
- **FR-LIAB-006**: Support credit card debt with minimum payments
- **FR-LIAB-007**: Support personal loans
- **FR-LIAB-008**: Support student loans with type (federal/private)

#### 3.5.3 Other Liabilities
- **FR-LIAB-009**: Support 401k loans
- **FR-LIAB-010**: Support business loans
- **FR-LIAB-011**: Support tax debt
- **FR-LIAB-012**: Calculate total monthly debt payments

### 3.6 Insurance Section

#### 3.6.1 Life Insurance
- **FR-INSURANCE-001**: Support term life policies
- **FR-INSURANCE-002**: Support permanent life policies
- **FR-INSURANCE-003**: Collect death benefit amounts
- **FR-INSURANCE-004**: Collect beneficiary information

#### 3.6.2 Disability Insurance
- **FR-INSURANCE-005**: Support short-term disability
- **FR-INSURANCE-006**: Support long-term disability
- **FR-INSURANCE-007**: Collect benefit percentages
- **FR-INSURANCE-008**: Collect elimination periods

#### 3.6.3 Other Insurance
- **FR-INSURANCE-009**: Support long-term care insurance
- **FR-INSURANCE-010**: Support umbrella policies
- **FR-INSURANCE-011**: Track premium payments
- **FR-INSURANCE-012**: Calculate total insurance costs

### 3.7 Income Section

#### 3.7.1 Employment Income
- **FR-INCOME-001**: Support W2 employment income
- **FR-INCOME-002**: Support 1099 contractor income
- **FR-INCOME-003**: Support bonus and commission tracking
- **FR-INCOME-004**: Calculate gross vs net income

#### 3.7.2 Investment Income
- **FR-INCOME-005**: Support dividend income
- **FR-INCOME-006**: Support interest income
- **FR-INCOME-007**: Support rental property income
- **FR-INCOME-008**: Support capital gains tracking

#### 3.7.3 Other Income
- **FR-INCOME-009**: Support retirement income (pension, social security)
- **FR-INCOME-010**: Support alimony/child support
- **FR-INCOME-011**: Support business income
- **FR-INCOME-012**: Calculate total monthly/annual income

### 3.8 Expenses Section

#### 3.8.1 Fixed Expenses
- **FR-EXPENSE-001**: Housing expenses (mortgage/rent, utilities)
- **FR-EXPENSE-002**: Transportation (car payments, insurance)
- **FR-EXPENSE-003**: Insurance premiums
- **FR-EXPENSE-004**: Debt payments

#### 3.8.2 Variable Expenses
- **FR-EXPENSE-005**: Food and dining
- **FR-EXPENSE-006**: Entertainment and recreation
- **FR-EXPENSE-007**: Shopping and personal care
- **FR-EXPENSE-008**: Healthcare costs

#### 3.8.3 Savings & Investments
- **FR-EXPENSE-009**: Retirement contributions
- **FR-EXPENSE-010**: Emergency fund savings
- **FR-EXPENSE-011**: Investment contributions
- **FR-EXPENSE-012**: Education savings

### 3.9 Review & Completion

#### 3.9.1 Data Review
- **FR-REVIEW-001**: Display summary of all entered information
- **FR-REVIEW-002**: Allow inline editing from review screen
- **FR-REVIEW-003**: Highlight missing required information
- **FR-REVIEW-004**: Calculate key financial metrics

#### 3.9.2 Submission
- **FR-REVIEW-005**: Validate all required fields before submission
- **FR-REVIEW-006**: Generate PDF summary of intake data
- **FR-REVIEW-007**: Send confirmation email to client
- **FR-REVIEW-008**: Transition to client dashboard after completion

## 4. Non-Functional Requirements

### 4.1 Usability
- **NFR-USE-001**: Forms shall be mobile-responsive
- **NFR-USE-002**: Average completion time shall be under 30 minutes
- **NFR-USE-003**: Forms shall support keyboard navigation
- **NFR-USE-004**: Error messages shall be clear and actionable

### 4.2 Performance
- **NFR-PERF-001**: Form pages shall load within 2 seconds
- **NFR-PERF-002**: Auto-save shall complete within 500ms
- **NFR-PERF-003**: Navigation between steps shall be instant

### 4.3 Accessibility
- **NFR-ACCESS-001**: Meet WCAG 2.1 AA standards
- **NFR-ACCESS-002**: Support screen readers
- **NFR-ACCESS-003**: Provide keyboard-only navigation
- **NFR-ACCESS-004**: Maintain 4.5:1 color contrast ratio

## 5. User Stories

### 5.1 New Client Stories
- **US-001**: As a new client, I want to save my progress so I can complete intake later
- **US-002**: As a new client, I want clear instructions so I know what information to provide
- **US-003**: As a new client, I want to see my progress so I know how much is left
- **US-004**: As a new client, I want to review my information before submitting

### 5.2 Complex Client Stories
- **US-005**: As a client with investments, I want to enter detailed portfolio information
- **US-006**: As a business owner, I want to include business assets and income
- **US-007**: As a married client, I want to include spouse's financial information

### 5.3 Advisor Stories
- **US-008**: As an advisor, I want to see client's intake progress
- **US-009**: As an advisor, I want to help clients complete difficult sections
- **US-010**: As an advisor, I want to review submitted intake data

## 6. Data Validation Rules

### 6.1 Personal Information
- Email: Valid email format required
- Phone: Valid phone number with country code
- Date of Birth: Must be between 18-120 years old
- ZIP Code: Valid US ZIP code format

### 6.2 Financial Data
- Currency: All amounts in USD, positive values unless debt
- Percentages: Between 0-100
- Dates: Cannot be future dates for historical data
- Interest Rates: Between 0-50% APR

### 6.3 Business Rules
- Net Worth: Assets - Liabilities
- Debt-to-Income: Total debt payments / Gross income
- Savings Rate: Savings / Gross income
- Emergency Fund: 3-6 months of expenses

## 7. Technical Requirements

### 7.1 API Endpoints
- `GET /api/intake/progress` - Get intake progress
- `POST /api/intake/personal` - Save personal information
- `POST /api/intake/assets` - Save assets data
- `POST /api/intake/liabilities` - Save liabilities data
- `POST /api/intake/income` - Save income data
- `POST /api/intake/expenses` - Save expenses data
- `POST /api/intake/submit` - Submit completed intake
- `GET /api/intake/summary` - Get intake summary

### 7.2 Data Models
```typescript
interface IntakeProgress {
  userId: string;
  status: 'draft' | 'completed';
  currentStep: string;
  completionPercentage: number;
  lastUpdated: Date;
  data: IntakeData;
}

interface IntakeData {
  personal: PersonalInfo;
  situation: SituationInfo;
  assets: Asset[];
  liabilities: Liability[];
  insurance: Insurance[];
  income: Income[];
  expenses: Expense[];
  calculatedMetrics: FinancialMetrics;
}
```

## 8. Integration Points
- OCR service for document scanning
- Address validation service
- Bank account verification (Plaid)
- Credit report integration (future)

## 9. Risks & Mitigation
- **Risk**: Users abandon due to length
  - **Mitigation**: Save progress, send reminder emails
- **Risk**: Inaccurate data entry
  - **Mitigation**: Validation rules, confirmation screens
- **Risk**: Sensitive data exposure
  - **Mitigation**: Encryption, secure sessions