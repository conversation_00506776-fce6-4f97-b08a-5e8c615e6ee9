# Product Requirements Document: Income Management Module

## 1. Overview

### 1.1 Purpose
The Income Management module enables users to track, categorize, and analyze all sources of income. This module provides comprehensive income tracking with support for various income types, tax implications, and future income projections to help users understand and optimize their cash flow.

### 1.2 Scope
This module covers all income types including employment income, investment income, business income, retirement income, and passive income sources, with support for frequency tracking, tax withholding, and paystub OCR integration.

## 2. Income Categories

### 2.1 Employment Income
- **W2 Employment**: Salary, hourly wages, overtime
- **1099 Contract**: Independent contractor income
- **Bonuses**: Performance, signing, retention bonuses
- **Commissions**: Sales-based compensation
- **Tips**: Service industry gratuities

### 2.2 Investment Income
- **Dividends**: Stock dividends (qualified/non-qualified)
- **Interest**: Bond interest, savings account interest
- **Capital Gains**: Realized gains from investments
- **Rental Income**: Property rental receipts
- **Royalties**: Intellectual property income

### 2.3 Retirement Income
- **Social Security**: Government retirement benefits
- **Pension**: Employer pension payments
- **Annuities**: Insurance annuity distributions
- **Retirement Accounts**: 401(k), IRA distributions
- **Veterans Benefits**: Military retirement/disability

### 2.4 Other Income
- **Alimony**: Spousal support received
- **Child Support**: Child support payments
- **Disability**: Short/long-term disability benefits
- **Unemployment**: State unemployment benefits
- **Side Hustles**: Gig economy, small business

## 3. Functional Requirements

### 3.1 Income Entry & Management

#### 3.1.1 Income Source Creation
- **FR-INCOME-001**: Users shall add multiple income sources
- **FR-INCOME-002**: System shall categorize income by type
- **FR-INCOME-003**: Users shall specify payment frequency
- **FR-INCOME-004**: System shall track employer/payer information
- **FR-INCOME-005**: Users shall set start/end dates for income

#### 3.1.2 Income Details
- **FR-INCOME-006**: Track gross income amount
- **FR-INCOME-007**: Record tax withholdings (federal, state, local)
- **FR-INCOME-008**: Monitor pre-tax deductions (401k, insurance)
- **FR-INCOME-009**: Calculate net take-home pay
- **FR-INCOME-010**: Store direct deposit information

### 3.2 Employment Income Features

#### 3.2.1 Paycheck Management
- **FR-EMP-001**: Track regular salary/hourly rate
- **FR-EMP-002**: Calculate overtime pay (1.5x, 2x rates)
- **FR-EMP-003**: Monitor shift differentials
- **FR-EMP-004**: Track paid time off balances
- **FR-EMP-005**: Record employer benefits value

#### 3.2.2 Deductions & Withholdings
- **FR-EMP-006**: Track federal income tax withholding
- **FR-EMP-007**: Monitor state and local tax withholding
- **FR-EMP-008**: Record FICA taxes (Social Security, Medicare)
- **FR-EMP-009**: Track retirement contributions (401k, 403b)
- **FR-EMP-010**: Monitor insurance premiums and HSA contributions

#### 3.2.3 Variable Compensation
- **FR-EMP-011**: Track bonus payments and frequency
- **FR-EMP-012**: Calculate commission based on rules
- **FR-EMP-013**: Project annual compensation
- **FR-EMP-014**: Monitor stock compensation (RSUs, options)
- **FR-EMP-015**: Track profit sharing distributions

### 3.3 Paystub Integration

#### 3.3.1 Paystub Upload
- **FR-PAYSTUB-001**: Upload paystub documents (PDF, image)
- **FR-PAYSTUB-002**: Support drag-and-drop file upload
- **FR-PAYSTUB-003**: Validate file format and size
- **FR-PAYSTUB-004**: Store paystubs securely
- **FR-PAYSTUB-005**: Organize paystubs by date

#### 3.3.2 OCR Processing
- **FR-PAYSTUB-006**: Extract data using Azure Document Intelligence
- **FR-PAYSTUB-007**: Identify income components automatically
- **FR-PAYSTUB-008**: Extract YTD totals
- **FR-PAYSTUB-009**: Validate extracted data accuracy
- **FR-PAYSTUB-010**: Allow manual correction of OCR results

#### 3.3.3 Data Integration
- **FR-PAYSTUB-011**: Auto-populate income fields from paystub
- **FR-PAYSTUB-012**: Update running totals from paystubs
- **FR-PAYSTUB-013**: Flag discrepancies for review
- **FR-PAYSTUB-014**: Track paystub history
- **FR-PAYSTUB-015**: Generate income verification reports

### 3.4 Investment Income Features

#### 3.4.1 Dividend & Interest Tracking
- **FR-INVEST-001**: Track dividend payments by source
- **FR-INVEST-002**: Differentiate qualified vs non-qualified dividends
- **FR-INVEST-003**: Monitor interest income by account
- **FR-INVEST-004**: Track foreign tax paid
- **FR-INVEST-005**: Calculate dividend yield

#### 3.4.2 Capital Gains Management
- **FR-INVEST-006**: Record realized capital gains/losses
- **FR-INVEST-007**: Track short-term vs long-term gains
- **FR-INVEST-008**: Calculate tax implications
- **FR-INVEST-009**: Monitor wash sale adjustments
- **FR-INVEST-010**: Generate Schedule D reports

### 3.5 Retirement Income Features

#### 3.5.1 Social Security Management
- **FR-RETIRE-001**: Track monthly benefit amount
- **FR-RETIRE-002**: Monitor COLA adjustments
- **FR-RETIRE-003**: Calculate taxation of benefits
- **FR-RETIRE-004**: Project future benefits
- **FR-RETIRE-005**: Track Medicare premiums

#### 3.5.2 Pension & Annuity Tracking
- **FR-RETIRE-006**: Monitor pension payments
- **FR-RETIRE-007**: Track survivor benefits
- **FR-RETIRE-008**: Calculate pension present value
- **FR-RETIRE-009**: Monitor annuity distributions
- **FR-RETIRE-010**: Track cost basis for annuities

### 3.6 Income Analysis & Projections

#### 3.6.1 Income Metrics
- **FR-ANALYSIS-001**: Calculate total monthly/annual income
- **FR-ANALYSIS-002**: Show income by category breakdown
- **FR-ANALYSIS-003**: Track income trends over time
- **FR-ANALYSIS-004**: Calculate effective tax rate
- **FR-ANALYSIS-005**: Monitor income stability score

#### 3.6.2 Projections & Planning
- **FR-ANALYSIS-006**: Project future income based on trends
- **FR-ANALYSIS-007**: Model income scenarios (job change, retirement)
- **FR-ANALYSIS-008**: Calculate income replacement needs
- **FR-ANALYSIS-009**: Generate income verification letters
- **FR-ANALYSIS-010**: Export income data for taxes

### 3.7 Tax Management

#### 3.7.1 Tax Withholding
- **FR-TAX-001**: Track federal withholding by source
- **FR-TAX-002**: Monitor state tax withholding
- **FR-TAX-003**: Calculate estimated tax payments needed
- **FR-TAX-004**: Track tax refunds received
- **FR-TAX-005**: Generate W-4 optimization suggestions

#### 3.7.2 Tax Reporting
- **FR-TAX-006**: Generate income summary for tax filing
- **FR-TAX-007**: Track 1099 income by payer
- **FR-TAX-008**: Monitor tax deductions at source
- **FR-TAX-009**: Calculate quarterly estimates
- **FR-TAX-010**: Export to tax software formats

## 4. Non-Functional Requirements

### 4.1 Accuracy
- **NFR-ACC-001**: Calculations accurate to penny
- **NFR-ACC-002**: Tax withholding matches paycheck
- **NFR-ACC-003**: YTD totals reconcile correctly
- **NFR-ACC-004**: OCR accuracy above 95%

### 4.2 Performance
- **NFR-PERF-001**: Paystub OCR completes within 10 seconds
- **NFR-PERF-002**: Income calculations update in real-time
- **NFR-PERF-003**: Support 5 years of income history
- **NFR-PERF-004**: Reports generate within 3 seconds

### 4.3 Security
- **NFR-SEC-001**: Encrypt paystub documents at rest
- **NFR-SEC-002**: Secure OCR processing pipeline
- **NFR-SEC-003**: Mask sensitive income data
- **NFR-SEC-004**: Audit trail for income changes

### 4.4 Compliance
- **NFR-COMP-001**: Comply with IRS reporting requirements
- **NFR-COMP-002**: Support state tax variations
- **NFR-COMP-003**: Maintain data for 7 years
- **NFR-COMP-004**: Generate audit-ready reports

## 5. User Stories

### 5.1 Basic Income Tracking
- **US-001**: As an employee, I want to track my salary so I know my income
- **US-002**: As a user, I want to upload paystubs to save data entry time
- **US-003**: As a freelancer, I want to track multiple income sources
- **US-004**: As a user, I want to see my total income at a glance

### 5.2 Tax Planning
- **US-005**: As a taxpayer, I want to optimize my withholdings
- **US-006**: As a contractor, I want to calculate quarterly taxes
- **US-007**: As an investor, I want to track investment income
- **US-008**: As a user, I want tax-ready income reports

### 5.3 Financial Planning
- **US-009**: As a user, I want to project future income
- **US-010**: As a retiree, I want to track retirement income
- **US-011**: As a parent, I want to verify income for applications
- **US-012**: As a user, I want to analyze income trends

## 6. Data Models

```typescript
interface IncomeSource {
  id: string;
  userId: string;
  name: string;
  category: IncomeCategory;
  type: IncomeType;
  employer?: EmployerInfo;
  frequency: PaymentFrequency;
  grossAmount: number;
  netAmount: number;
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  taxTreatment: TaxTreatment;
}

interface EmploymentIncome extends IncomeSource {
  employmentType: 'w2' | '1099';
  baseSalary?: number;
  hourlyRate?: number;
  hoursPerWeek?: number;
  overtime?: OvertimeRules;
  deductions: Deduction[];
  benefits: BenefitValue[];
  paystubs: Paystub[];
}

interface Paystub {
  id: string;
  incomeSourceId: string;
  payPeriodStart: Date;
  payPeriodEnd: Date;
  payDate: Date;
  grossPay: number;
  netPay: number;
  regularHours?: number;
  overtimeHours?: number;
  deductions: PaystubDeduction[];
  taxes: TaxWithholding[];
  ytdTotals: YTDTotals;
  documentUrl?: string;
  ocrProcessed: boolean;
  ocrConfidence?: number;
}

interface InvestmentIncome extends IncomeSource {
  investmentType: 'dividend' | 'interest' | 'capitalGain' | 'rental';
  account?: string;
  symbol?: string;
  qualified?: boolean;
  foreignTaxPaid?: number;
  costBasis?: number;
}

interface RetirementIncome extends IncomeSource {
  retirementType: 'socialSecurity' | 'pension' | 'annuity' | '401k' | 'ira';
  benefitAmount: number;
  survivorBenefit?: boolean;
  colaAdjustment?: boolean;
  taxablePercentage: number;
}

interface IncomeProjection {
  incomeSourceId: string;
  projectionPeriod: number; // months
  assumptions: ProjectionAssumptions;
  projectedAmounts: MonthlyProjection[];
  confidence: 'high' | 'medium' | 'low';
}
```

## 7. API Endpoints

- `GET /api/income` - List all income sources
- `GET /api/income/:id` - Get specific income details
- `POST /api/income` - Create new income source
- `PUT /api/income/:id` - Update income source
- `DELETE /api/income/:id` - Delete income source
- `POST /api/income/:id/paystubs` - Upload paystub
- `POST /api/income/paystubs/ocr` - Process paystub with OCR
- `GET /api/income/summary` - Get income summary
- `GET /api/income/projections` - Get income projections
- `GET /api/income/tax-summary` - Get tax withholding summary

## 8. Integration Points

### 8.1 External Services
- Azure Document Intelligence for OCR
- Azure Blob Storage for documents
- Payroll provider APIs (future)
- Tax calculation services
- Bank transaction import

### 8.2 Internal Modules
- Tax calculation engine
- Document storage service
- Notification service
- Reporting module

## 9. Acceptance Criteria

### 9.1 Income Management
- [ ] Users can add all income types
- [ ] Income calculations are accurate
- [ ] Paystub upload works smoothly
- [ ] OCR extracts data correctly

### 9.2 Analysis Features
- [ ] Income summaries display correctly
- [ ] Tax calculations are accurate
- [ ] Projections use realistic assumptions
- [ ] Reports generate properly

### 9.3 User Experience
- [ ] Paystub upload is intuitive
- [ ] OCR corrections are easy
- [ ] Income trends visualize clearly
- [ ] Mobile experience works well

## 10. Future Enhancements
- Direct payroll provider integration
- Automated bank transaction categorization
- Multi-currency support
- Cryptocurrency income tracking
- Real-time income notifications
- Voice-based income entry