# Product Requirements Document: Organization Administrator Portal Module

## 1. Overview

### 1.1 Purpose
The Organization Administrator Portal module provides organization-level administrators with comprehensive tools to manage their organization's advisors, teams, households, and billing within the FinPro platform.

### 1.2 Scope
This module covers advisor administration, team management, billing management, subscription control, and organization-wide reporting capabilities designed for organization administrators.

## 2. Core Capabilities

### 2.1 Dashboard Overview
- **Advisor Analytics**: Total count of advisors
- **Household Metrics**: Total count of households
- **Activity Monitoring**: Last login tracking of advisors
- **Team Management**: Team structure overview

### 2.2 Advisor Management
- **Advisor Administration**: View and manage advisors
- **Team Assignment**: Organize advisors into teams
- **Status Management**: Monitor advisor status
- **Household Tracking**: View advisor household assignments

### 2.3 Billing & Subscription Management
- **Billing Overview**: Current plan and outstanding balance
- **Payment Methods**: Add and manage payment methods
- **Invoice Management**: View and pay invoices
- **Subscription Control**: Change or cancel subscription

## 3. Functional Requirements

### 3.1 Overview Dashboard

#### 3.1.1 Dashboard Metrics
- **FR-DASH-001**: Display total count of advisors in organization
- **FR-DASH-002**: Display total count of households across all advisors

### 3.2 Advisor Management

#### 3.2.1 Advisor Table
- **FR-ADVISOR-001**: Display searchable list of all advisors
- **FR-ADVISOR-002**: Show advisor name and profile information
- **FR-ADVISOR-003**: Display team assignment (if applicable)
- **FR-ADVISOR-004**: Show advisor status (Active/Inactive/Suspended)
- **FR-ADVISOR-005**: Display number of households per advisor
- **FR-ADVISOR-006**: Show last login timestamp
- **FR-ADVISOR-007**: Enable sorting by all columns
- **FR-ADVISOR-008**: Implement advanced filtering options

#### 3.2.2 Search and Filter Capabilities
- **FR-SEARCH-001**: Search advisors by name
- **FR-SEARCH-002**: Search advisors by email
- **FR-SEARCH-003**: Filter by team assignment
- **FR-SEARCH-004**: Filter by status
- **FR-SEARCH-005**: Filter by household count range
- **FR-SEARCH-006**: Filter by last login date range
- **FR-SEARCH-007**: Combine multiple filters

### 3.3 Team Management

#### 3.3.1 Team Administration
- **FR-TEAM-001**: View all teams in organization
- **FR-TEAM-002**: Create new teams
- **FR-TEAM-003**: Assign advisors to teams
- **FR-TEAM-004**: Remove advisors from teams
- **FR-TEAM-005**: View team performance metrics
- **FR-TEAM-006**: Activate/deactivate teams
- **FR-TEAM-007**: Soft delete teams
- **FR-TEAM-008**: View team status (active/inactive/deleted)

#### 3.3.2 Team Membership Rules
- **FR-TEAM-009**: Enforce single team membership per advisor
- **FR-TEAM-010**: Prevent advisor from joining multiple active teams
- **FR-TEAM-011**: Allow advisor to change teams
- **FR-TEAM-012**: Auto-remove all members when team is deleted
- **FR-TEAM-013**: Display warning when assigning advisor already in a team
- **FR-TEAM-014**: Show current team assignment in advisor profile
- **FR-TEAM-015**: Track team membership history

### 3.4 Advisor Actions

#### 3.4.1 Advisor Operations
- **FR-ACTION-001**: View detailed advisor profile
- **FR-ACTION-002**: Activate/deactivate advisor accounts
- **FR-ACTION-003**: Reset advisor passwords
- **FR-ACTION-004**: View advisor activity logs
- **FR-ACTION-005**: Export advisor data

### 3.5 Advisor Account Creation

#### 3.5.1 Account Creation Management
- **FR-CREATE-001**: Create new advisor accounts with email and profile information
- **FR-CREATE-002**: Check available seats before creating accounts
- **FR-CREATE-003**: Validate email domain matches organization domain
- **FR-CREATE-005**: Send welcome emails with login instructions
- **FR-CREATE-006**: Assign advisors to teams during account creation
- **FR-CREATE-007**: Set initial advisor status (Active/Inactive)
- **FR-CREATE-008**: Display remaining seats available
- **FR-CREATE-009**: Track account creation history

### 3.6 Billing Management

#### 3.6.1 Billing Overview
- **FR-BILL-001**: Display current subscription plan details
- **FR-BILL-002**: Show outstanding balance
- **FR-BILL-003**: Display default payment method
- **FR-BILL-004**: Show list of recent invoices
- **FR-BILL-005**: Display next billing date
- **FR-BILL-006**: Show usage against plan limits

#### 3.6.2 Payment Methods
- **FR-PAY-001**: Add new payment method (credit card/ACH)
- **FR-PAY-002**: Set default payment method
- **FR-PAY-003**: Remove payment method
- **FR-PAY-004**: Update payment method details
- **FR-PAY-005**: View payment method history

#### 3.6.3 Invoice Management
- **FR-INV-001**: View all invoices with status
- **FR-INV-002**: Download invoice PDFs
- **FR-INV-003**: Pay outstanding invoices
- **FR-INV-004**: View invoice line items
- **FR-INV-005**: Search invoices by date range
- **FR-INV-006**: Filter invoices by status (paid/unpaid/overdue)

#### 3.6.4 Subscription Management
- **FR-SUB-001**: View current plan features and limits
- **FR-SUB-002**: Compare available plans
- **FR-SUB-003**: Upgrade subscription plan
- **FR-SUB-004**: Downgrade subscription plan
- **FR-SUB-005**: Cancel subscription
- **FR-SUB-006**: Reactivate cancelled subscription
- **FR-SUB-007**: View subscription change history

## 4. Non-Functional Requirements

### 4.1 Performance
- **NFR-PERF-001**: Dashboard loads within 2 seconds
- **NFR-PERF-002**: Table supports 1000+ advisors with pagination
- **NFR-PERF-003**: Search results appear within 1 second
- **NFR-PERF-004**: Support 100 concurrent org admins

### 4.2 Security
- **NFR-SEC-001**: Role-based access control for org admins
- **NFR-SEC-002**: Audit all admin actions
- **NFR-SEC-005**: Domain validation for advisor invitations


### 4.5 Business Rules
- **NFR-BUS-001**: Enforce single team membership constraint
- **NFR-BUS-002**: Maintain team status integrity
- **NFR-BUS-003**: Ensure advisor-team relationship consistency
- **NFR-BUS-004**: Preserve team membership audit trail

## 5. User Stories

### 5.1 Organization Administration
- **US-001**: As an org admin, I want to see an overview of all advisors and households
- **US-002**: As an org admin, I want to search for specific advisors quickly
- **US-003**: As an org admin, I want to filter advisors by various criteria
- **US-004**: As an org admin, I want to track advisor login activity
- **US-005**: As an org admin, I want to manage advisor team assignments
- **US-006**: As an org admin, I want to export advisor data for reporting

### 5.2 Team Management
- **US-007**: As an org admin, I want advisors to only belong to one team at a time
- **US-008**: As an org admin, I want to deactivate teams without deleting them
- **US-009**: As an org admin, I want to delete teams and auto-remove all members
- **US-010**: As an org admin, I want to see warnings when assigning advisors to teams
- **US-011**: As an org admin, I want advisors to leave teams before joining new ones
- **US-012**: As an org admin, I want to track team membership changes

### 5.3 Advisor Account Creation
- **US-013**: As an org admin, I want to create new advisor accounts
- **US-014**: As an org admin, I want to see available seats before creating accounts
- **US-015**: As an org admin, I want to set initial advisor profile information
- **US-016**: As an org admin, I want to assign advisors to teams during account creation
- **US-017**: As an org admin, I want to validate email domain matches organization domain
- **US-018**: As an org admin, I want to send welcome emails to new advisors

### 5.4 Billing Administration
- **US-019**: As an org admin, I want to view my current plan and billing status
- **US-020**: As an org admin, I want to add or update payment methods
- **US-021**: As an org admin, I want to view and pay outstanding invoices
- **US-022**: As an org admin, I want to upgrade or downgrade my subscription
- **US-023**: As an org admin, I want to download invoices for accounting
- **US-024**: As an org admin, I want to cancel my subscription if needed


## 7. API Endpoints

### 7.1 Dashboard
- `GET /api/org-admin/dashboard` - Get dashboard metrics
- `GET /api/org-admin/dashboard/advisors/count` - Get advisor count
- `GET /api/org-admin/dashboard/households/count` - Get household count

### 7.2 Advisor Management
- `GET /api/org-admin/advisors` - List all advisors with pagination
- `GET /api/org-admin/advisors/:id` - Get advisor details
- `PUT /api/org-admin/advisors/:id/status` - Update advisor status
- `POST /api/org-admin/advisors/:id/reset-password` - Reset advisor password

### 7.3 Team Management
- `GET /api/org-admin/teams` - List all teams with status
- `POST /api/org-admin/teams` - Create team
- `PUT /api/org-admin/teams/:id` - Update team details
- `PUT /api/org-admin/teams/:id/status` - Change team status (active/inactive)
- `DELETE /api/org-admin/teams/:id` - Soft delete team
- `POST /api/org-admin/teams/:id/advisors/:advisorId` - Add advisor to team
- `DELETE /api/org-admin/teams/:id/advisors/:advisorId` - Remove advisor from team
- `GET /api/org-admin/advisors/:id/team-history` - Get advisor team history

### 7.4 Advisor Account Creation
- `POST /api/org-admin/advisors` - Create new advisor account
- `GET /api/org-admin/advisors/seats` - Check available seats
- `POST /api/org-admin/advisors/validate-email` - Validate email domain
- `GET /api/org-admin/advisors/creation-history` - Get account creation history
- `POST /api/org-admin/advisors/:id/send-welcome` - Send welcome email

### 7.5 Billing Management
- `GET /api/org-admin/billing/overview` - Get billing overview
- `GET /api/org-admin/billing/invoices` - List all invoices
- `GET /api/org-admin/billing/invoices/:id` - Get invoice details
- `POST /api/org-admin/billing/invoices/:id/pay` - Pay invoice
- `GET /api/org-admin/billing/payment-methods` - List payment methods
- `POST /api/org-admin/billing/payment-methods` - Add payment method
- `PUT /api/org-admin/billing/payment-methods/:id` - Update payment method
- `DELETE /api/org-admin/billing/payment-methods/:id` - Remove payment method

### 7.6 Subscription Management
- `GET /api/org-admin/subscription/current` - Get current subscription
- `GET /api/org-admin/subscription/plans` - List available plans
- `PUT /api/org-admin/subscription/change` - Change subscription plan
- `POST /api/org-admin/subscription/cancel` - Cancel subscription
- `POST /api/org-admin/subscription/reactivate` - Reactivate subscription

## 8. Acceptance Criteria

### 8.1 Dashboard
- [ ] Dashboard displays accurate advisor count
- [ ] Dashboard displays accurate household count

### 8.2 Advisor Management
- [ ] Advisor table displays all required columns
- [ ] Search functionality works across all fields
- [ ] Filters can be combined effectively
- [ ] Table supports sorting on all columns
- [ ] Pagination works for large datasets
- [ ] Last login times are accurate

### 8.3 Team Management
- [ ] Teams can be created and managed
- [ ] Advisors can be assigned to teams
- [ ] Team assignments display correctly in table
- [ ] Team filtering works as expected
- [ ] Advisor can only belong to one team at a time
- [ ] System prevents joining multiple active teams
- [ ] Warning appears when assigning advisor already in team
- [ ] Advisor must leave team before joining another
- [ ] Team deletion removes all members automatically
- [ ] Soft deleted teams retain data but release members
- [ ] Team membership history is tracked accurately

### 8.4 Advisor Account Creation
- [ ] Advisor accounts can only be created when seats are available
- [ ] Email domain validation prevents external account creation
- [ ] Account creation form validates all required fields
- [ ] Welcome emails are sent to new advisors
- [ ] Initial advisor status can be set during creation
- [ ] Advisors can be assigned to teams during account creation
- [ ] Available seats counter is accurate
- [ ] Account creation history is maintained
- [ ] Domain validation matches organization profile

### 8.5 Billing Management
- [ ] Billing overview displays current plan accurately
- [ ] Outstanding balance is calculated correctly
- [ ] Default payment method is displayed
- [ ] Recent invoices list is accurate and up-to-date
- [ ] Payment methods can be added successfully
- [ ] Payment methods can be updated and removed
- [ ] Invoices can be viewed and downloaded
- [ ] Invoice payments process correctly

### 8.6 Subscription Management
- [ ] Current plan details are displayed accurately
- [ ] Plan comparison shows all available options
- [ ] Subscription upgrades process correctly
- [ ] Subscription downgrades follow proper rules
- [ ] Cancellation process works with confirmations
- [ ] Reactivation is available for cancelled accounts
- [ ] All subscription changes are logged properly