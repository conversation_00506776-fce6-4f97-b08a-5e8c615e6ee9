# Technical Specification: AUTH-05 - Session Management

## 1. Feature Overview

### 1.1 Purpose
The Session Management feature provides comprehensive session lifecycle management including JWT token handling, refresh token rotation, session persistence, and security-focused session invalidation. This feature ensures secure and efficient user session management across the FinPro platform.

### 1.2 Scope
- JWT access and refresh token generation and validation
- Token refresh mechanism with rotation
- Session persistence and expiration management
- Token blacklisting for logout and security events
- Concurrent session support with security controls

### 1.3 Dependencies
- User Authentication (AUTH-02) for session creation
- Redis for token blacklisting and session caching
- Database for session audit logging
- Password Management (AUTH-03) for session invalidation

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-SESSION-001**: Sessions shall be stored server-side
- **FR-SESSION-002**: JWT tokens shall be stored in httpOnly cookies
- **FR-SESSION-003**: Sessions shall expire after 1 day of inactivity
- **FR-SESSION-004**: System shall support concurrent sessions per user
- **FR-SESSION-005**: System shall use secure JWT signing with HS256 algorithm

### 2.2 Token Management Requirements
- **FR-SESSION-006**: JWT access tokens shall have 15-minute expiration
- **FR-SESSION-007**: JWT refresh tokens shall have 7-day expiration
- **FR-SESSION-008**: System shall automatically refresh tokens when needed
- **FR-SESSION-009**: System shall invalidate all tokens on password change
- **FR-SESSION-010**: System shall maintain token blacklist for revoked tokens

### 2.3 Non-Functional Requirements
- **NFR-PERF-002**: Token validation shall complete within 100ms
- **NFR-PERF-003**: System shall support 1000 concurrent sessions

### 2.4 User Stories
- Sessions support transparent user experience across devices
- Automatic token refresh prevents interruption during active use
- Secure logout invalidates all user sessions

## 3. Technical Architecture

### 3.1 JWT Token Architecture
- **Access Token**: Short-lived (15 min), contains user claims and permissions
- **Refresh Token**: Long-lived (7 days), used only for token refresh
- **Token Storage**: httpOnly, secure, SameSite cookies for web clients
- **Token Signing**: HMAC-SHA256 with environment-based secret

### 3.2 Session Storage Strategy
- **Redis Cache**: Active sessions and token blacklists
- **Database**: Session audit logs and long-term tracking
- **Memory**: JTI (JWT ID) tracking for duplicate detection

### 3.3 API Endpoints
- **Token Refresh**: `POST /api/v1/auth/refresh`
- **Session Validation**: Internal middleware for all protected endpoints
- **Session Cleanup**: Background job for expired session cleanup

## 4. Database Schema

### 4.1 Session Audit Logging

#### user_sessions Table (New)
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(100) NOT NULL,
    access_token_jti VARCHAR(100),
    refresh_token_jti VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    device_fingerprint VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    ended_at TIMESTAMPTZ,
    end_reason session_end_reason
);

CREATE TYPE session_end_reason AS ENUM (
    'LOGOUT',
    'EXPIRED', 
    'PASSWORD_CHANGE',
    'ADMIN_REVOKED',
    'SECURITY_BREACH'
);
```

### 4.2 Redis Schema

#### Session Cache Structure
```redis
# Active session tracking
session:{session_id} = {
    "user_id": "uuid",
    "access_token_jti": "jti",
    "refresh_token_jti": "jti",
    "created_at": "timestamp",
    "last_activity": "timestamp",
    "ip_address": "ip",
    "user_agent": "agent"
}
TTL: 7 days

# Token blacklist
blacklist:{jti} = "revoked"
TTL: token_expiry + 1 hour

# User session count
user_sessions:{user_id} = set[session_id1, session_id2, ...]
TTL: 7 days
```

### 4.3 Required Indexes
```sql
CREATE INDEX idx_user_sessions_user_active 
ON user_sessions(user_id, is_active, last_activity_at);

CREATE INDEX idx_user_sessions_cleanup 
ON user_sessions(expires_at) WHERE is_active = true;

CREATE INDEX idx_user_sessions_jti 
ON user_sessions(access_token_jti) WHERE is_active = true;
```

## 5. JWT Token Specifications

### 5.1 Access Token Claims
```json
{
  "sub": "user_uuid",
  "email": "<EMAIL>",
  "role": "CLIENT",
  "permissions": ["read:own_data", "write:own_data"],
  "session_id": "session_uuid",
  "iat": 1640995200,
  "exp": 1640996100,
  "iss": "finpro-api",
  "aud": "finpro-web",
  "jti": "access_token_uuid"
}
```

### 5.2 Refresh Token Claims
```json
{
  "sub": "user_uuid",
  "type": "refresh",
  "session_id": "session_uuid",
  "iat": 1640995200,
  "exp": 1641600000,
  "iss": "finpro-api",
  "aud": "finpro-web",
  "jti": "refresh_token_uuid"
}
```

### 5.3 Token Security Configuration
- **Algorithm**: HS256 (HMAC with SHA-256)
- **Secret Key**: Environment variable (minimum 256 bits)
- **Key Rotation**: Support for multiple keys with key ID
- **Clock Skew**: 30-second tolerance for time drift

## 6. API Specifications

### 6.1 Token Refresh Endpoint

#### POST /api/v1/auth/refresh

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "refresh_token": {
      "type": "string",
      "description": "Valid refresh token"
    }
  },
  "required": ["refresh_token"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "access_token": "NEW_JWT_ACCESS_TOKEN",
    "token_type": "bearer",
    "expires_in": 900
  }
}
```

**Error Responses:**

**400 Invalid Refresh Token:**
```json
{
  "success": false,
  "error": "INVALID_REFRESH_TOKEN",
  "message": "Invalid or expired refresh token",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**401 Token Blacklisted:**
```json
{
  "success": false,
  "error": "TOKEN_REVOKED",
  "message": "Token has been revoked",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 7. Session Lifecycle Management

### 7.1 Session Creation Flow
1. User successfully authenticates
2. Generate unique session ID
3. Create access and refresh token with JTIs
4. Store session in Redis and database
5. Set httpOnly cookies with tokens
6. Return authentication response

### 7.2 Token Refresh Flow
1. Client presents expired/expiring access token and refresh token
2. Validate refresh token signature and claims
3. Check refresh token not blacklisted
4. Generate new access token with same session ID
5. Optionally rotate refresh token (security best practice)
6. Update session last activity timestamp
7. Return new access token

### 7.3 Session Termination Flow
1. User logs out or session expires
2. Add all session tokens to blacklist
3. Remove session from Redis cache
4. Mark session as ended in database
5. Clear httpOnly cookies
6. Log session end event

## 8. Token Validation Middleware

### 8.1 Access Token Validation
```python
def validate_access_token(token: str) -> TokenClaims:
    # 1. Verify JWT signature and structure
    # 2. Check token expiration
    # 3. Validate issuer and audience
    # 4. Check token not blacklisted (Redis)
    # 5. Validate session still active
    # 6. Return validated claims
```

### 8.2 Validation Performance
- **Cache Strategy**: Cache valid tokens for 5 minutes
- **Blacklist Check**: Redis SET membership test
- **Session Check**: Redis key existence check
- **Fallback**: Database lookup if Redis unavailable

## 9. Security Implementation

### 9.1 Token Security
- **Secure Token Generation**: Cryptographically secure JTI generation
- **Token Rotation**: Optional refresh token rotation on use
- **Token Binding**: Bind tokens to IP address/device fingerprint
- **Clock Skew Tolerance**: 30-second tolerance for time differences

### 9.2 Session Security
- **Session Timeout**: Configurable inactivity timeout (default 1 day)
- **Concurrent Sessions**: Configurable limit per user (default unlimited)
- **Device Tracking**: Track sessions by device fingerprint
- **Anomaly Detection**: Monitor unusual session patterns

### 9.3 Blacklist Management
- **Immediate Blacklisting**: Critical security events
- **Automatic Cleanup**: Remove expired tokens from blacklist
- **Distributed Blacklist**: Redis-based for multi-instance deployment

## 10. Error Handling

### 10.1 Token Errors
- `INVALID_TOKEN`: Malformed or invalid JWT
- `TOKEN_EXPIRED`: Token past expiration time
- `TOKEN_REVOKED`: Token in blacklist
- `INVALID_SIGNATURE`: JWT signature validation failed
- `INVALID_CLAIMS`: Required claims missing or invalid

### 10.2 Session Errors
- `SESSION_EXPIRED`: Session past expiration time
- `SESSION_NOT_FOUND`: Session ID not found
- `SESSION_INACTIVE`: Session marked as inactive
- `MAX_SESSIONS_EXCEEDED`: Too many concurrent sessions

## 11. Business Logic

### 11.1 Session Management Services
- **TokenService**: JWT generation, validation, and blacklisting
- **SessionService**: Session lifecycle and tracking
- **RefreshService**: Token refresh and rotation logic
- **SecurityService**: Anomaly detection and security enforcement

### 11.2 Background Jobs
- **Session Cleanup**: Remove expired sessions every hour
- **Blacklist Cleanup**: Remove expired blacklisted tokens daily
- **Session Analytics**: Generate session usage reports
- **Security Monitoring**: Alert on suspicious session patterns

## 12. Configuration Management

### 12.1 JWT Configuration
```python
JWT_CONFIG = {
    "access_token_expiry": 900,  # 15 minutes
    "refresh_token_expiry": 604800,  # 7 days
    "algorithm": "HS256",
    "issuer": "finpro-api",
    "audience": "finpro-web",
    "clock_skew_tolerance": 30
}
```

### 12.2 Session Configuration
```python
SESSION_CONFIG = {
    "session_timeout": 86400,  # 1 day
    "max_concurrent_sessions": None,  # Unlimited
    "refresh_token_rotation": True,
    "bind_to_ip": False,
    "device_fingerprinting": True
}
```

## 13. Testing Requirements

### 13.1 Unit Tests
- JWT token generation and validation
- Session creation and cleanup
- Token blacklisting logic
- Clock skew handling

### 13.2 Integration Tests
- Token refresh flow
- Session expiration handling
- Redis cache integration
- Middleware token validation

### 13.3 Load Tests
- Concurrent session handling
- Token validation performance
- Redis cache performance
- Session cleanup efficiency

## 14. Monitoring & Logging

### 14.1 Key Metrics
- Active session count
- Token refresh frequency
- Session duration analytics
- Token validation performance

### 14.2 Session Events
- Session creation and termination
- Token refresh events
- Session timeout events
- Security-related session events

### 14.3 Performance Metrics
- Token validation latency
- Redis cache hit rates
- Session cleanup efficiency
- Concurrent session peaks

## 15. Integration Points

### 15.1 Frontend Integration
- Automatic token refresh handling
- Session timeout warnings
- Concurrent session management
- Graceful session expiration handling

### 15.2 API Integration
- Authentication middleware for all protected endpoints
- Token extraction from cookies/headers
- Permission validation based on token claims
- Session context in request processing

### 15.3 Dependencies
- **AUTH-02 (User Authentication)**: Session creation triggers
- **AUTH-03 (Password Management)**: Session invalidation triggers
- **Redis**: Token blacklisting and session caching
- **Background Jobs**: Session and blacklist cleanup

### 15.4 Infrastructure Requirements
- **Redis Cluster**: For session storage and blacklisting
- **Load Balancer**: Session affinity not required (stateless)
- **CDN**: Token validation can be cached at edge
- **Monitoring**: Session metrics and alerting