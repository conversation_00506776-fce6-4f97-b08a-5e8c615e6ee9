# Technical Specification: AUTH-03 - Password Management

## 1. Feature Overview

### 1.1 Purpose
The Password Management feature provides comprehensive password lifecycle management including password reset functionality, password change capabilities, and password security enforcement. This feature ensures users can securely manage their account credentials while maintaining strong security standards.

### 1.2 Scope
- Forgotten password reset via email
- Authenticated password change
- Password complexity validation and enforcement
- Password history tracking to prevent reuse
- Secure token generation for password reset

### 1.3 Dependencies
- Email service (SMTP) for password reset emails
- User Management for account lookup
- Session Management for token invalidation
- Redis for token caching and rate limiting

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-AUTH-011**: System shall provide email-based password reset functionality
- **FR-AUTH-012**: Password reset tokens shall expire after 1 hour
- **FR-AUTH-013**: System shall enforce password complexity requirements
- **FR-AUTH-014**: System shall hash passwords using bcrypt with salt
- **FR-AUTH-015**: System shall maintain password history to prevent reuse of last 5 passwords

### 2.2 Non-Functional Requirements
- **NFR-SEC-001**: All authentication data shall be encrypted in transit using TLS 1.2+
- **NFR-SEC-002**: System shall log all authentication attempts
- **NFR-SEC-007**: System shall use time-limited tokens for password reset (15 minute expiry)

### 2.3 User Stories
- **US-012**: As a user, I want to reset my password if I forget it
- **US-013**: As a user, I want to change my password when logged in

## 3. Technical Architecture

### 3.1 Password Reset Flow
1. User requests password reset with email address
2. System validates email and generates secure reset token
3. System sends password reset email with token link
4. User clicks link and submits new password
5. System validates token and password complexity
6. System updates password and invalidates all user sessions

### 3.2 Password Change Flow
1. Authenticated user submits current and new passwords
2. System validates current password
3. System validates new password complexity and history
4. System updates password and invalidates all sessions
5. System forces re-authentication

### 3.3 API Endpoints
- **Forgot Password**: `POST /api/v1/auth/forgot-password`
- **Reset Password**: `POST /api/v1/auth/reset-password`
- **Change Password**: `POST /api/v1/auth/change-password`

## 4. Database Schema

### 4.1 Password Reset Tokens

#### auth_password_reset_tokens Table
```sql
CREATE TABLE auth_password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    issued_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL, -- 1 hour expiry
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMPTZ,
    ip_address INET,
    user_agent TEXT,
    reason reset_request_reason DEFAULT 'FORGOTTEN_PASSWORD',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE reset_request_reason AS ENUM (
    'FORGOTTEN_PASSWORD', 
    'SECURITY_BREACH', 
    'ADMIN_RESET'
);
```

### 4.2 Password History Enhancement
The existing `auth_user_passwords` table includes:
```sql
-- password_history column stores last 5 password hashes
password_history JSONB DEFAULT '[]' -- Array of {hash, created_at}
```

### 4.3 Required Indexes
```sql
CREATE INDEX idx_password_reset_tokens_user_active 
ON auth_password_reset_tokens(user_id, is_used, expires_at);

CREATE INDEX idx_password_reset_tokens_cleanup 
ON auth_password_reset_tokens(expires_at) WHERE is_used = false;
```

## 5. API Specifications

### 5.1 Forgot Password Endpoint

#### POST /api/v1/auth/forgot-password

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "email": {
      "type": "string",
      "format": "email",
      "description": "Email address for password reset"
    }
  },
  "required": ["email"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset email sent successfully"
}
```

**Rate Limit Error (429):**
```json
{
  "success": false,
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Too many password reset requests. Please try again later.",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 5.2 Reset Password Endpoint

#### POST /api/v1/auth/reset-password

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "token": {
      "type": "string",
      "description": "Password reset token from email"
    },
    "password": {
      "type": "string",
      "minLength": 8,
      "description": "New password meeting complexity requirements"
    }
  },
  "required": ["token", "password"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Error Responses:**

**400 Invalid Token:**
```json
{
  "success": false,
  "error": "INVALID_RESET_TOKEN",
  "message": "Invalid or expired reset token",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**400 Password History:**
```json
{
  "success": false,
  "error": "PASSWORD_RECENTLY_USED",
  "message": "Password was recently used. Please choose a different password.",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 5.3 Change Password Endpoint

#### POST /api/v1/auth/change-password

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "current_password": {
      "type": "string",
      "description": "Current password for verification"
    },
    "new_password": {
      "type": "string",
      "minLength": 8,
      "description": "New password meeting complexity requirements"
    }
  },
  "required": ["current_password", "new_password"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password changed successfully. Please log in again."
}
```

**Error Responses:**

**400 Current Password Incorrect:**
```json
{
  "success": false,
  "error": "INVALID_CURRENT_PASSWORD",
  "message": "Current password is incorrect",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**400 Same Password:**
```json
{
  "success": false,
  "error": "SAME_PASSWORD",
  "message": "New password must be different from current password",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 6. Password Security Implementation

### 6.1 Password Complexity Requirements
- **Minimum Length**: 8 characters
- **Uppercase**: At least one uppercase letter (A-Z)
- **Lowercase**: At least one lowercase letter (a-z)
- **Numbers**: At least one digit (0-9)
- **Special Characters**: At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)

### 6.2 Password Validation Regex
```regex
^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]).{8,}$
```

### 6.3 Password Hashing
- **Algorithm**: bcrypt with salt rounds = 12
- **Salt**: Unique per password, generated by bcrypt
- **Storage**: Only hashed password stored, never plaintext

### 6.4 Password History
- Store last 5 password hashes in JSONB array
- Check new passwords against history before acceptance
- Include creation timestamp for each historical password

```json
{
  "password_history": [
    {
      "hash": "bcrypt_hash_1",
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "hash": "bcrypt_hash_2", 
      "created_at": "2024-01-02T00:00:00Z"
    }
  ]
}
```

## 7. Token Security

### 7.1 Reset Token Generation
- **Length**: 32 bytes (256 bits)
- **Encoding**: URL-safe Base64
- **Source**: Cryptographically secure random number generator
- **Hashing**: SHA-256 before database storage

### 7.2 Token Expiration
- **Password Reset**: 1 hour (3600 seconds)
- **Security Reset**: 15 minutes (900 seconds)
- **Cleanup**: Automated cleanup of expired tokens

### 7.3 Token Usage Rules
- Single-use tokens (marked as used after successful reset)
- IP address and user agent tracking
- Maximum 3 active tokens per user
- Rate limiting: 3 requests per hour per email

## 8. Security Implementation

### 8.1 Rate Limiting
- **Forgot Password**: 3 requests per hour per email
- **Reset Password**: 5 attempts per token
- **Change Password**: 5 attempts per hour per user

### 8.2 Session Invalidation
- Invalidate all user sessions on password change
- Clear all JWT tokens from blacklist cache
- Force re-authentication on next request

### 8.3 Security Logging
- Log all password change attempts
- Track failed authentication after password reset
- Monitor unusual password reset patterns

## 9. Error Handling

### 9.1 Password Reset Errors
- `INVALID_EMAIL`: Email format validation failure
- `USER_NOT_FOUND`: Email not associated with account (silent fail)
- `RATE_LIMIT_EXCEEDED`: Too many reset requests
- `INVALID_RESET_TOKEN`: Token expired, used, or invalid
- `PASSWORD_RECENTLY_USED`: Password in history

### 9.2 Password Change Errors
- `INVALID_CURRENT_PASSWORD`: Current password incorrect
- `SAME_PASSWORD`: New password same as current
- `PASSWORD_COMPLEXITY`: New password doesn't meet requirements
- `PASSWORD_RECENTLY_USED`: Password in history

## 10. Business Logic

### 10.1 Password Management Services
- **PasswordResetService**: Handle reset token lifecycle
- **PasswordValidationService**: Validate complexity and history
- **PasswordHashingService**: Secure hashing and verification
- **SessionInvalidationService**: Clear user sessions

### 10.2 Reset Token Lifecycle
1. Generate cryptographically secure token
2. Hash token for storage (SHA-256)
3. Set expiration (1 hour for normal, 15 min for security)
4. Send reset email with original token
5. Validate token on reset attempt
6. Mark token as used on successful reset

## 11. Email Templates

### 11.1 Password Reset Email
```html
Subject: Reset Your FinPro Password

Hello {{first_name}},

You requested a password reset for your FinPro account.

Click the link below to reset your password:
{{reset_link}}

This link will expire in 1 hour for security reasons.

If you didn't request this reset, please ignore this email.

Best regards,
The FinPro Team
```

### 11.2 Password Changed Notification
```html
Subject: Your FinPro Password Was Changed

Hello {{first_name}},

Your password was successfully changed on {{change_date}}.

If you didn't make this change, please contact support immediately.

Best regards,
The FinPro Team
```

## 12. Testing Requirements

### 12.1 Unit Tests
- Password complexity validation
- Token generation and validation
- Password history checking
- BCrypt hashing and verification

### 12.2 Integration Tests
- Complete password reset flow
- Password change with session invalidation
- Rate limiting enforcement
- Email delivery integration

### 12.3 Security Tests
- Token manipulation attempts
- Brute force protection
- Session hijacking prevention
- Password history bypass attempts

## 13. Monitoring & Logging

### 13.1 Key Metrics
- Password reset request rates
- Successful vs failed reset attempts
- Password complexity failure patterns
- Session invalidation events

### 13.2 Security Events
- All password reset requests
- Failed password reset attempts
- Password change events
- Suspicious password patterns

### 13.3 Performance Metrics
- Password hashing performance
- Email delivery success rates
- Token validation latency
- Database query performance

## 14. Integration Points

### 14.1 Frontend Integration
- Password reset form component
- Password strength indicator
- Password change form
- Session handling after password change

### 14.2 Email Service Integration
- Password reset email templates
- Email delivery confirmation
- Retry logic for failed deliveries
- Template customization per organization

### 14.3 Dependencies
- **AUTH-01 (User Registration)**: User account management
- **AUTH-02 (User Authentication)**: Session invalidation
- **AUTH-05 (Session Management)**: Token blacklisting
- **EMAIL (Email Communication)**: Template and delivery service