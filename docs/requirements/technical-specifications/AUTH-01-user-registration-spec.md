# Technical Specification: AUTH-01 - User Registration

## 1. Feature Overview

### 1.1 Purpose
The User Registration feature enables new users to create accounts in the FinPro system with email and password authentication. This feature provides a secure foundation for user onboarding with email verification requirements.

### 1.2 Scope
- New user account creation via API endpoint
- Password complexity validation and secure storage
- Email verification token generation and management
- Integration with authentication system and user management

### 1.3 Dependencies
- Email service (SMTP) for verification emails
- Database (PostgreSQL) for user and token storage
- Redis for token caching and rate limiting
- bcrypt for password hashing

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-AUTH-001**: System shall support JWT-based authentication with email/password
- **FR-AUTH-007**: System shall require email verification for new accounts
- **FR-AUTH-013**: System shall enforce password complexity requirements
- **FR-AUTH-014**: System shall hash passwords using bcrypt with salt

### 2.2 Non-Functional Requirements
- **NFR-SEC-001**: All authentication data shall be encrypted in transit using TLS 1.2+
- **NFR-SEC-002**: System shall log all authentication attempts
- **NFR-SEC-003**: System shall implement rate limiting on registration attempts (5 per minute)
- **NFR-SEC-006**: System shall require email verification for account activation

### 2.3 User Stories
- **US-010**: As a new user, I want to register with my email and password
- **US-011**: As a new user, I want to verify my email to activate my account

## 3. Technical Architecture

### 3.1 API Endpoint
- **Endpoint**: `POST /api/v1/auth/register`
- **Authentication**: None required (public endpoint)
- **Rate Limiting**: 5 requests per minute per IP address

### 3.2 Request/Response Flow
1. User submits registration form with email, password, and optional profile data
2. System validates input data and password complexity
3. System checks for existing email address
4. System hashes password using bcrypt with salt
5. System creates user record with PENDING_VERIFICATION status
6. System generates email verification token
7. System sends verification email
8. System returns registration success response

### 3.3 Data Flow Architecture
```
Client → API Gateway → Registration Service → Database
                    ↓
               Email Service
```

## 4. Database Schema

### 4.1 Primary Tables

#### users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role user_role NOT NULL DEFAULT 'CLIENT',
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMPTZ,
    status user_status DEFAULT 'PENDING_VERIFICATION',
    last_login_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by_id UUID,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by_id UUID
);
```

#### auth_user_passwords Table
```sql
CREATE TABLE auth_user_passwords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    hashed_password VARCHAR(255) NOT NULL,
    salt VARCHAR(100) NOT NULL,
    algorithm password_hash_algorithm DEFAULT 'bcrypt',
    password_version INTEGER DEFAULT 1,
    is_temporary BOOLEAN DEFAULT FALSE,
    password_set_at TIMESTAMPTZ DEFAULT NOW(),
    password_expires_at TIMESTAMPTZ,
    last_password_change TIMESTAMPTZ DEFAULT NOW(),
    password_history JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ
);
```

#### auth_email_verification_tokens Table
```sql
CREATE TABLE auth_email_verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    verification_type verification_type DEFAULT 'REGISTRATION',
    issued_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMPTZ,
    ip_address INET,
    user_agent TEXT,
    attempt_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 4.2 Enum Types
```sql
CREATE TYPE user_role AS ENUM ('CLIENT', 'ADVISOR', 'ADMINISTRATOR');
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION');
CREATE TYPE password_hash_algorithm AS ENUM ('bcrypt');
CREATE TYPE verification_type AS ENUM ('REGISTRATION', 'EMAIL_CHANGE');
```

## 5. API Specifications

### 5.1 Registration Endpoint

#### POST /api/v1/auth/register

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "email": {
      "type": "string",
      "format": "email",
      "description": "Valid email address"
    },
    "password": {
      "type": "string",
      "minLength": 8,
      "description": "Password meeting complexity requirements"
    },
    "first_name": {
      "type": "string",
      "maxLength": 100,
      "description": "User first name"
    },
    "last_name": {
      "type": "string",
      "maxLength": 100,
      "description": "User last name"
    },
    "role": {
      "type": "string",
      "enum": ["CLIENT", "ADVISOR", "ADMINISTRATOR"],
      "default": "CLIENT"
    }
  },
  "required": ["email", "password"]
}
```

**Response Schemas:**

**201 Success:**
```json
{
  "success": true,
  "message": "Registration successful. Please check your email to verify your account.",
  "data": {
    "user_id": "uuid",
    "email": "string",
    "email_verified": false
  }
}
```

**400 Validation Error:**
```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Validation failed",
  "details": [
    {
      "field": "password",
      "message": "Password must contain at least one uppercase letter"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**409 Conflict Error:**
```json
{
  "success": false,
  "error": "EMAIL_EXISTS",
  "message": "An account with this email already exists",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 6. Input Validation

### 6.1 Password Complexity Requirements
- Minimum 8 characters
- At least one uppercase letter (A-Z)
- At least one lowercase letter (a-z)
- At least one number (0-9)
- At least one special character (!@#$%^&*()_+)

### 6.2 Email Validation
- Valid email format (RFC 5322 compliant)
- Maximum length: 255 characters
- Case-insensitive uniqueness check

### 6.3 Input Sanitization
- Trim whitespace from all string inputs
- Prevent SQL injection through parameterized queries
- Validate UTF-8 encoding for text fields

## 7. Security Implementation

### 7.1 Password Security
- Use bcrypt with minimum 12 salt rounds
- Generate unique salt per password
- Store only hashed password, never plaintext
- Implement password history to prevent reuse

### 7.2 Email Verification Token Security
- Generate cryptographically secure random tokens (32 bytes)
- Hash tokens before storage (SHA-256)
- Set 24-hour expiration for verification tokens
- Limit verification attempts per token

### 7.3 Rate Limiting
- 5 registration attempts per minute per IP address
- 3 verification email requests per hour per email
- Exponential backoff for repeated failures

## 8. Error Handling

### 8.1 Registration Errors
- `VALIDATION_ERROR`: Input validation failures
- `EMAIL_EXISTS`: Duplicate email address
- `RATE_LIMIT_EXCEEDED`: Too many registration attempts
- `SERVICE_UNAVAILABLE`: Email service failures

### 8.2 Error Response Format
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "timestamp": "ISO8601 timestamp",
  "request_id": "unique request identifier"
}
```

## 9. Business Logic

### 9.1 Registration Service Requirements
- **UserRegistrationService**: Handle registration workflow
- **PasswordValidationService**: Validate password complexity
- **EmailVerificationService**: Generate and manage verification tokens
- **UserService**: Create and manage user records

### 9.2 Email Verification Logic
1. Generate secure random verification token
2. Hash token for database storage
3. Set 24-hour expiration
4. Send verification email with token link
5. Track verification attempts and status

## 10. Testing Requirements

### 10.1 Unit Tests
- Password complexity validation
- Email format validation
- Token generation and hashing
- Business logic components

### 10.2 Integration Tests
- Registration API endpoint
- Database transaction handling
- Email service integration
- Redis caching functionality

### 10.3 Security Tests
- Password strength validation
- Rate limiting enforcement
- SQL injection prevention
- Token security verification

## 11. Monitoring & Logging

### 11.1 Key Metrics
- Registration success/failure rates
- Email verification completion rates
- Password complexity failure patterns
- Rate limiting activations

### 11.2 Logging Events
- All registration attempts (success/failure)
- Password validation failures
- Email verification token generation
- Rate limiting activations

### 11.3 Log Format
```json
{
  "timestamp": "ISO8601",
  "level": "INFO|WARN|ERROR",
  "event": "USER_REGISTRATION",
  "user_id": "uuid",
  "email": "masked_email",
  "ip_address": "ip",
  "user_agent": "user_agent",
  "success": true,
  "error_code": "ERROR_CODE"
}
```

## 12. Integration Points

### 12.1 Dependencies on Other Features
- **AUTH-04 (Email Verification)**: Complete verification process
- **AUTH-05 (Session Management)**: Post-verification login
- **PROFILE (User Profile Settings)**: User profile management

### 12.2 External Service Integration
- **SMTP Service**: Email delivery for verification
- **Rate Limiting Service**: Request throttling
- **Audit Logging Service**: Security event tracking

### 12.3 Frontend Integration Requirements
- Registration form component in @finpro/ui
- Password strength indicator
- Email verification status display
- Error message handling and display