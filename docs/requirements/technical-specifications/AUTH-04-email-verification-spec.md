# Technical Specification: AUTH-04 - Email Verification

## 1. Feature Overview

### 1.1 Purpose
The Email Verification feature ensures account security by requiring users to verify their email addresses before gaining full access to the FinPro system. This feature prevents account creation with invalid emails and confirms user ownership of email addresses.

### 1.2 Scope
- Email verification token generation and management
- Verification email sending and resending functionality
- Email verification link handling and validation
- Account activation upon successful verification
- Support for both registration and email change verification

### 1.3 Dependencies
- User Registration (AUTH-01) for new account verification
- Email service (SMTP) for verification emails
- User Management for account status updates
- Redis for token caching and rate limiting

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-AUTH-007**: System shall require email verification for new accounts
- **NFR-SEC-006**: System shall require email verification for account activation

### 2.2 Non-Functional Requirements
- **NFR-SEC-001**: All authentication data shall be encrypted in transit using TLS 1.2+
- **NFR-SEC-002**: System shall log all authentication attempts

### 2.3 User Stories
- **US-011**: As a new user, I want to verify my email to activate my account

## 3. Technical Architecture

### 3.1 Email Verification Flow
1. User registers account or changes email
2. System generates secure verification token
3. System sends verification email with token link
4. User clicks verification link
5. System validates token and updates account status
6. System activates account for login access

### 3.2 Resend Verification Flow
1. User requests resend of verification email
2. System validates email and account status
3. System generates new verification token
4. System sends new verification email
5. Previous tokens remain valid until expiration

### 3.3 API Endpoints
- **Verify Email**: `POST /api/v1/auth/verify-email`
- **Resend Verification**: `POST /api/v1/auth/resend-verification`

## 4. Database Schema

### 4.1 Email Verification Tokens

The existing `auth_email_verification_tokens` table is used:
```sql
CREATE TABLE auth_email_verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    verification_type verification_type DEFAULT 'REGISTRATION',
    issued_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMPTZ,
    ip_address INET,
    user_agent TEXT,
    attempt_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE verification_type AS ENUM (
    'REGISTRATION',
    'EMAIL_CHANGE'
);
```

### 4.2 Required Indexes
```sql
CREATE INDEX idx_email_verification_tokens_user_active 
ON auth_email_verification_tokens(user_id, is_verified, expires_at);

CREATE INDEX idx_email_verification_tokens_cleanup 
ON auth_email_verification_tokens(expires_at) WHERE is_verified = false;

CREATE INDEX idx_email_verification_tokens_lookup 
ON auth_email_verification_tokens(token_hash) WHERE is_verified = false;
```

## 5. API Specifications

### 5.1 Verify Email Endpoint

#### POST /api/v1/auth/verify-email

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "token": {
      "type": "string",
      "description": "Email verification token from email link"
    }
  },
  "required": ["token"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "user_id": "uuid",
    "email_verified": true,
    "verified_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Responses:**

**400 Invalid Token:**
```json
{
  "success": false,
  "error": "INVALID_VERIFICATION_TOKEN",
  "message": "Invalid or expired verification token",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**400 Already Verified:**
```json
{
  "success": false,
  "error": "EMAIL_ALREADY_VERIFIED",
  "message": "Email address has already been verified",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 5.2 Resend Verification Endpoint

#### POST /api/v1/auth/resend-verification

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "email": {
      "type": "string",
      "format": "email",
      "description": "Email address to resend verification"
    }
  },
  "required": ["email"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Verification email sent successfully"
}
```

**Rate Limit Error (429):**
```json
{
  "success": false,
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Too many verification emails sent. Please try again later.",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 6. Token Security Implementation

### 6.1 Verification Token Generation
- **Length**: 32 bytes (256 bits)
- **Encoding**: URL-safe Base64
- **Source**: Cryptographically secure random number generator
- **Hashing**: SHA-256 before database storage

### 6.2 Token Expiration
- **Registration Verification**: 24 hours (86400 seconds)
- **Email Change Verification**: 1 hour (3600 seconds)
- **Cleanup**: Automated cleanup of expired tokens

### 6.3 Token Usage Rules
- Multiple active tokens allowed per user
- Tokens remain valid until expiration or verification
- New tokens don't invalidate previous ones
- Rate limiting: 3 verification emails per hour per email

## 7. Email Verification Workflow

### 7.1 Registration Verification
1. User completes registration
2. System creates user with `email_verified = false`
3. System generates verification token
4. System sends welcome email with verification link
5. User clicks link and verifies email
6. System updates `email_verified = true` and `status = 'ACTIVE'`

### 7.2 Email Change Verification
1. User requests email change
2. System generates verification token for new email
3. System sends verification email to new address
4. User verifies new email address
5. System updates user email and sets verified status

### 7.3 Verification Link Format
```
https://app.finpro.com/verify-email?token={{verification_token}}
```

## 8. Input Validation

### 8.1 Token Validation
- Token format validation (Base64 URL-safe)
- Token length validation (44 characters)
- Token expiration check
- Token usage status check

### 8.2 Email Validation
- Valid email format (RFC 5322)
- Maximum length: 255 characters
- Domain validation (MX record check optional)
- Disposable email detection (optional)

### 8.3 Rate Limiting
- 3 verification emails per hour per email address
- 5 verification attempts per token
- IP-based rate limiting for token verification

## 9. Security Implementation

### 9.1 Token Security
- Cryptographically secure token generation
- Hash tokens before database storage
- Constant-time token comparison
- Automatic token cleanup

### 9.2 Verification Security
- Track verification attempts per token
- Log all verification attempts
- Monitor suspicious verification patterns
- Rate limiting on verification requests

### 9.3 Email Security
- Validate email deliverability
- Track bounce rates and failed deliveries
- Implement email reputation monitoring
- Support for SPF, DKIM, and DMARC

## 10. Error Handling

### 10.1 Verification Errors
- `INVALID_VERIFICATION_TOKEN`: Token invalid, expired, or malformed
- `EMAIL_ALREADY_VERIFIED`: Account already verified
- `TOKEN_EXPIRED`: Verification token expired
- `VERIFICATION_FAILED`: General verification failure
- `RATE_LIMIT_EXCEEDED`: Too many verification attempts

### 10.2 Resend Errors
- `EMAIL_NOT_FOUND`: Email not associated with account (silent fail)
- `EMAIL_ALREADY_VERIFIED`: Account already verified
- `RATE_LIMIT_EXCEEDED`: Too many resend requests
- `EMAIL_DELIVERY_FAILED`: SMTP delivery failure

## 11. Business Logic

### 11.1 Email Verification Services
- **EmailVerificationService**: Core verification logic
- **TokenGenerationService**: Secure token creation
- **EmailDeliveryService**: Email sending and tracking
- **UserActivationService**: Account status management

### 11.2 Verification Token Lifecycle
1. Generate cryptographically secure token
2. Hash token for storage (SHA-256)
3. Set expiration based on verification type
4. Send verification email with original token
5. Validate token on verification attempt
6. Mark token as verified on success
7. Update user account status

## 12. Email Templates

### 12.1 Registration Verification Email
```html
Subject: Welcome to FinPro - Verify Your Email

Hello {{first_name}},

Welcome to FinPro! Please verify your email address to activate your account.

Click the link below to verify your email:
{{verification_link}}

This link will expire in 24 hours for security reasons.

If you didn't create this account, please ignore this email.

Best regards,
The FinPro Team
```

### 12.2 Email Change Verification
```html
Subject: Verify Your New Email Address

Hello {{first_name}},

You requested to change your email address to {{new_email}}.

Click the link below to verify your new email address:
{{verification_link}}

This link will expire in 1 hour for security reasons.

If you didn't request this change, please contact support immediately.

Best regards,
The FinPro Team
```

### 12.3 Verification Success Email
```html
Subject: Email Verified Successfully

Hello {{first_name}},

Your email address has been successfully verified!

You can now log in to your FinPro account and access all features.

Best regards,
The FinPro Team
```

## 13. Testing Requirements

### 13.1 Unit Tests
- Token generation and validation
- Email format validation
- Verification logic components
- Token expiration handling

### 13.2 Integration Tests
- Complete verification flow
- Email delivery integration
- Rate limiting enforcement
- Database transaction handling

### 13.3 End-to-End Tests
- Registration to verification flow
- Email change verification flow
- Token expiration scenarios
- Rate limiting scenarios

## 14. Monitoring & Logging

### 14.1 Key Metrics
- Email verification completion rates
- Token generation and usage rates
- Email delivery success rates
- Verification attempt failure patterns

### 14.2 Email Events
- All verification email sends
- Email delivery confirmations
- Verification token generations
- Successful verifications

### 14.3 Security Events
- Failed verification attempts
- Expired token usage attempts
- Rate limiting activations
- Suspicious verification patterns

## 15. Integration Points

### 15.1 Frontend Integration
- Email verification status display
- Resend verification button
- Verification success/error handling
- Account activation confirmation

### 15.2 Email Service Integration
- SMTP configuration and authentication
- Email template management
- Delivery status tracking
- Bounce and complaint handling

### 15.3 Dependencies
- **AUTH-01 (User Registration)**: Account creation triggers
- **AUTH-02 (User Authentication)**: Login prevention for unverified accounts
- **EMAIL (Email Communication)**: Template and delivery service
- **PROFILE (User Profile Settings)**: Email change triggers

### 15.4 Frontend Components Required
- Email verification banner for unverified accounts
- Resend verification email button
- Verification success/error messages
- Account activation confirmation page