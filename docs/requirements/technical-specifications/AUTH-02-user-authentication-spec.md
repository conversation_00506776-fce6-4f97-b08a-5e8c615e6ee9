# Technical Specification: AUTH-02 - User Authentication

## 1. Feature Overview

### 1.1 Purpose
The User Authentication feature provides secure login functionality using email/password credentials with JWT token-based authentication. This feature serves as the primary entry point for verified users to access the FinPro system.

### 1.2 Scope
- User login via email/password authentication
- JWT access and refresh token generation
- Session persistence and management
- Login attempt logging and rate limiting
- Remember me functionality for extended sessions

### 1.3 Dependencies
- User Registration (AUTH-01) for verified user accounts
- Password Management (AUTH-03) for credential validation
- Session Management (AUTH-05) for token handling
- Redis for session caching and rate limiting

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-AUTH-001**: System shall support JWT-based authentication with email/password
- **FR-AUTH-002**: System shall validate user credentials against secure password storage
- **FR-AUTH-003**: System shall redirect unauthenticated users to login page
- **FR-AUTH-004**: System shall support remember me functionality with secure cookies
- **FR-AUTH-007**: System shall require email verification for new accounts

### 2.2 Non-Functional Requirements
- **NFR-SEC-001**: All authentication data shall be encrypted in transit using TLS 1.2+
- **NFR-SEC-002**: System shall log all authentication attempts
- **NFR-SEC-003**: System shall implement rate limiting on login attempts (5 per minute)
- **NFR-PERF-001**: Login process shall complete within 3 seconds
- **NFR-PERF-002**: Token validation shall complete within 100ms

### 2.3 User Stories
- **US-001**: As a Client, I want to securely login so that I can access my financial data
- **US-004**: As an Advisor, I want to see a list of my clients so that I can manage them
- **US-007**: As an Administrator, I want to manage all users in the system

## 3. Technical Architecture

### 3.1 Authentication Flow
1. User submits login credentials (email/password)
2. System validates email format and checks user existence
3. System verifies account is email verified and active
4. System validates password against stored hash
5. System generates JWT access and refresh tokens
6. System updates last login timestamp
7. System returns authentication tokens and user profile

### 3.2 API Endpoints
- **Login**: `POST /api/v1/auth/login`
- **Logout**: `POST /api/v1/auth/logout`
- **Current User**: `GET /api/v1/auth/me`

### 3.3 Token Architecture
- **Access Token**: Short-lived (15 minutes), contains user claims
- **Refresh Token**: Long-lived (7 days), used to generate new access tokens
- **Token Storage**: httpOnly cookies for web, secure storage for mobile

## 4. Database Schema

### 4.1 Login Tracking
No additional tables required - leverages existing:
- `users` table for user lookup and last_login_at updates
- `auth_user_passwords` table for password verification
- `audit_log` table for authentication attempt logging

### 4.2 Required Indexes
```sql
-- Optimize login queries
CREATE INDEX idx_users_email_active ON users(email) WHERE status = 'ACTIVE';
CREATE INDEX idx_users_email_verified ON users(email) WHERE email_verified = true;
CREATE INDEX idx_auth_passwords_user_active ON auth_user_passwords(user_id) WHERE is_deleted = false;
```

## 5. API Specifications

### 5.1 Login Endpoint

#### POST /api/v1/auth/login

**Request Schema:**
```json
{
  "type": "object",
  "properties": {
    "email": {
      "type": "string",
      "format": "email",
      "description": "User email address"
    },
    "password": {
      "type": "string",
      "description": "User password"
    },
    "remember_me": {
      "type": "boolean",
      "default": false,
      "description": "Extended session duration"
    }
  },
  "required": ["email", "password"]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "JWT_ACCESS_TOKEN",
    "refresh_token": "JWT_REFRESH_TOKEN",
    "token_type": "bearer",
    "expires_in": 900,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "CLIENT",
      "permissions": ["read:own_data", "write:own_data"]
    }
  }
}
```

**Error Responses:**

**400 Invalid Credentials:**
```json
{
  "success": false,
  "error": "INVALID_CREDENTIALS",
  "message": "Invalid email or password",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**403 Account Not Verified:**
```json
{
  "success": false,
  "error": "ACCOUNT_NOT_VERIFIED",
  "message": "Please verify your email address before logging in",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 5.2 Current User Endpoint

#### GET /api/v1/auth/me

**Success Response (200):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "username": "johndoe",
  "first_name": "John",
  "last_name": "Doe",
  "role": "CLIENT",
  "status": "ACTIVE",
  "email_verified": true,
  "last_login_at": "2024-01-01T00:00:00Z",
  "permissions": ["read:own_data", "write:own_data"]
}
```

### 5.3 Logout Endpoint

#### POST /api/v1/auth/logout

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

## 6. JWT Token Specifications

### 6.1 Access Token Claims
```json
{
  "sub": "user_uuid",
  "email": "<EMAIL>",
  "role": "CLIENT",
  "permissions": ["read:own_data", "write:own_data"],
  "iat": **********,
  "exp": **********,
  "iss": "finpro-api",
  "aud": "finpro-web"
}
```

### 6.2 Refresh Token Claims
```json
{
  "sub": "user_uuid",
  "type": "refresh",
  "iat": **********,
  "exp": **********,
  "iss": "finpro-api",
  "jti": "unique_token_id"
}
```

### 6.3 Token Security
- Algorithm: HS256 (HMAC with SHA-256)
- Secret Key: Environment variable (minimum 256 bits)
- Token Blacklisting: Redis-based for logout/password change

## 7. Input Validation

### 7.1 Login Validation
- Email format validation (RFC 5322)
- Password presence check (no complexity validation on login)
- Rate limiting per IP address
- Brute force protection

### 7.2 Security Validations
- Account status verification (ACTIVE)
- Email verification status check
- Password expiration check (if applicable)
- Account lockout check

## 8. Security Implementation

### 8.1 Authentication Security
- Constant-time password comparison (bcrypt.checkpw)
- Failed login attempt tracking
- Account lockout after 5 failed attempts (15-minute lockout)
- Rate limiting: 5 login attempts per minute per IP

### 8.2 Token Security
- Secure JWT signing with HMAC-SHA256
- Short access token lifespan (15 minutes)
- Refresh token rotation on use
- httpOnly, Secure, SameSite cookie attributes

### 8.3 Session Security
- Server-side session validation
- Token blacklisting on logout
- Automatic cleanup of expired tokens

## 9. Error Handling

### 9.1 Authentication Errors
- `INVALID_CREDENTIALS`: Wrong email/password (generic message)
- `ACCOUNT_NOT_VERIFIED`: Email not verified
- `ACCOUNT_SUSPENDED`: Account disabled
- `ACCOUNT_LOCKED`: Too many failed attempts
- `RATE_LIMIT_EXCEEDED`: Too many login attempts

### 9.2 Error Security
- Generic error messages to prevent user enumeration
- Detailed logging for security monitoring
- No information leakage about account existence

## 10. Business Logic

### 10.1 Authentication Service Requirements
- **AuthenticationService**: Core login logic
- **TokenService**: JWT generation and validation
- **SessionService**: Session management and tracking
- **SecurityService**: Rate limiting and brute force protection

### 10.2 Login Workflow
1. Validate input format and rate limits
2. Lookup user by email (case-insensitive)
3. Verify account status and email verification
4. Check password against stored hash
5. Update last login timestamp
6. Generate JWT tokens
7. Create session record
8. Return authentication response

## 11. Testing Requirements

### 11.1 Unit Tests
- Password verification logic
- JWT token generation and validation
- Rate limiting functionality
- Authentication service methods

### 11.2 Integration Tests
- Login API endpoint flows
- Token refresh mechanisms
- Session management
- Database transaction handling

### 11.3 Security Tests
- Brute force attack prevention
- Token manipulation attempts
- Session hijacking prevention
- Rate limiting enforcement

## 12. Monitoring & Logging

### 12.1 Key Metrics
- Login success/failure rates
- Failed login attempt patterns
- Token refresh frequency
- Session duration analytics

### 12.2 Security Events
- All login attempts (success/failure)
- Account lockout events
- Rate limiting activations
- Suspicious login patterns

### 12.3 Performance Metrics
- Login response times
- Token validation latency
- Database query performance
- Cache hit rates

## 13. Integration Points

### 13.1 Frontend Integration
- Login form component (@finpro/ui)
- Token storage and refresh handling
- Authentication state management
- Protected route implementation

### 13.2 API Integration
- Authentication middleware for all protected endpoints
- Role-based access control enforcement
- Token validation on each request
- Automatic token refresh handling

### 13.3 Dependencies
- **AUTH-01 (User Registration)**: Verified user accounts
- **AUTH-03 (Password Management)**: Password validation
- **AUTH-05 (Session Management)**: Token lifecycle
- **AUTH-06 (Role-Based Authorization)**: Permission enforcement