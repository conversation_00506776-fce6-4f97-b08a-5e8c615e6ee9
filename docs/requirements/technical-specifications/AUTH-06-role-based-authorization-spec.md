# Technical Specification: AUTH-06 - Role-Based Authorization

## 1. Feature Overview

### 1.1 Purpose
The Role-Based Authorization feature implements comprehensive access control throughout the FinPro system, ensuring users can only access data and functionality appropriate to their role and permissions. This feature provides the foundation for secure multi-tenant financial data management.

### 1.2 Scope
- Role-based access control (RBAC) for all API endpoints
- Permission validation middleware
- Frontend authorization controls
- Dynamic permission checking based on user context
- Resource-level access control for financial data

### 1.3 Dependencies
- User Authentication (AUTH-02) for authenticated user context
- Session Management (AUTH-05) for token validation
- User Management for role assignment
- Database for permission and role definitions

## 2. Feature Requirements

### 2.1 Functional Requirements
- **FR-AUTHZ-001**: System shall enforce role-based permissions on all API endpoints
- **FR-AUTHZ-002**: System shall validate user role on each request
- **FR-AUTHZ-003**: System shall deny access with 403 status for unauthorized requests
- **FR-AUTHZ-004**: Frontend shall hide UI elements based on user permissions

### 2.2 User Role Definitions
- **Client**: Individual users managing personal financial data
- **Advisor**: Financial advisors managing multiple client accounts
- **Administrator**: System administrators with full access

### 2.3 User Stories
- Clients can only access their own financial data
- Advisors can access assigned client data
- Administrators have system-wide access
- Unauthorized access attempts are denied with clear error messages

## 3. Technical Architecture

### 3.1 Permission Model
- **Role-Based**: Primary roles define base permissions
- **Resource-Based**: Specific resource access controls
- **Contextual**: Permissions validated against request context
- **Hierarchical**: Administrator > Advisor > Client permission hierarchy

### 3.2 Authorization Flow
1. Extract JWT token from request
2. Validate token and extract user claims
3. Determine required permissions for endpoint
4. Check user role and specific permissions
5. Validate resource ownership/access rights
6. Allow or deny request with appropriate response

### 3.3 Middleware Architecture
```
Request → JWT Validation → Role Extraction → Permission Check → Endpoint
                                                      ↓
                                                403 Forbidden
```

## 4. Role and Permission System

### 4.1 Role Definitions

#### Client Role
**Permissions:**
- `read:own_profile` - View own user profile
- `write:own_profile` - Edit own user profile
- `read:own_financial_data` - View own financial assets, liabilities, income, expenses
- `write:own_financial_data` - Manage own financial data
- `read:own_reports` - View personal financial reports
- `delete:own_account` - Delete own account

#### Advisor Role
**Permissions:**
- `read:own_profile` - View own user profile
- `write:own_profile` - Edit own user profile
- `read:client_list` - View assigned client list
- `read:client_financial_data` - View client financial data
- `write:client_financial_data` - Manage client financial data
- `read:client_reports` - View client financial reports
- `invite:clients` - Send client invitations
- `read:aggregated_data` - View aggregated client data

#### Administrator Role
**Permissions:**
- `read:all_users` - View all users in system
- `write:all_users` - Manage all users
- `read:all_financial_data` - View all financial data
- `write:all_financial_data` - Manage all financial data
- `read:system_reports` - View system-wide reports
- `write:system_config` - Configure system settings
- `read:audit_logs` - View audit logs
- `manage:organizations` - Manage organizations

### 4.2 Permission Hierarchy
```
ADMINISTRATOR
├── All Advisor permissions
├── All Client permissions
├── System management permissions

ADVISOR
├── All Client permissions (for assigned clients)
├── Client management permissions

CLIENT
├── Own data permissions only
```

## 5. Database Schema

### 5.1 Permission Storage
Permissions are encoded in JWT tokens and validated against role definitions. No additional database tables required beyond existing user role storage.

### 5.2 Role Validation Queries
```sql
-- Validate user role and status
SELECT role, status, email_verified 
FROM users 
WHERE id = ? AND status = 'ACTIVE' AND email_verified = true;

-- Check client-advisor relationship
SELECT ha.household_id 
FROM household_advisors ha
JOIN team_advisors ta ON ha.team_advisor_id = ta.id
WHERE ta.advisor_user_id = ? AND ha.household_id = ?;

-- Check household membership
SELECT hm.household_id 
FROM household_members hm
WHERE hm.user_id = ? AND hm.household_id = ? AND hm.status = 'ACTIVE';
```

## 6. Authorization Middleware

### 6.1 Permission Decorator
```python
@require_permissions("read:client_financial_data")
@require_role("ADVISOR", "ADMINISTRATOR")
def get_client_assets(user_id: str, client_id: str):
    # Endpoint implementation
    pass
```

### 6.2 Resource Access Validation
```python
def validate_resource_access(user: User, resource_id: str, permission: str) -> bool:
    if user.role == "ADMINISTRATOR":
        return True
    
    if user.role == "ADVISOR":
        return validate_advisor_client_access(user.id, resource_id)
    
    if user.role == "CLIENT":
        return validate_client_ownership(user.id, resource_id)
    
    return False
```

### 6.3 Middleware Implementation
```python
class AuthorizationMiddleware:
    def process_request(self, request, endpoint_permissions):
        # 1. Extract user from validated JWT token
        # 2. Check user has required role
        # 3. Validate specific permissions
        # 4. Check resource-level access
        # 5. Allow request or return 403
```

## 7. API Authorization Patterns

### 7.1 Endpoint Protection Examples

#### Client Data Access
```python
# GET /api/v1/assets/{asset_id}
@require_permissions("read:own_financial_data", "read:client_financial_data")
def get_asset(asset_id: str, current_user: User):
    if current_user.role == "CLIENT":
        # Validate asset belongs to user's household
        validate_asset_ownership(current_user.id, asset_id)
    elif current_user.role == "ADVISOR":
        # Validate advisor has access to asset's household
        validate_advisor_access(current_user.id, asset_id)
```

#### User Management
```python
# GET /api/v1/users
@require_role("ADMINISTRATOR")
@require_permissions("read:all_users")
def list_users(current_user: User):
    # Only administrators can list all users
    pass
```

### 7.2 Resource Ownership Validation
- **Assets/Liabilities**: Validate through portfolio → household → household_member
- **Households**: Validate through household_member relationship
- **Users**: Validate user can only access own profile (unless admin/advisor)

## 8. Frontend Authorization

### 8.1 UI Component Protection
```typescript
// React component with permission check
function AssetManagement() {
  const { hasPermission } = useAuth();
  
  if (!hasPermission('write:own_financial_data')) {
    return <AccessDenied />;
  }
  
  return <AssetForm />;
}
```

### 8.2 Menu/Navigation Control
```typescript
const NavigationItems = {
  dashboard: { permission: 'read:own_financial_data', roles: ['CLIENT', 'ADVISOR', 'ADMINISTRATOR'] },
  clients: { permission: 'read:client_list', roles: ['ADVISOR', 'ADMINISTRATOR'] },
  admin: { permission: 'read:all_users', roles: ['ADMINISTRATOR'] }
};
```

### 8.3 Dynamic UI Elements
- Hide/show buttons based on permissions
- Disable form fields for read-only access
- Show different views based on user role
- Display appropriate error messages for unauthorized access

## 9. Permission Validation Logic

### 9.1 Client Access Validation
```python
def validate_client_access(user_id: str, resource_id: str, resource_type: str) -> bool:
    # Get user's households
    households = get_user_households(user_id)
    
    # Get resource's household
    resource_household = get_resource_household(resource_id, resource_type)
    
    return resource_household in households
```

### 9.2 Advisor Access Validation
```python
def validate_advisor_access(advisor_id: str, resource_id: str, resource_type: str) -> bool:
    # Get advisor's assigned households
    assigned_households = get_advisor_households(advisor_id)
    
    # Get resource's household
    resource_household = get_resource_household(resource_id, resource_type)
    
    return resource_household in assigned_households
```

### 9.3 Administrator Access
Administrators have full access to all resources and data, with audit logging for all actions.

## 10. Security Implementation

### 10.1 Authorization Security
- **Principle of Least Privilege**: Users granted minimum required permissions
- **Defense in Depth**: Multiple validation layers (token, role, resource)
- **Fail-Safe Defaults**: Default deny for unauthorized access
- **Audit Logging**: Log all authorization decisions

### 10.2 Token-Based Permissions
- Permissions embedded in JWT tokens
- Token validation on every request
- Permission caching for performance
- Token invalidation on role changes

### 10.3 Resource-Level Security
- Validate ownership/access for every resource
- Check permissions at data access layer
- Prevent privilege escalation attacks
- Audit all data access attempts

## 11. Error Handling

### 11.1 Authorization Errors
- `INSUFFICIENT_PERMISSIONS`: User lacks required permission
- `FORBIDDEN_RESOURCE`: User cannot access specific resource
- `INVALID_ROLE`: User role is invalid or inactive
- `ACCESS_DENIED`: Generic access denial

### 11.2 Error Response Format
```json
{
  "success": false,
  "error": "FORBIDDEN",
  "message": "Insufficient permissions to access this resource",
  "required_permissions": ["read:client_financial_data"],
  "user_permissions": ["read:own_financial_data"],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 11.3 Error Security
- Don't reveal information about resource existence
- Provide generic error messages to prevent enumeration
- Log detailed information for security monitoring
- Return consistent 403 status for all authorization failures

## 12. Performance Optimization

### 12.1 Permission Caching
- Cache user permissions in Redis (5-minute TTL)
- Cache resource ownership relationships
- Invalidate cache on role/permission changes
- Background refresh for frequently accessed permissions

### 12.2 Query Optimization
- Index on role-based query patterns
- Optimize household membership queries
- Use prepared statements for permission checks
- Implement connection pooling for permission queries

### 12.3 Authorization Middleware Performance
- Fast-path for administrator role
- Short-circuit evaluation for permission checks
- Batch permission validation where possible
- Async permission validation for non-critical paths

## 13. Testing Requirements

### 13.1 Unit Tests
- Permission validation logic
- Role-based access controls
- Resource ownership checks
- Authorization middleware

### 13.2 Integration Tests
- End-to-end authorization flows
- Cross-role access validation
- Permission boundary testing
- Error response validation

### 13.3 Security Tests
- Privilege escalation attempts
- Resource enumeration attacks
- Token manipulation testing
- Role boundary violations

## 14. Monitoring & Logging

### 14.1 Authorization Metrics
- Permission check success/failure rates
- Authorization latency by endpoint
- Role distribution across requests
- Access denied patterns

### 14.2 Security Events
- All authorization failures
- Role-based access patterns
- Suspicious access attempts
- Permission escalation attempts

### 14.3 Audit Logging
```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "event": "AUTHORIZATION_CHECK",
  "user_id": "uuid",
  "role": "CLIENT",
  "permissions": ["read:own_financial_data"],
  "resource_type": "asset",
  "resource_id": "uuid",
  "action": "read",
  "result": "ALLOWED|DENIED",
  "reason": "permission_check_result"
}
```

## 15. Integration Points

### 15.1 Frontend Integration
- Authentication context provider
- Permission-based component rendering
- Role-based navigation
- Authorization error handling

### 15.2 API Integration
- Authorization middleware on all protected endpoints
- Permission validation in business logic
- Resource access validation
- Consistent error responses

### 15.3 Dependencies
- **AUTH-02 (User Authentication)**: User context and role information
- **AUTH-05 (Session Management)**: Token validation and claims extraction
- **User Management**: Role assignment and updates
- **Audit Logging**: Security event tracking

### 15.4 Cross-Feature Authorization
- **Assets/Liabilities**: Household-based access control
- **Dashboard/Reports**: Data filtering based on permissions
- **User Management**: Role-based user administration
- **Organizations**: Multi-tenant access control