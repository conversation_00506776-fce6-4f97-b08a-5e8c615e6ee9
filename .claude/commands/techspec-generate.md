# Generate Technical Specification

Generate technical specifications by breaking down a product requirements document into individual features: $ARGUMENTS

## Usage Examples:
- `/techspec-generate 01-authentication-authorization.md`
- `/techspec-generate 02-client-intake-onboarding.md`
- `/techspec-generate 03-assets-management.md`

## Approach:
This command analyzes a product requirements document and breaks it down into discrete features, generating a separate technical specification for each feature. This provides more granular, focused specifications that are easier to implement and maintain.

## Process:

### Phase 1: Validation & Setup
1. Parse arguments to get:
   - Product requirements file name (e.g., "01-authentication-authorization.md")
2. Validate that the product requirements file exists in `docs/requirements/product-requirements/`
3. Auto-derive tech spec prefix from filename:
   - Extract module name from filename after number prefix
   - Map to appropriate tech spec prefix using module mapping table
   - Examples: "authentication-authorization" → "AUTH", "client-intake-onboarding" → "INTAKE"
4. Check existing technical specifications in `docs/requirements/technical-specifications/` to determine next spec ID
5. Generate spec ID format: `{PREFIX}-{##}` (e.g., AUTH-01, AUTH-02, etc.)

### Phase 2: Requirements Analysis & Feature Identification
6. Read and analyze the product requirements document:
   - Extract title and module name
   - Identify functional requirements (FR-*)
   - Identify non-functional requirements (NFR-*)
   - Extract user stories (US-*)
   - Parse API endpoints if defined
   - Identify key business logic and data flows
7. **Break down requirements into discrete features**:
   - Group related functional requirements by feature
   - Identify feature boundaries based on user stories
   - Map API endpoints to specific features
   - Separate core features from supporting features
   - Create feature dependency graph
8. Read and analyze the domain model at `docs/technical/domain-model/domain_model.mmd`:
   - Identify relevant entities and relationships for each feature
   - Extract table definitions and constraints
   - Map entities to specific features
   - Identify foreign key relationships
   - Note existing enum types and data structures
9. Review the OpenAPI specification at `docs/technical/api-spec/openapi-spec.yaml`:
   - Identify existing API patterns and conventions
   - Extract authentication and security requirements
   - Review error response schemas and status codes
   - Identify common request/response patterns
   - Note pagination, validation, and filtering patterns
   - Check for existing endpoints that may need extension
10. **Create feature specification plan**:
    - List all identified features with descriptions
    - Assign priority and implementation order
    - Map requirements to features
    - Identify shared components and dependencies

### Phase 3: Feature-Based Technical Specification Generation
11. **For each identified feature, generate a focused technical specification**:
   - **Section 1**: Feature Overview (Purpose, Scope, Dependencies)
   - **Section 2**: Feature Requirements (specific FR-*, NFR-*, US-* for this feature)
   - **Section 3**: Technical Architecture (feature-specific components, data flow)
   - **Section 4**: Database Schema (feature-specific tables, relationships, migrations)
   - **Section 5**: API Specifications (feature endpoints following OpenAPI patterns)
   - **Section 6**: UI/UX Specifications (screens, modals, components, user flows)
   - **Section 7**: Input Validation (feature-specific validation rules)
   - **Section 8**: Security Implementation (feature-specific security requirements)
   - **Section 9**: Error Handling (feature-specific error types and responses)
   - **Section 10**: Business Logic (feature-specific service requirements)
   - **Section 11**: Testing Requirements (feature-focused test scenarios)
   - **Section 12**: Monitoring & Logging (feature-specific metrics and events)
   - **Section 13**: Integration Points (dependencies on other features/services)

### Phase 4: Feature-Specific Customization
12. Customize each feature specification based on feature type and module prefix:
   - **AUTH**: Authentication/authorization patterns, JWT handling, password security, login/signup forms
   - **INTAKE**: Form validation, multi-step workflows, progress tracking, wizard components
   - **ASSET**: Financial calculations, data aggregation, reporting, portfolio dashboards
   - **LIABILITY**: Payment scheduling, interest calculations, reminder systems, debt management UI
   - **DASHBOARD**: Analytics, data visualization, report generation, chart components
   - **ADVISOR**: Multi-client management, permissions, data access patterns, client management UI
   - **ADMIN**: System administration, user management, global settings, admin panels

### Phase 5: Cross-Feature Integration Specifications
13. Define integration requirements between features:
    - Feature dependency management and shared components
    - Database schema integration and cross-feature relationships
    - API contract compatibility between features
    - Shared authentication and authorization requirements
    - Common validation and error handling patterns
    - Shared UI components and design system integration
    - Frontend routing and navigation between features
    - Integration testing scenarios across features

### Phase 6: Quality Assurance
14. Generate testing specifications for each feature:
    - Feature-specific unit test requirements and scenarios
    - Integration test specifications for feature API endpoints
    - Frontend component and user interaction testing
    - Security test cases for feature-specific auth and data protection
    - Validation test cases for feature edge cases and error conditions
    - Cross-feature integration testing requirements

## Output File Structure:
Multiple technical specifications will be generated, one for each identified feature:
`docs/requirements/technical-specifications/{SPEC_ID}-{feature-name}-spec.md`

Examples from Authentication & Authorization module:
- `AUTH-01-user-registration-spec.md`
- `AUTH-02-user-authentication-spec.md`
- `AUTH-03-password-management-spec.md`
- `AUTH-04-email-verification-spec.md`
- `AUTH-05-session-management-spec.md`
- `AUTH-06-role-based-authorization-spec.md`

Examples from Client Intake & Onboarding module:
- `INTAKE-01-client-registration-spec.md`
- `INTAKE-02-financial-profile-setup-spec.md`
- `INTAKE-03-document-upload-spec.md`
- `INTAKE-04-advisor-assignment-spec.md`

## Template Variables:
Each feature specification will automatically populate these variables:
- `{feature_title}`: Specific feature name and description
- `{spec_id}`: Auto-generated spec ID (PREFIX-##)
- `{feature_purpose}`: Specific purpose and scope of this feature
- `{feature_requirements}`: FR-*, NFR-*, US-* requirements specific to this feature
- `{feature_endpoints}`: API endpoints specific to this feature
- `{feature_database_schema}`: Database tables and relationships for this feature
- `{feature_validation_rules}`: Validation requirements specific to this feature
- `{feature_security_requirements}`: Security requirements for this feature
- `{feature_dependencies}`: Dependencies on other features or external services
- `{integration_points}`: How this feature integrates with other system components
- `{ui_components}`: Required screens, modals, forms, and interactive components
- `{user_flows}`: Step-by-step user interaction flows and navigation paths

## Filename to Prefix Mapping:
Auto-derive tech spec prefix from product requirements filename:

| Filename Pattern | Tech Spec Prefix | Module Description |
|------------------|-----------------|-------------------|
| `*authentication-authorization*` | **AUTH** | JWT, bcrypt, session management |
| `*client-intake-onboarding*` | **INTAKE** | Form validation, progress tracking |
| `*assets-management*` | **ASSET** | Portfolio tracking, net worth calculations |
| `*liabilities-management*` | **LIABILITY** | Debt tracking, payment scheduling |
| `*income-management*` | **INCOME** | Income tracking, tax calculations |
| `*expenses-management*` | **EXPENSE** | Budget tracking, categorization |
| `*insurance-management*` | **INSURANCE** | Policy tracking, coverage analysis |
| `*dashboard-reporting*` | **DASHBOARD** | Analytics, visualizations, exports |
| `*paystub-ocr*` | **OCR** | Document processing, data extraction |
| `*advisor-portal*` | **ADVISOR** | Multi-client management, permissions |
| `*saas-administrator-portal*` | **ADMIN** | System management, user administration |
| `*email-communication*` | **EMAIL** | Template management, delivery tracking |
| `*data-import-export*` | **IMPORT** | File processing, format conversion |
| `*user-profile-settings*` | **PROFILE** | Preferences, account management |
| `*org-administrator-portal*` | **ORG** | Multi-tenant administration |

**Fallback Logic:**
- If no pattern matches, extract first significant word and convert to uppercase
- Example: `16-custom-feature.md` → `CUSTOM`

## Success Criteria:
- Each feature has a focused, implementable technical specification
- All functional requirements are mapped to specific features
- Feature specifications are cohesive and have clear boundaries
- Database schema references domain model entities and maintains consistency
- Database changes include proper Alembic migrations for existing schema
- API specifications follow established patterns with consistent schemas and error handling
- Feature dependencies and integration points are clearly documented
- Security requirements are translated to feature-specific implementation details
- UI/UX specifications include all required screens, modals, and user flows
- Frontend components align with the @finpro/ui design system
- Testing requirements are comprehensive and feature-focused
- Each specification can be implemented independently where possible
- Cross-feature integration requirements are clearly documented

## Error Handling:
- If product requirements file not found: suggest available files from the directory
- If filename pattern doesn't match mapping table: use fallback logic to derive prefix
- If feature specifications already exist: ask for confirmation to overwrite
- If missing required sections in product requirements: generate with placeholders
- If unable to identify discrete features: create single comprehensive specification

## Post-Generation Actions:
1. Display summary of all generated feature specifications
2. Show feature dependency graph and implementation order recommendations
3. Highlight shared components and integration requirements
4. Provide implementation roadmap with feature priorities
5. Generate UI component library requirements for @finpro/ui
6. Suggest next steps for development team (backend, frontend, design)
7. Generate cross-feature integration checklist