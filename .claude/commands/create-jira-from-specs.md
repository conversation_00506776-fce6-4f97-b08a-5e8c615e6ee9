# Create Jira Tickets from Technical Specifications

## Description
Converts technical specifications in the `/docs/requirements/technical-specifications/` folder into structured Jira tickets using MCP Atlassian tools. Uses the spec prefix (e.g., "AUTH") as the Epic name, creating it if it doesn't exist in Jira.

## Jira Configuration
- **Board URL**: https://finproholdings.atlassian.net/jira/software/projects/SCRUM/boards/1/backlog?epics=visible
- **Cloud ID**: 6fceec85-be38-4391-ae7c-236b94e0f448
- **Project Key**: SCRUM (default)
- **Selected Epic**: SCRUM-36 (if applicable)

## Usage
```bash
# Convert all technical specifications to Jira tickets
/create-jira-from-specs

# Convert specific specification file
/create-jira-from-specs AUTH-01-user-registration-spec.md

# Convert pattern of files (e.g., all AUTH specs)
/create-jira-from-specs AUTH-*

# Preview mode (analyze without creating)
/create-jira-from-specs --preview

# Specify target project (default: SCRUM)
/create-jira-from-specs --project SCRUM
```

## How It Works

### 1. Specification Analysis
- Reads technical specification files using LLM analysis
- Extracts spec prefix (AUTH, PROFILE, etc.) from filename
- Identifies key implementation areas (API, Database, Security, Testing)
- Generates story breakdown with appropriate tasks

### 2. Epic Management
- Checks if Epic exists for spec prefix using JQL search
- Creates Epic if it doesn't exist: "{PREFIX} System Implementation"
- Groups all related specifications under same Epic

### 3. Ticket Creation Hierarchy
- **Epic**: Groups all features by prefix (AUTH, PROFILE, etc.)
- **Story**: One per technical specification (AUTH-01: User Registration)
- **Tasks**: Implementation items broken down from Story

### 4. MCP Integration
Uses Atlassian MCP tools for all Jira operations:
- `mcp__atlassian__getAccessibleAtlassianResources` - Get cloud ID
- `mcp__atlassian__getVisibleJiraProjects` - Validate project access
- `mcp__atlassian__searchJiraIssuesUsingJql` - Check existing Epics
- `mcp__atlassian__createJiraIssue` - Create all ticket types

## Ticket Structure

### Epic Level
```
Summary: AUTH System Implementation  
Type: Epic
Description: Complete authentication and authorization system with user management, security controls, and access management features.
```

### Story Level
```
Summary: AUTH-01: User Registration
Type: Story
Epic Link: AUTH System Implementation
Description: [Extracted from spec purpose/overview]
Story Points: [Estimated based on complexity]
Acceptance Criteria:
- All functional requirements implemented
- Unit tests with >90% coverage
- Integration tests passing
- Security requirements validated
- API documentation complete
```

### Task Level
```
Summary: Create user registration API endpoint
Type: Task
Parent: AUTH-01: User Registration  
Story Points: 5
Description: Implement POST /api/v1/auth/register with proper validation and error handling
Acceptance Criteria:
- API endpoint created with correct schema
- Input validation implemented
- Rate limiting configured
- Error responses standardized
```

## Implementation Features

### Smart Analysis
- LLM analyzes specification content to identify implementation areas
- Generates appropriate task breakdown based on spec complexity
- Estimates story points based on technical requirements
- Creates meaningful acceptance criteria from functional requirements

### Dependency Awareness
- Reads dependency matrix to understand relationships
- Sets up proper ticket linking for blocking dependencies
- Considers implementation order in task creation

### Best Practices Integration
- Follows Jira best practices from research
- Uses imperative titles for actionability
- Includes technical context and background
- Provides clear, measurable acceptance criteria
- Appropriate story point estimation