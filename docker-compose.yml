version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:14-alpine
    container_name: finpro-db
    environment:
      POSTGRES_USER: finpro
      POSTGRES_PASSWORD: password
      POSTGRES_DB: finpro_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finpro -d finpro_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  api:
    build:
      context: ./apps/api
      dockerfile: Dockerfile
    container_name: finpro-api
    environment:
      POSTGRES_SERVER: db
      POSTGRES_USER: finpro
      POSTGRES_PASSWORD: password
      POSTGRES_DB: finpro_db
      SECRET_KEY: "your-secret-key-here"
      BACKEND_CORS_ORIGINS: '["http://localhost:3000","http://localhost:8000"]'
      PROJECT_NAME: "FinPro API"
      VERSION: "1.0.0"
      API_V1_STR: "/api/v1"
      ALGORITHM: "HS256"
      ACCESS_TOKEN_EXPIRE_MINUTES: 10080
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./apps/api:/app

  # Next.js Frontend
  web:
    build:
      context: .
      dockerfile: ./apps/web/Dockerfile
    container_name: finpro-web
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_APP_URL: http://localhost:3000
      JWT_SECRET: "your-super-secret-jwt-key-change-this-in-production"
      WATCHPACK_POLLING: "true"
      CHOKIDAR_USEPOLLING: "true"
    ports:
      - "3000:3000"
    depends_on:
      - api
    volumes:
      - .:/workspace
      - /workspace/node_modules
      - /workspace/apps/web/node_modules
      - web_next_cache:/workspace/apps/web/.next
    working_dir: /workspace/apps/web
    command: npm run dev

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: finpro-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
  web_next_cache: